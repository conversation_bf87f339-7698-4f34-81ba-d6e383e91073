<?php

namespace App\Listeners;

use App\Events\SignedLoan;
use Modules\Sales\Services\SaleService;

class DeactivateSaleTasks
{
    protected SaleService $saleService;

    /**
     * Create the event listener.
     *
     * @param SaleService $saleService
     */
    public function __construct(SaleService $saleService)
    {
        $this->saleService = $saleService;
    }

    /**
     * Handle the event.
     *
     * @param  SignedLoan  $event
     * @return void
     */
    public function handle(SignedLoan $event)
    {
        $saleTasks = $this->saleService->getUndoneTasksByLoan($event->loan);

        foreach ($saleTasks as $saleTask) {
            $this->saleService->systemClose($saleTask);
        }
    }
}
