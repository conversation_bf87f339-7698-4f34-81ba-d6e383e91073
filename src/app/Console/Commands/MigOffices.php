<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\City;
use Modules\Common\Models\Office;
use Modules\Common\Models\SiteMapOffice;

class MigOffices extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mig:offices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate offices';

    public function handle(): int
    {
        $allOfficeData = collect([]);
        $offices = $this->getOffices();
        $officeTranslations = $this->getOfficeTranslations();

        $offices->each(function ($row, int $officeId) use ($officeTranslations, $allOfficeData) {
            $officeTranslation = $officeTranslations->get($officeId);

            $allOfficeData->put($officeId, array_merge($row, $officeTranslation));
        });

        $allOfficeData->each(function ($row, int $officeId) {
            $city = City::whereCityId($row['city_id'])->first();
            if (!$city) {
                echo "Error city with id: {$row['city_id']} ({$row['title']}) not found.";
            }

            $data = [];
            $data = [
                'slug' => $row['slug'],
                'city_id' => $row['city_id'],
                'name' => $row['title'],
                'phone' => $row['phone'],
                'working_time' => str_replace(['\r\n', "\n", "\r", "<p>&nbsp;</p>"], '', $row['working_time']),
                'description' => $row['description'],
                'address' => $row['address'],
                'lat' => $row['lat'],
                'lng' => $row['long'],
            ];
            SiteMapOffice::create($data);
        });

        echo "OK" . "\n";


        return Command::SUCCESS;
    }

    private function getOfficeTranslations(): Collection
    {
        $officeTranslations = file_get_contents(database_path('/offices_translations.json'));
        $officeTranslations = json_decode($officeTranslations, true);

        return collect($officeTranslations[2]['data'])->keyBy('offices_id');
    }

    private function getOffices(): Collection
    {
        $offices = file_get_contents(database_path('/offices.json'));
        $offices = json_decode($offices, true);

        return collect($offices[2]['data'])->keyBy('id');
    }
}
