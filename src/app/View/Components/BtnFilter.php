<?php

namespace App\View\Components;

use Illuminate\View\Component;

class BtnFilter extends Component
{
    public ?string $clearFilterUrl;

    public function __construct(?string $clearFilterUrl = null)
    {
        $this->clearFilterUrl = $clearFilterUrl;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.btn-filter');
    }
}
