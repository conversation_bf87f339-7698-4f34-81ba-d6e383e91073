<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Modules\Common\Models\BaseModel;

class Timestamps extends Component
{
    public BaseModel $model;
    public string $styles;

    /**
     * Create a new component instance.
     *
     * @param BaseModel $model
     */
    public function __construct(BaseModel $model, string $styles = '')
    {
        $this->model = $model;
        $this->styles = $styles;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.timestamps');
    }
}
