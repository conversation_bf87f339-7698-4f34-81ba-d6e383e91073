<?php

namespace Modules\CashDesk\Services;

use Modules\CashDesk\Models\CashOperationalTransactionStatsDaily;
use Modules\CashDesk\Repositories\CashOperationalTransactionStatsDailyRepository;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Services\BaseService;

class CashOperationalTransactionStatsDailyService extends BaseService
{
    private CashOperationalTransactionStatsDailyRepository $cashOperationalTransactionStatsDailyRepository;

    /**
     * CashOperationalTransactionService constructor.
     * @param CashOperationalTransactionStatsDailyRepository $cashOperationalTransactionStatsDailyRepository
     */
    public function __construct(
        CashOperationalTransactionStatsDailyRepository $cashOperationalTransactionStatsDailyRepository
    ) {
        $this->cashOperationalTransactionStatsDailyRepository = $cashOperationalTransactionStatsDailyRepository;

        parent::__construct();
    }

    /**
     * @throws ProblemException
     */
    public function getById(int $id): CashOperationalTransactionStatsDaily
    {
        if (!$cashOperational = $this->cashOperationalTransactionStatsDailyRepository->getById($id)) {
            throw new ProblemException(__('cashdesk::cashDesk.WrongID'));
        }

        return $cashOperational;
    }

    /**
     * @param array $data
     * @param int $administratorId
     * @return CashOperationalTransactionStatsDaily
     */
    public function store(array $data, int $administratorId): CashOperationalTransactionStatsDaily
    {
        $data['accounting_code_id'] = $administratorId;

        return $this->cashOperationalTransactionStatsDailyRepository->createOrUpdate($data);
    }

    /**
     * @param array $data
     * @return bool
     */
    public function noDailyReport(array $data): bool
    {
        return $this->cashOperationalTransactionStatsDailyRepository->previousBalanceExist($data);
    }
}
