<?php

namespace Modules\CashDesk\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class TerminalLogIndexRequest extends BaseRequest
{
    public function rules(): array
    {
        $rules = [
            'amount.from' => 'nullable|numeric',
            'amount.to' => 'nullable|numeric',
            'transaction_type' => 'nullable|array',
            'from_to_whom' => 'nullable|string',
            'loan_id' => 'nullable|numeric',
            'officeId' => 'nullable|numeric',
            'name' => 'nullable|string',
            'active' => 'nullable|numeric',
            'printing_type.*' => 'nullable|string',
            'status.*' => 'nullable|string',
        ];

        $this->fillRuleOffice($rules);
        $this->fillRuleCreatedAt($rules);
        $this->fillRuleLimitRule($rules);
        $this->fillRuleOrder($rules);

        return $rules;
    }

    public function authorize(): bool
    {
        return true;
    }
}
