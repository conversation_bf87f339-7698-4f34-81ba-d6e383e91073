<?php

namespace Modules\CashDesk\Domain\Entities;

use Exception;
use Modules\CashDesk\Application\ActionDTO\TransactionDto;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction as DbModel;
use Modules\CashDesk\Repositories\CashOperationalTransactionRepository;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Enums\PaymentDescriptionEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\Client;
use Modules\Common\Models\Payment;
use Modules\Common\Models\Payment as DbPayment;
use Modules\ThirdParty\Services\FiscalDevice\FiscalDeviceServiceFactory;

class IncomingCashTransaction extends DomainModel
{
    public $fiscalDeviceException = null;

    public function __construct(
        private DbModel                                       $dbModel,
        private readonly CashOperationalTransactionRepository $repo,
        private readonly FiscalDeviceServiceFactory           $fiscalDeviceService,
    )
    {
    }

    public function printReceiptFromPayment(Payment $payment): self
    {
        /// check if payment is valid
        if (!$payment->exists) {
            throw new Exception('Error payment not found');
        }

        if (empty($payment->parent_payment_id) && !$payment->isCash()) {
            return $this;
        }
        if (!empty($payment->parent_payment_id)) {
            $parentPayment = $payment->getParentPayment();
            if (!$parentPayment->isCash()) {
                return $this;
            }
        }

        if (!$payment->cashOperationalTransaction) {
            throw new Exception('Error cash operational transaction not found for this payment');
        }

        $this->dbModel = $payment->cashOperationalTransaction;

        $this->printReceipt();

        return $this;
    }

    /**
     * Used for IN payments in CashDesk
     * @throws ProblemException
     */
    public function buildNewFromDto(TransactionDto $dto): self
    {
        return $this
            ->checkForOpenCloseTransactions($dto->office_id, $dto->transaction_type)
            ->setOfficeId($dto->office_id)
            ->setType($dto->transaction_type, null)
            ->setAmount($dto->amount)
            ->setFromToWhom($dto->from_to_whom)
            ->setBasis($dto->basis)
            ->setDirection()
            ->save() // event CashOperationalTransactionSaved -> GenerateCashDocsListener
            ->printReceipt();
    }

    /**
     * Used for IN payments
     * @throws ProblemException
     */
    public function buildNewFromPayment(DbPayment $payment): self
    {

        $client = $payment->client;
        if (empty($client) && !empty($payment->client_id)) {
            $client = Client::where('client_id', $payment->client_id)->first();
        }

        $from = 'Undefind client';
        if (!empty($client->client_id)) {
            $from = $client->getFullName();
        }

        // in case of refinance we have parent_payment_id
        // so we can detect it and change basis & type
        $type = $payment->purpose->value;
        $basis = $payment->description;
        if (!empty($payment->parent_payment_id)) {
            $type = CashOperationalTransactionTypeEnum::LOAN_REFINANCE->value;
            $basis = PaymentDescriptionEnum::REFINANCE_INCOMING->text($payment->getParentPayment()?->loan_id);
        }

        return $this
            ->checkForOpenCloseTransactions($payment->office_id)
            ->setOfficeId($payment->office_id)
            ->setPaymentId($payment->getKey())
            ->setType(null, $type)
            ->setAmount($payment->amount)
            ->setLoanId($payment->loan_id)
            ->setClientId($payment->client_id)
            ->setFromToWhom($from)
            ->setBasis($basis)
            ->setDirection()
            ->save() // event CashOperationalTransactionSaved -> GenerateCashDocsListener
            ->setPaymentCashTransactionLink($payment);
    }

    private function checkForOpenCloseTransactions(int $officeId, ?CashOperationalTransactionTypeEnum $transactionType = null)
    {
        // тр да има опенинг баланс от днес и да няма клосинг баланс от днес

        $transactions = $this->repo->getDailyOpeningAndClosingTransactionTypes($officeId);
        if (empty($transactions)) {

            // if 1st init balance for a day -> continue
            if ($transactionType === CashOperationalTransactionTypeEnum::INITIAL_BALANCE) {
                return $this;
            }


            throw new ProblemException(__('cashdesk::cashDesk.NoInitialBalanceRecord'));
        }

        if (!in_array(CashOperationalTransactionTypeEnum::INITIAL_BALANCE, $transactions)) {
            throw new ProblemException(__('cashdesk::cashDesk.NoInitialBalanceRecord'));
        }

        if (in_array(CashOperationalTransactionTypeEnum::CASH_CLOSING, $transactions)) {
            throw new ProblemException(
                __('cashdesk::cashDesk.NoClosingBalanceRecordYesterday', [
                    'linkToClosePreviewDay' => '<button type="button" class="btn btn-success mt-3 closeBalanceFromYesterday">' . __('Close balance from yesterday') . '</button>'
                ]));
        }

        return $this;
    }

    private function setOfficeId(int $officeId): self
    {
        $this->dbModel->office_id = $officeId;

        return $this;
    }

    private function setPaymentId(int $paymentId): self
    {
        $this->dbModel->payment_id = $paymentId;
        return $this;
    }

    private function setAmount(int $amount): self
    {
        if ($this->dbModel->transaction_type->isOpening()) {
            $amount = 0;
        }

        $this->dbModel->amount = $amount;
        $this->dbModel->amount_signed = $amount;

        return $this;
    }

    // by type comes from Cashdesk
    // by stringType comes from payment: loan_payment, loan_extension, loan_early_repayment
    private function setType(?CashOperationalTransactionTypeEnum $type, ?string $stringType): self
    {
        // понеже нямаме в CashOperationalTransactionTypeEnum тип за early_repayment, ще ползвам loan_payment
        // dirty shiban fix:
        if ($stringType == PaymentPurposeEnum::LOAN_EARLY_REPAYMENT->value) {
            $stringType = PaymentPurposeEnum::LOAN_PAYMENT->value;
        }

        if ($stringType == PaymentPurposeEnum::LOAN_EXTENSION->value) {
            $stringType = PaymentPurposeEnum::LOAN_PAYMENT->value;
        }

        if (!$type) {
            $type = CashOperationalTransactionTypeEnum::tryFrom($stringType);
        }
        if (!$type) {
            $type = CashOperationalTransactionTypeEnum::EARNINGS;
        }

        $this->dbModel->transaction_type = $type;

        return $this;
    }

    private function setLoanId(?int $loanId): self
    {
        if ($loanId) {//not service fee payment
            $this->dbModel->loan_id = $loanId;
        }

        return $this;
    }

    private function setClientId(?int $clientId): self
    {
        if ($clientId) {//not service fee payment
            $this->dbModel->client_id = $clientId;
        }

        return $this;
    }

    private function setFromToWhom(?string $fromToWhom): self
    {
        if ($fromToWhom) {
            $this->dbModel->from_to_whom = $fromToWhom;
        }

        return $this;
    }

    private function setBasis(?string $basis): self
    {
        if ($basis) {
            $this->dbModel->basis = $basis;
        }

        return $this;
    }

    private function setDirection(): self
    {
        $this->dbModel->direction = CashOperationalTransactionDirectionEnum::IN->value;
        return $this;
    }

    private function save(): self
    {
        $this->repo->save($this->dbModel);

        return $this;
    }

    private function setPaymentCashTransactionLink(?Payment $payment = null): self
    {
        if (!empty($payment->payment_id)) {
            $payment->cash_operational_transaction_id = $this->dbModel->cash_operational_transaction_id;
            $payment->save();
        }

        return $this;
    }

    private function printReceipt(): self
    {
        if ($this->dbModel->transaction_type == CashOperationalTransactionTypeEnum::INITIAL_BALANCE) {
            return $this;
        }

        try {
            $fds = $this->fiscalDeviceService;
            match ($this->dbModel->transaction_type) {
                CashOperationalTransactionTypeEnum::EARNINGS,
                CashOperationalTransactionTypeEnum::CASH_SUPPLY => $fds->makeServiceReceipt(
                    $this->dbModel,
                    $this->dbModel->office->fiscalDevice,
                    $this->dbModel->transaction_type->translation()
                ),
                default => $fds->makeFiscalReceipt($this->dbModel)
            };
        } catch (\Throwable $exception) {
            $this->fiscalDeviceException = $exception;
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
