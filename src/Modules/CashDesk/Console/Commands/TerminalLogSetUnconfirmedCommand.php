<?php

namespace Modules\CashDesk\Console\Commands;

use Illuminate\Support\Carbon;
use Modules\CashDesk\Enums\TerminalLogStatusEnum;
use Modules\CashDesk\Models\TerminalLog;
use Modules\Common\Console\CommonCommand;
use Throwable;

class TerminalLogSetUnconfirmedCommand extends CommonCommand
{
    protected $signature = 'terminal-log:set-unconfirmed';

    protected $description = 'If not confirm to print receipt, change status for manual handling';

    protected const defaultTimeExpiration = '1 minute';

    /**
     * @throws Throwable
     */
    public function handle()
    {
        $this->startLog();

        try {
            $processed = TerminalLog::query()
                ->whereNull('confirm_at')
                ->where('status', TerminalLogStatusEnum::new)
                ->where(
                    'created_at',
                    '<=',
                    Carbon::now()->sub(self::defaultTimeExpiration)
                )->update(['status' => TerminalLogStatusEnum::not_confirmed]);
            $msg = 'Receipts unconfirmed: ' . $processed;
            $logMessages = [
                $this->executionTimeString(),
                $msg,
                '---Finish ' . $this->getClassName() . '---'
            ];
            $this->finishLog($logMessages, $processed, $processed, $msg);
        } catch (Throwable $e) {
            $msg = 'Process error: ' . $e->getMessage();
            $logMessages = [
                $msg,
                '---Finish ' . $this->getClassName() . '---'
            ];
            $this->finishLog($logMessages, null, null, $msg);
            throw $e;
        }
    }
}
