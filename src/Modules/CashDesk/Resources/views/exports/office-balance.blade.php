<table>
    <thead>
    <tr>
        <th style="height: 40px; padding: 5pt 0;">{{__('head::clientCard.DateCreated')}}</th>
        <th>{{__('table.Amount')}}</th>
        <th>{{__('table.Transaction')}}</th>
        <th>{{__('table.TransactionFromTo')}}</th>
        <th>{{__('head::clientCard.basis')}}</th>
        <th>{{__('table.CreatedBy')}}</th>
    </tr>
    </thead>
    <tbody>
    @forelse($cashOperationalTransactions as $transaction)
        <tr>
            <td style="padding: 2pt 0; height: 20px;">
                {{formatDate($transaction->created_at , 'd/m/Y H:i:s')}}
            </td>
            @php
            switch ($transaction->direction->value) {
                case 'out':
                    $color = "#ff0f3c";
                    break;
                case 'in':
                    $color = "#347F3C";
                    break;
                default:
                    $color = '#000000';
            }

            $amnt = +abs(intToFloat($transaction->amount));
            if ($transaction->direction->value === 'out') {
                $amnt = -abs(intToFloat($transaction->amount));
            }

            if (!empty($transaction->deleted_at)) {
                $color = "#ff0f3c";
                $amnt = -1 * floatval($amnt);
            }

            @endphp
            <td class="color-export-{{$transaction->direction}}" style="color: {{$color}}">{{$amnt}}</td>
            <td>{{ getTransactionTypeTranslation($transaction->transaction_type->value) }}</td>
            <td>{{ $transaction->from_to_whom }}</td>
            <td>{{ $transaction->basis ?? '' }}</td>
            <td>{{ $transaction->creator->getFullNames()}}</td>
        </tr>
    @empty
        <tr>

        </tr>
    @endforelse
    </tbody>
</table>
