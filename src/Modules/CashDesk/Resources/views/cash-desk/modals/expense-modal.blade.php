@php
    use Mo<PERSON>les\CashDesk\Application\ActionDTO\ModalDTO;use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
    use Modules\CashDesk\Application\ActionDTO\ListDTO;

    /**
     * @var ModalDTO $dto
     */
@endphp
<x-common::modal modal-id="expenseModal" :modal-title="__('table.ExpenseCashDesk')">
    <form method="POST" action="{{ route('payment.cashDesk.add') }}" accept-charset="UTF-8" class="pl-3">
        <div class="modal-body">
            @csrf
            <x-cashdesk::modals.transaction-type :type="CashOperationalTransactionTypeEnum::EXPENSE"/>
            <x-cashdesk::modals.field-office :offices="$dto->offices" :select-office="$dto->selectedOffice"/>
            <x-cashdesk::modals.field-amount/>
            <x-cashdesk::modals.field-transaction-from-to :max-length="100"/>
            <x-cashdesk::modals.field-basis :max-length="200"/>
        </div>
        <x-cashdesk::modals.buttons-save-cancel/>
    </form>
</x-common::modal>