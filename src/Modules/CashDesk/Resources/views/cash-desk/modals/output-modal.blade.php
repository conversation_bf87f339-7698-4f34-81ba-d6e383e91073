@php
    use Modules\CashDesk\Application\ActionDTO\ModalDTO;use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
    use Modules\CashDesk\Application\ActionDTO\ListDTO;

    /**
     * @var ModalDTO $dto
     */
@endphp
<x-common::modal modal-id="outputModal" :modal-title="__('table.OutputCashDesc')">
    <form method="POST" action="{{ route('payment.cashDesk.add') }}" accept-charset="UTF-8" class="pl-3">
        <div class="modal-body">
            <!-- Manual Supply Modal -->
            <h5 class="font-weight-bold">{{ __('table.MadeManualDiscount') }}</h5>
            <hr/>
            @csrf
            <x-cashdesk::modals.transaction-type :type="CashOperationalTransactionTypeEnum::DEDUCTED_FUNDS"/>
            <x-cashdesk::modals.field-office :offices="$dto->offices" :select-office="$dto->selectedOffice"/>
            <x-cashdesk::modals.field-amount/>
            <x-cashdesk::modals.field-transaction-from-to placeholder="table.BankAccount"/>
            <x-cashdesk::modals.field-basis placeholder="table.CashWithdrawal"/>
        </div>
        <x-cashdesk::modals.buttons-save-cancel/>
    </form>
</x-common::modal>