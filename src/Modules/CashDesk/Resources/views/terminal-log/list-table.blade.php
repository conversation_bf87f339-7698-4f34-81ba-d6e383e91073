@php
    use Mo<PERSON>les\CashDesk\Application\ActionDTO\TerminalIndexDTO;use Modules\CashDesk\Enums\TerminalLogStatusEnum;

    /**
    * @var TerminalIndexDTO $dto
    */
@endphp
@foreach($dto->items as $item)
    <tr class="{{$item->statusTrClass}}">
        <td class="cash-desc-list-transaction-date">
            {{ preg_replace('!(.{5}).*(.{5})!','$1...$2',$item->id) }}
            <i class="fa-duotone fa-circle-info cursor-help" title="{{$item->id}}"></i>
        </td>
        <td class="cash-desc-list-transaction-date">{{ $item->createdAt }}</td>
        <td class="cash-desc-list-transaction-date">{{ $item->amount }}</td>
        <td class="cash-desc-list-transaction-date">{{ $item->officeLabel }}</td>
        <td class="cash-desc-list-transaction-date">
            {{ $item->statusLabel }}
            @if($item->status === TerminalLogStatusEnum::failed)
                <i class="fa-duotone fa-circle-info cursor-help" title="{{$item->comment}}"></i>
            @endif

        </td>
        <td class="cash-desc-list-transaction-date">{{ $item->printingTypeLabel }}</td>
        <td class="cash-desc-list-transaction-date">
            <button class="btn btn-default">
                @if($item->isReprint && $item->fiscalDeviceId && $item->status->value == 'failed')
                    <a href="{{route('terminal-log.resend',[$item->id])}}">
                        <i class="fa-duotone fa-print"></i>
                    </a>
                @endif
            </button>
            <button class="btn btn-default">
                <a href="{{route('terminal-log.show',[$item->id])}}">
                    <i class="fa-duotone fa-file-magnifying-glass"></i>
                </a>
            </button>
        </td>
    </tr>
@endforeach
