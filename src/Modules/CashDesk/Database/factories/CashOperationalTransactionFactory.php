<?php

namespace Modules\CashDesk\Database\factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;

class CashOperationalTransactionFactory extends Factory
{
    protected $model = CashOperationalTransaction::class;

    public function definition(): array
    {
        return [
            'transaction_type' => CashOperationalTransactionTypeEnum::CASH_CLOSING,
            'direction' => null,
            'office_id' => Office::factory(),
            'from_to_whom' => 'Първи запис - затваряне',
            'basis' => 'Първи запис - затваряне',
            'amount' => $this->faker->randomNumber(),
            'active' => CashOperationalTransaction::STATUS_ACTIVE,
            'deleted' => CashOperationalTransaction::NOT_DELETED,
            'payment_id' => Payment::factory(),
            'loan_id' => Loan::factory(),
            'created_at' => Carbon::yesterday(),
            'updated_at' => now(),
        ];
    }
}