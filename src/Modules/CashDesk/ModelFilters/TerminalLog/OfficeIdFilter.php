<?php

namespace Modules\CashDesk\ModelFilters\TerminalLog;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class OfficeIdFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        // $this->query->whereI('fiscal_device_id', $filterValue);
        $this->query->whereRaw("fiscal_device_id IN (SELECT fd.fiscal_device_id FROM fiscal_device fd WHERE fd.office_id = '" . $filterValue . "')");
    }
}
