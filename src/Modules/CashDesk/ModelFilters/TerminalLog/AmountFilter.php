<?php

namespace Modules\CashDesk\ModelFilters\TerminalLog;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class AmountFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (!is_array($filterValue)) {
            return;
        }

        $from = $value['from'] ?? null;
        if ($from) {
            $this->query->where('amount', '>=', $from);
        }

        $to = $value['to'] ?? null;
        if ($to) {
            $this->query->where('amount', '<=', $from);
        }
    }
}