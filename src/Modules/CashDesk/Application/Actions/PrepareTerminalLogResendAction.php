<?php

namespace Modules\CashDesk\Application\Actions;

use Exception;
use Modules\CashDesk\Enums\TeminalPrintingTypeEnum;
use Modules\CashDesk\Enums\TremolProcessingStackEnum;
use Modules\CashDesk\Events\Tremol\DTO\TremolDailyReportDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolFiscalReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolServiceReceiptDTO;
use Modules\CashDesk\Events\Tremol\DTO\TremolStornoReceiptDTO;
use Modules\CashDesk\Models\TerminalLog;
use Modules\ThirdParty\Services\FiscalDevice\FiscalDeviceServiceFactory;
use Modules\ThirdParty\Services\FiscalDevice\PHPFiscalDevicePrintService;

class PrepareTerminalLogResendAction
{

    public function __construct(
        public FiscalDeviceServiceFactory  $fiscalDeviceServiceFactory,
        public PHPFiscalDevicePrintService $PHPFiscalDevicePrintService,
    )
    {
    }

    protected function php(TerminalLog $terminalLog): void
    {
        $fiscalDevelope = $terminalLog->fiscalDevice;
        switch ($terminalLog->printing_type) {
            case TeminalPrintingTypeEnum::dailyReport:
                $dto = TremolDailyReportDTO::from($terminalLog->details);
                $this->PHPFiscalDevicePrintService->dailyReport($dto, $fiscalDevelope);
                break;
            case TeminalPrintingTypeEnum::serviceReceipt:
                $dto = TremolServiceReceiptDTO::from($terminalLog->details);
                $this->PHPFiscalDevicePrintService->serviceReceipt($dto, $fiscalDevelope);
                break;
            case TeminalPrintingTypeEnum::stornoReceipt:
                $dto = TremolStornoReceiptDTO::from($terminalLog->details);
                $this->PHPFiscalDevicePrintService->stornoReceipt($dto, $fiscalDevelope);
                break;
            case TeminalPrintingTypeEnum::fiscalReceipt:
                $dto = TremolFiscalReceiptDTO::from($terminalLog->details);

                if (!$terminalLog->cashOperationalTransaction) {
                    throw new Exception('Error no available cash operational transaction.');
                }

                $this->PHPFiscalDevicePrintService->fiscalReceipt($dto, $terminalLog->cashOperationalTransaction);
                break;
        }
    }

    /**
     * @throws Exception
     */
    public function execute(TerminalLog $terminalLog, TremolProcessingStackEnum $stack): void
    {
        if ($terminalLog->status->value != 'failed') {
            throw new \Exception('Can not resend transaction, which is not failed');
        }

        switch ($stack) {
            case TremolProcessingStackEnum::PHP:
                $this->php($terminalLog);
                break;
        }
    }
}
