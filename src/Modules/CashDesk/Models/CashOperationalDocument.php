<?php

namespace Modules\CashDesk\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\Client;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\File;
use Modules\Common\Models\Loan;


/**
 * \Modules\CashDesk\Models\CashOperationalDocument
 *
 * @property int $cash_operational_documents_id
 * @property int $document_template_id
 * @property int $cash_operational_transaction_id
 * @property int|null $file_id
 * @property int|null $loan_id
 * @property int|null $client_id
 * @property int $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read CashOperationalTransaction|null $cashOperationalTransaction
 * @property-read Client|null $client
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read DocumentTemplate $documentTemplate
 * @property-read File|null $file
 * @property-read Administrator|null $handler
 * @property-read Loan|null $loan
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperCashOperationalDocument
 */
class CashOperationalDocument extends BaseModel
{
    const STATUS_NOT_ACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const DELETED = 1;
    const NOT_DELETED = 0;

    /**
     * @var string
     */
    protected $table = 'cash_operational_documents';

    /**
     * @var string
     */
    protected $primaryKey = 'cash_operational_documents_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'cash_operational_documents_id',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    /**
     * @var string[]
     */
    protected $fillable = [
        'document_template_id',
        'cash_operational_transaction_id',
        'file_id',
        'loan_id',
        'client_id',
        'content',
        'variables',
        'active',
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'active' => 'integer'
    ];

    /**
     * @return BelongsTo
     */
    public function documentTemplate(): BelongsTo
    {
        return $this->belongsTo(
            DocumentTemplate::class,
            'document_template_id',
            'document_template_id',
        );
    }

    /**
     * @return BelongsTo
     */
    public function cashOperationalTransaction(): BelongsTo
    {
        return $this->belongsTo(
            CashOperationalTransaction::class,
            'cash_operational_transaction_id',
            'cash_operational_transaction_id',
        );
    }

    /**
     * @return BelongsTo
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(
            File::class,
            'file_id',
            'file_id'
        );
    }

    /**
     * @return BelongsTo
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    /**
     * @return BelongsTo
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }
}
