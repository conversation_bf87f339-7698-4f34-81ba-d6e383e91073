<?php

namespace Modules\CashDesk\FilterForms;

use Carbon\Carbon;
use Modules\CashDesk\Enums\TeminalPrintingTypeEnum;
use Mo<PERSON>les\CashDesk\Enums\TerminalLogStatusEnum;
use Modules\Common\FilterForms\BaseFilterForm;

class TerminalLogListFilterForm extends BaseFilterForm
{
    public function buildForm()
    {
        $this
            ->addCreatedAtFilter(Carbon::today()->format('d-m-Y') . ' - ' . Carbon::today()->format('d-m-Y')) // format: 21-02-2024 - 21-02-2024
            ->addOfficeIdFilter()
            ->addLoanIdFilter()
            ->addAmountFilterFromTo('amount_from', 'amount_to')
            ->add('transaction_id', 'text', ['label' => __('table.LoanId')])
            ->add('status', 'select', [
                'label' => __('table.Status'),
                'choices' => TerminalLogStatusEnum::getSelect(),
                'attr' => [
                    'data-boostrap-selectpicker' => 'true',
                    'multiple' => 'multiple'
                ]
            ])
            ->add('printing_type', 'select', [
                'label' => __('table.Type'),
                'choices' => TeminalPrintingTypeEnum::getSelect(),
                'attr' => [
                    'data-boostrap-selectpicker' => 'true',
                    'multiple' => 'multiple'
                ]
            ]);
    }
}
