<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\CashLoanSteps\ActiveCashLoanSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\EasyPayAttempt;
use Modules\Common\Models\Loan;
use Modules\Common\Models\PaymentTask;
use Tests\TestCase;

class RegisterPaymentFromEasyPayTest extends TestCase
{
    use DatabaseTransactions;

    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $registerPaymentFromEasyPayUrl = '/api/v1/easypay-register-payment/';
    private array $registerPaymentFromEasyPayData = [
        'pin'            => '5609100932',
        'amount'         => 1000,
        'date'           => '',
        'type'           => 'test',
        'transaction_id' => '123123456',
    ];

    public function testHappyPathAndEasyPayObject()
    {
        $this->seed(ActiveLoanSeeder::class);
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = now()->startOfDay()->addHours(11)->format('Y-m-d H:i:s');
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        /** @var EasyPayAttempt $easyPayAttempt */
        $easyPayAttempt = EasyPayAttempt::all()->last();
        $this->assertEquals(ClientSeeder::CLIENT_ID, $easyPayAttempt->client_id);
        $this->assertEquals($data['transaction_id'], $easyPayAttempt->easy_pay_transaction_id);
        $this->assertEquals($data['date'], $easyPayAttempt->sent_at);
        $this->assertEquals(now()->format('Y-m-d H:i'), $easyPayAttempt->created_at->format('Y-m-d H:i'));
        $this->assertEquals(5, $easyPayAttempt->created_by);
        $this->assertNotNull($easyPayAttempt->payment_id);
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'response' => [
                    'msg' => "Payment accepted #".$easyPayAttempt->payment_id,
                ]
            ]);
    }


    public function testNoPin()
    {
        $data = $this->registerPaymentFromEasyPayData;
        $data['pin'] = '';
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        //validator fails
        $response->assertStatus(400)
            ->assertJson([
                'status'=>false,
                "message"=> "{\"pin\":[\"validation.required\"]}"
            ]);
    }

    public function testNoAmount()
    {
        $data = $this->registerPaymentFromEasyPayData;
        $data['amount'] = '0';
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        $response->assertStatus(400)
            ->assertJson([
                'success'=>false,
                'response'=>[
                    'error_code'=>1040,
                    'msg'=>'No amount provided'
                ]
            ]);
    }

    public function testNoDate()
    {
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = 'vasya';
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        $response->assertStatus(400)
            ->assertJson([
                'success'=>false,
                'response'=>[
                    'error_code'=>1032,
                    'msg'=>'Invalid date: vasya provided'
                ]
            ]);
    }

    public function testDuplicateTransactionId()
    {
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = now()->startOfDay()->addHours(11)->format('Y-m-d H:i:s');
        $easyPayAttempt = new EasyPayAttempt();
        $easyPayAttempt->amount = $data['amount'];
        $easyPayAttempt->pin = $data['pin'];
        $easyPayAttempt->client_id = 1;
        $easyPayAttempt->easy_pay_transaction_id = $data['transaction_id'];
        $easyPayAttempt->sent_at = now()->startOfDay()->addHours(11);
        $easyPayAttempt->created_by = Administrator::DEFAULT_EASYPAY_USER_ID;
        $easyPayAttempt->created_at = now();
        $easyPayAttempt->type = $data['type'];
        $easyPayAttempt->save();
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        $response->assertStatus(400)
            ->assertJson([
                'success'=>false,
                'response'=>[
                    'error_code'=>1062,
                    'msg'=>'Duplicated transaction id 123123456'
                ]
            ]);
    }

    public function testNoClientId()
    {
        $this->seed(ActiveLoanSeeder::class);
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = now()->startOfDay()->addHours(11)->format('Y-m-d H:i:s');
        $data['pin'] = '5609100933';
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        /** @var PaymentTask $paymentTask */
        $paymentTask = EasyPayAttempt::all()->last()->payment?->paymentTask;
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'response'=>[
                    'msg'=>'Payment task created #'.$paymentTask->getKey()
                ]
            ]);

        $this->assertEquals(PaymentTaskNameEnum::FIND_CLIENT, $paymentTask->name);
    }

    public function testNoLoanId()
    {
        $this->seed(ClientSeeder::class);
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = now()->startOfDay()->addHours(11)->format('Y-m-d H:i:s');
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        /** @var PaymentTask $paymentTask */
        $paymentTask = EasyPayAttempt::all()->last()->payment?->paymentTask;
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'response'=>[
                    'msg'=>'Payment task created #'.$paymentTask->getKey()
                ]
            ]);

        $this->assertEquals(PaymentTaskNameEnum::DISTRIBUTE_PAYMENT, $paymentTask->name);
    }

    public function testTwoLoans()
    {
        $this->seed([ActiveLoanSeeder::class, ActiveCashLoanSeeder::class]);
        $second = Loan::all()->last();
        $second->client_id = ActiveLoanSeeder::CLIENT_ID;
        $second->save();
        $data = $this->registerPaymentFromEasyPayData;
        $data['date'] = now()->startOfDay()->addHours(11)->format('Y-m-d H:i:s');
        $response = $this
            ->withHeaders($this->headers)
            ->postJson($this->registerPaymentFromEasyPayUrl, $data);
        /** @var PaymentTask $paymentTask */
        $paymentTask = EasyPayAttempt::all()->last()->payment?->paymentTask;
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'response'=>[
                    'msg'=>'Payment task created #'.$paymentTask->getKey()
                ]
            ]);
        $this->assertEquals(PaymentTaskNameEnum::DISTRIBUTE_PAYMENT, $paymentTask->name);
    }

}
