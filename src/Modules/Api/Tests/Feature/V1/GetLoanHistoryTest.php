<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\Payday30LoanExistingInstallmentSeeder;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Head\Repositories\LoanRepository;
use Tests\TestCase;

class GetLoanHistoryTest extends TestCase
{
    use DatabaseTransactions;

    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/get-history/1';
    private array $expectedResponse = [
        'success' => true,
        'response' =>
            [
                1 =>
                    [
                        'loan_id' => 1,
                        'client_id' => 1,
                        'product_id' => 1,
                        'product_type_id' => 1,
                        'loan_type_id' => 1,
                        'discount_percent' => '10.00',
                        'amount_requested' => '10.00',
                        'amount_approved' => '10.00',
                        'installments_requested' => 30,
                        'installments_approved' => 30,
                        'currency_id' => 1,
                        'period_requested' => 30,
                        'period_approved' => 30,
                        'period_grace' => null,
                        'loan_status_id' => 3,
                        'last_status_update_administrator_id' => 1,
                        'payment_method_id' => 1,
                        'source' => LoanSourceEnum::CRM,
                        'channel_id' => 1,
                        'office_id' => 1,
                        'administrator_id' => 1,
                        'comment' => 'first default loan',
                        'amount_rest' => '1000.00',
                        'interest_percent' => 0.1,
                        'penalty_percent' => 0.1,
                        'installment_modifier' => '+7 days',
                        'loan_actual_stats' =>
                            [
                                'loan_id' => 1,
                                'profit' => 0,
                                'approved' => 0,
                                'first_loan' => 0,
                                'has_payment' => 0,
                                'total_installments_count' => 1,
                            ],
                        'documents' => [],
                    ],
            ],
    ];

    public function testHappyPath()
    {
        $this->seed(Payday30LoanExistingInstallmentSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        (new LoanRepository())->addLoanStatusHistory(Loan::find(1));
        $response = $this->withHeaders($this->headers)->getJson($this->url);
        $response->assertStatus(200)->assertJson($this->expectedResponse);
    }
}
