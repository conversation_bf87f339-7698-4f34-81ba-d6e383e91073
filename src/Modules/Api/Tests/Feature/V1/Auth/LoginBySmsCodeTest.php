<?php

namespace Modules\Api\Tests\Feature\V1\Auth;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Database\Seeders\Test\NewPayday30LoanSeeder;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\SmsLoginCode;
use Tests\TestCase;

class LoginBySmsCodeTest extends TestCase
{
    use DatabaseTransactions;

    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/login/';
    private array $data = [
        'code' => 'LEDSO5',
        "client_id" => 1,
        "ip" => "**************",
        "browser" => "xxxx",
    ];
    private array $smsCodeData = [
        "sms_login_code_id" => 43,
        "client_id" => 1,
        "phone_number" => "0896667788",
        "code" => "LEDSO5",
        "used" => 0,
        "ip_requested" => "**************",
        "browser_requested" => "xxxx",
    ];

    public function testHappyPathActive()
    {
        $this->seed([
            ActiveLoanSeeder::class,
        ]);
        DB::table('sms_login_code')->insert($this->smsCodeData);
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);

        $expected = [
            'success' => true,
            'response' => true,
            //'token' => ClientSession::where(['client_id' => 1])->first()->token,
            'redirect' => [
                'url' => 'active.loan',
                'url_params' => [
                    'loan_id' => ActiveLoanSeeder::LOAN_ID,
                    'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID
                ]
            ]
        ];
        $response->assertStatus(200)->assertJson($expected);
        //Test that code was saved and correct
        $codes = SmsLoginCode::where(['client_id' => ClientSeeder::CLIENT_ID])->get();
        $this->assertCount(1, $codes);
        $this->assertEquals(1, $codes[0]->used);

    }

    public function testHappyPathNew()
    {
        $this->seed([
            NewPayday30LoanSeeder::class,
        ]);
        DB::table('sms_login_code')->insert($this->smsCodeData);
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);

        $expected = [
            'success' => true,
            'response' => true,
            //'token' => ClientSession::where(['client_id' => 1])->first()->token,
            'redirect' => [
                'url' => 'contract',
                'url_params' => [
                    'loan_id' => NewPayday30LoanSeeder::LOAN_ID,
                    'loan_status_id' => LoanStatus::NEW_STATUS_ID
                ]
            ]
        ];
        $response->assertStatus(200)->assertJson($expected);
        //Test that code was saved and correct
        $codes = SmsLoginCode::where(['client_id' => ClientSeeder::CLIENT_ID])->get();
        $this->assertCount(1, $codes);
        $this->assertEquals(1, $codes[0]->used);
    }

    public function testHappyPathNoLoan()
    {
        $this->seed([
            ClientRelationsSeeder::class,
        ]);
        DB::table('sms_login_code')->insert($this->smsCodeData);
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);

        $expected = [
            'success' => true,
            'response' => true,
            //'token' => ClientSession::where(['client_id' => 1])->first()->token,
            'redirect' => [
                'url' => 'verify',
                'url_params' => []
            ]
        ];
        $response->assertStatus(200)->assertJson($expected);
        //Test that code was saved and correct
        $codes = SmsLoginCode::where(['client_id' => ClientSeeder::CLIENT_ID])->get();
        $this->assertCount(1, $codes);
        $this->assertEquals(1, $codes[0]->used);
    }

    public function testClientNotFound()
    {
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);
        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
            'error' => 'Client with id 1 was not found'
        ]);
    }

    public function testSmsCodeNotFound()
    {
        $this->seed([
            ClientRelationsSeeder::class,
        ]);
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);
        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
            'error' => 'SmsLoginCode with code LEDSO5 was not found'
        ]);
    }

    public function testSmsCodeBelongsToDifferentClient()
    {
        $this->seed([
            ClientRelationsSeeder::class
        ]);
        DB::table('client')->insert([
            'client_id' => 2,
            'pin' => 5609100933,
            'idcard_number' => "9104188750",
            'first_name' => "Калоян",
            'middle_name' => "Патлеев",
            'last_name' => "Илиев",
            'phone' => "0896667788",
            'legal_status' => Client::LS_INDV,
            'citizenship_type' => Client::CT_LOCAL,
            'email' => '<EMAIL>'
        ]);
        $data = $this->smsCodeData;
        $data['client_id'] = 2;
        DB::table('sms_login_code')->insert($data);
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->data);
        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
            'error' => 'SmsLoginCode with code LEDSO5 belongs to client 2'
        ]);
    }

    public function testSmsCodeNotSaved()
    {
        $this->seed([
            ClientRelationsSeeder::class,
        ]);
        DB::table('sms_login_code')->insert($this->smsCodeData);
        $requestData = $this->data;
        //bad data to fail save
        for ($i = 0; $i < 260; $i++) {
            $requestData['ip'] .= 'A';
        }
        $response = $this->withHeaders($this->headers)->postJson($this->url, $requestData);
        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
        ]);
    }

}
