<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Tests\TestCase;

class ProfileRequestTest extends TestCase
{
    use DatabaseTransactions;
    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/request-from-profile';
    private array $requestData = [
        "ip" => "**************",
        "browser" => "xxxx",
        "product_id" => 1,
        "amount_requested" => 100000,
        "period_requested" => 1,
        "client_id" => "1",
        "payment_method_id" => 1,
        "iban" => '[]',
    ];

    private array $expectedResponse = [
        'success' => true,
        'response' => [
            'client_id' => 1,
            //'loan_id' => 10027,
            'loan_data' => [
                'client_id' => 1,
                'office_id' => 1,
                'loan_status_id' => 1,
                'administrator_id' => 2,
                'last_status_update_administrator_id' => 2,
                //'last_status_update_date' => '2023-02-07T14:26:18.003868Z',
                //'loan_changed_at' => '2023-02-07T14:26:18.003868Z',
                'product_id' => 1,
                'product_type_id' => 1,
                'payment_method_id' => 1,
                'amount_requested' => 1000.00,
                'amount_approved' => 100000,
                'loan_type_id' => 1,
                'amount_rest' => 100000,
                'discount_percent' => 0,
                'period_requested' => 1,
                'period_approved' => 1,
                'period_grace' => null,
                'interest_percent' => 36.00,
                'penalty_percent' => 240.01,
                'installment_modifier' => '+1 days',
                'installments_requested' => 1,
                'installments_approved' => 1,
                'currency_id' => 1,
                'source' => LoanSourceEnum::CRM,
                'channel_id' => 1,
                //'hash' => '1fd0b9f3f4d710b344ed3c5f88ffe599',
                'created_by' => 1,
                //'created_at' => '07-02-2023 14:26',
                //'updated_at' => '07-02-2023 14:26',
                //'loan_id' => 10027,
                //'product_setting' => null,
                'addresses' => [],
                'phones' => [],
//                'installments' => [
//                    [
//                        //'installment_id' => 16,
//                        'client_id' => 1,
//                        //'loan_id' => 10027,
//                        'seq_num' => 1,
//                        //'due_date' => '07-02-2023',
//                        'accrued_total_amount' => '100000.00',
//                        'total_amount' => 100770.00,
//                        'principal' => '100000.00',
//                        'paid_principal' => '0.00',
//                        'rest_principal' => null,
//                        'accrued_interest' => '0.00',
//                        'interest' => 100.00,
//                        'late_interest' => '0.00',
//                        'paid_accrued_interest' => '0.00',
//                        'paid_interest' => '0.00',
//                        'rest_interest' => '0.00',
//                        'paid_late_interest' => '0.00',
//                        'accrued_penalty' => '0.00',
//                        'penalty' => '670.00',
//                        'late_penalty' => '0.00',
//                        'paid_accrued_penalty' => '0.00',
//                        'paid_penalty' => '0.00',
//                        'rest_penalty' => '0.00',
//                        'paid_late_penalty' => '0.00',
//                        'overdue_days' => 0,
//                        'overdue_amount' => '0.00',
//                        'max_overdue_days' => 0,
//                        'max_overdue_amount' => '0.00',
//                        'status' => 'scheduled',
//                        'paid' => 0,
//                        'paid_at' => null,
//                        'active' => true,
//                        'deleted' => false,
//                        //'created_at' => '07-02-2023 14:26',
//                        'created_by' => 1,
//                        //'updated_at' => '07-02-2023 14:26',
//                        'updated_by' => null,
//                        'deleted_at' => null,
//                        'deleted_by' => null,
//                        'enabled_at' => null,
//                        'enabled_by' => null,
//                        'disabled_at' => null,
//                        'disabled_by' => null
//                    ]
//                ],
                //'actual_stats' => null,
//                'client' => [
//                    'client_id' => 1,
//                    'pin' => '5609100932',
//                    'idcard_number' => '9104188750',
//                    'first_name' => 'Калоян',
//                    'middle_name' => 'Патлеев',
//                    'last_name' => 'Илиев',
//                    'phone' => '0896667788',
//                    'email' => '<EMAIL>',
//                    'new' => 1,
//                    'dead' => 0,
//                    'blocked' => false,
//                    'type' => 'real',
//                    'active' => true,
//                    'deleted' => false,
//                    //'created_at' => '07-02-2023 14:26',
//                    'created_by' => null,
//                    'updated_at' => null,
//                    'updated_by' => null,
//                    'deleted_at' => null,
//                    'deleted_by' => null,
//                    'enabled_at' => null,
//                    'enabled_by' => null,
//                    'disabled_at' => null,
//                    'disabled_by' => null,
//                    'latin_names' => null,
//                    'gender' => null,
//                    'legal_status' => 'individual',
//                    'citizenship_type' => 'local',
//                    'legal_status_code' => null,
//                    'economy_sector_code' => null,
//                    'industry_code' => null,
//                    'registered_in_ccr' => 0,
//                    'need_ccr_sync' => 0,
//                    'registered_in_ccr_at' => null,
//                    'set_need_ccr_sync_at' => null,
//                    'unset_need_ccr_sync_at' => null,
//                    'verified' => null,
//                    'birth_date' => null,
//                    'first_name_latin' => null,
//                    'middle_name_latin' => null,
//                    'last_name_latin' => null
//                ],
                'periodLabel' => 'ден'
            ],
            //'token' => '8cd0019e317a7170d36e288945c39778',
            'redirect' => [
                'url' => 'contract',
                'url_params' => [
                    //'loan_id' => 10027,
                    'loan_status_id' => 1
                ]
            ]
        ]
    ];

    public function testHappyPath()
    {
        $this->seed(ClientRelationsSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        $response = $this->withHeaders($this->headers)
            ->postJson($this->url, $this->requestData);

        $expectedResponse = $this->expectedResponse;
        $expectedResponse['response']['loan_id'] = Loan::all()->last()->getKey();
        //$expectedResponse['response']['token'] = $token;
        $response->assertStatus(200)->assertJson($expectedResponse);
    }

    public function testMissingClient()
    {
        $this->seed(ClientRelationsSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        $requestData = $this->requestData;
        $requestData['client_id'] = 2;
        $response = $this->withHeaders($this->headers)
            ->postJson($this->url, $requestData);

        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
            'message' => 'Client with id 2 was not found'
        ]);
    }
}
