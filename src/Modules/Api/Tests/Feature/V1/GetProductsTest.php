<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class GetProductsTest extends TestCase
{
    use DatabaseTransactions;

    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/get-products/';

    public function testHappyPath()
    {
        $response = $this->getJson($this->url, $this->headers);
        $response->assertStatus(200)->assertJson([
            1 => [
                'product_id' => 1,
                'trade_name' => 'До заплата',
                'slider_data' => [
                    "min_amount" => 200,
                    "max_amount" => 600,
                    "default_amount" => 200,
                    "default_period" => 7,
                    "period" => 1,
                    "min_period" => 3,
                    "max_period" => 24,
                    "amount_step" => 50,
                    "period_step" => 1,
                ],
                'slider_stats' => [
                    "totalAmount" => 210,
                    "period" => 7,
                    "periodLabel" => 'дни',
                    "installmentAmount" => 210,
                    "interest" => 1,
                    "paid_interest" => 0,
                    "penalty" => 9,
                    "paid_penalty" => 0,
                    "installmentsCount" => 1,
                    "totalIncreasedAmount" => 10,
                    "discounts" => [],
                ]
            ],
            2 => [
                'product_id' => 2,
                'trade_name' => 'На вноски',
                'slider_data' => [
                    "min_amount" => 200,
                    "max_amount" => 3000,
                    "default_amount" => 200,
                    "default_period" => 7,
                    //"period" => 14,
                    "min_period" => 3,
                    "max_period" => 24,
                    "amount_step" => 50,
                    "period_step" => 1
                ],
                'slider_stats' => [
                    //"totalAmount" => 287,
                    "period" => 7,
                    //"periodLabel" => "двуседмичия",
                    //"installmentAmount" => 41,
                    //"interest" => 11,
                    "paid_interest" => 0,
                    //"penalty" => 76,
                    "paid_penalty" => 0,
                    "installmentsCount" => 7,
                    //"totalIncreasedAmount" => 87,
                    "discounts" => []
                ]
            ],
//            3 => [
//                'product_id' => 3,
//                'trade_name' => 'До заплата Нови пазар',
//                'slider_data' => [
//                    "min_amount" => 200,
//                    "max_amount" => 600,
//                    "default_amount" => 200,
//                    "default_period" => 7,
//                    "period" => 1,
//                    "min_period" => 3,
//                    "max_period" => 24,
//                    "amount_step" => 50,
//                    "period_step" => 1
//                ],
//                'slider_stats' => [
//                    "totalAmount" => 210,
//                    "period" => 7,
//                    "periodLabel" => "дни",
//                    "installmentAmount" => 210,
//                    "interest" => 1,
//                    "paid_interest" => 0,
//                    "penalty" => 9,
//                    "paid_penalty" => 0,
//                    "installmentsCount" => 1,
//                    "totalIncreasedAmount" => 10,
//                    "discounts" => []
//                ]
//            ],
//            4 => [
//                'product_id' => 4,
//                'trade_name' => 'На вноски Нови пазар',
//                'slider_data' => [
//                    "min_amount" => 200,
//                    "max_amount" => 3000,
//                    "default_amount" => 200,
//                    "default_period" => 7,
//                    "period" => 14,
//                    "min_period" => 3,
//                    "max_period" => 24,
//                    "amount_step" => 50,
//                    "period_step" => 1
//                ],
//                'slider_stats' => [
//                    "totalAmount" => 287,
//                    "period" => 7,
//                    "periodLabel" => "месеца",
//                    "installmentAmount" => 41,
//                    "interest" => 11,
//                    "paid_interest" => 0,
//                    "penalty" => 76,
//                    "paid_penalty" => 0,
//                    "installmentsCount" => 7,
//                    "totalIncreasedAmount" => 87,
//                    "discounts" => []
//                ]
//            ],
        ]);
    }
}