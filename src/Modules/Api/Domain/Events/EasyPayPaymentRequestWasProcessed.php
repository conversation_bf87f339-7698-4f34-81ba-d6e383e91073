<?php

namespace Modules\Api\Domain\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\EasyPayAttempt;

class EasyPayPaymentRequestWasProcessed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public readonly EasyPayAttempt $easyPayAttempt) {}
}
