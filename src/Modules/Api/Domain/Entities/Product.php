<?php

namespace Modules\Api\Domain\Entities;

use Modules\Api\Domain\Exceptions\ProductNotFound;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Product as DbModel;
use Modules\Product\Repository\ProductRepository as Repo;

class Product extends DomainModel
{
    public function __construct(
        private DbModel $dbModel,
        private Repo    $repo
    ) {}

    public function loadById(int $id): self
    {
        $dbModel = $this->repo->getProductById($id);
        if (!$dbModel->exists) {
            throw new ProductNotFound($id);
        }
        return $this->buildFromExisting($dbModel);
    }

    public function buildFromExisting(DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    public function setDbModel(DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
