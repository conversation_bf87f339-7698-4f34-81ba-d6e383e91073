<?php

namespace Modules\Api\Http\Requests;

use Modules\Common\Interfaces\LoggableRequestInterface;

interface ApiRequestInterface extends LoggableRequestInterface
{
    public function route($param = null, $default = null);

    public function header($key = null, $default = null);
    public function bearerToken();
    public function validated($key = null, $default = null);

    public function asDto();
}