<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\TmpUpdateDto;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\LoggableRequestInterface;

class TmpUpdateRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules():array
    {
        return [
            'utm_tracker_id' => 'nullable|numeric',
            'request_id' => 'required|numeric',
            'email' => $this->getConfiguration('requestRules.email'),
            'pin' => $this->getConfiguration('requestRules.pin'),
            'idcard_number' => 'required|string',
            'payment_method_id' => 'required|numeric',
            'iban' => 'nullable|string',
            'ip' => 'required',
            'browser' => 'required'
        ];
    }

    public function asDto(): TmpUpdateDto
    {
        return TmpUpdateDto::from($this->validated());
    }
}
