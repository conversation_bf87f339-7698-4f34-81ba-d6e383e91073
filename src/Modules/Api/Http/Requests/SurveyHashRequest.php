<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\SurveyHashDto;
use Modules\Common\Http\Requests\BaseRequest;

class SurveyHashRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules(): array
    {
        return [
            'ip' => 'required',
            'browser' => 'required'
        ];
    }

    public function asDto(): SurveyHashDto
    {
        return new SurveyHashDto(
            $this->route()->parameter('rate'),
            $this->route()->parameter('hash')
        );
    }
}
