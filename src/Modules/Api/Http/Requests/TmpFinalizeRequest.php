<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\TmpFinalizeRequestDto;
use Modules\Common\Http\Requests\BaseRequest;

class TmpFinalizeRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules(): array
    {
        return [
            'utm_tracker_id' => 'nullable|numeric',
            'request_id' => 'required|numeric',
            'first_name' => $this->getConfiguration('requestRules.firstName'),
            'middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'last_name' => $this->getConfiguration('requestRules.lastName'),
            'valid_date' => $this->getConfiguration('requestRules.validDate'),
            'lifetime_idcard' => 'required|integer|in:0,1',
            'issue_date' => 'required|date',
            'address' => 'required',
            'city_id' => 'required',
            'ip' => 'required',
            'browser' => 'required'
        ];
    }

    public function asDto(): TmpFinalizeRequestDto
    {
        return TmpFinalizeRequestDto::from($this->validated());
    }
}
