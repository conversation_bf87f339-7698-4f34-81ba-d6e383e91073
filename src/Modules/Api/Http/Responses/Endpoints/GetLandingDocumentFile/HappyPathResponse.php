<?php

namespace Modules\Api\Http\Responses\Endpoints\GetLandingDocumentFile;

use Throwable;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class HappyPathResponse extends AbstractResponse implements GetLandingDocumentFileResponseInterface
{
    public const HTTP_CODE = Response::HTTP_OK;
    public function matches(?Throwable $e): bool
    {
        return is_null($e);
    }

    public function get(array $data = []): array
    {
        $content = isTesting() ? 'File Contents' : file_get_contents($data[0]);
        return [
            'success' => true,
            'response' => [
                'document' => 'data: '. mime_content_type($data[0]) .';base64,'. base64_encode($content)
            ],
        ];
    }
}