<?php

namespace Modules\Api\Http\Responses\Endpoints\UpdateRequest;

use Throwable;
use Modules\Api\Domain\Exceptions\ClientAlreadyExists;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class ClientAlreadyExistsResponse extends AbstractResponse implements UpdateRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof ClientAlreadyExists;
    }

    public function get(array $data = []): array
    {
        $exc = $this->getException();

        $maskedPhone = '';
        if (method_exists($exc, 'getPhone') && !empty($exc->getPhone())) {
            $phone = $exc->getPhone();
            $maskedPhone = str_repeat("*", strlen($exc->getPhone()) - 4)
                         . substr($exc->getPhone(), -4);
        }

        $clientId = '';
        if (method_exists($exc, 'getClientId') && !empty($exc->getClientId())) {
            $clientId = $exc->getClientId();
        }

        return [
            'success' => false,
            'response' => [
                'redirect' => [
                    'url' => 'login.sms',
                    'url_params' => $data,
                ],
                'client_id' => $clientId,
                'phone' => $maskedPhone,
                'maskedPhone' => $maskedPhone,
            ],
        ];
    }
}
