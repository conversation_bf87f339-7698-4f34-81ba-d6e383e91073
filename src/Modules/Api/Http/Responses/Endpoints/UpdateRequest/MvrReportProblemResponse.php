<?php

namespace Modules\Api\Http\Responses\Endpoints\UpdateRequest;

use Throwable;
use Modules\Api\Domain\Exceptions\MvrReportIsNotCorrect;
use Modules\Api\Domain\Exceptions\MvrReportNotFound;
use Modules\Api\Http\Responses\AbstractResponse;
use Modules\Sales\Domain\Exceptions\IdCardExpired;
use Symfony\Component\HttpFoundation\Response;

class MvrReportProblemResponse extends AbstractResponse implements UpdateRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;

    public function matches(?Throwable $e): bool
    {
        if (!$e) {
            return false;
        }
        
//        return $e instanceof MvrReportNotFound || $e instanceof MvrReportNotCorrect;
        return in_array(get_class($e), [
            MvrReportNotFound::class,
            MvrReportIsNotCorrect::class,
            IdCardExpired::class,
        ]);
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'redirect' => [
                    'url' => 'third.step',
                    'url_params' => $data,
                ],
            ],
        ];
    }
}
