<?php

namespace Modules\Api\Http\Responses\Endpoints\UpdateRequest;

use Throwable;
use Modules\Api\Domain\Exceptions\TmpRequestNotFound;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class TmpRequestNotFoundResponse extends AbstractResponse implements UpdateRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof TmpRequestNotFound;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'redirect' => [
                    'url' => 'step1',//TODO: find out real link to step 1 form
                    'url_params' => $data,
                ],
            ],
        ];
    }
}