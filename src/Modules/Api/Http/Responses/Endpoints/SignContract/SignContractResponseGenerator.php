<?php

namespace Modules\Api\Http\Responses\Endpoints\SignContract;

use Modules\Api\Http\Responses\ResponseGenerator;
use Modules\Api\Http\Responses\ResponseGeneratorInterface;

class SignContractResponseGenerator extends ResponseGenerator implements ResponseGeneratorInterface
{
    protected string $serves = 'api.signContract';
    protected string $responseInterface = SignContractResponseInterface::class;
}