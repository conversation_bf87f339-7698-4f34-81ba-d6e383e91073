<?php

namespace Modules\Api\Http\Responses\Endpoints\FinalizeRequest;

use Carbon\Carbon;
use Modules\Api\Services\ApiService;
use Throwable;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Api\Http\Responses\AbstractResponse;
use Modules\Common\Models\Client;
use Symfony\Component\HttpFoundation\Response;

class HappyPathResponse extends AbstractResponse implements FinalizeRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_OK;

    public function matches(?Throwable $e): bool
    {
        return is_null($e);
    }

    public function get(array $data = []): array
    {
        /**
         * @var Client $client
         */
        $client = $data['client'];

        $apiService = app(ApiService::class);
        $token = $apiService->getProfileToken($client);

        return [
            'success' => true,
            'response' => [
                'client_id' => $data['client']->getKey(),
                'loan_id' => $data['loan']->getKey(),
                'token' => $token,
                'url' => 'contract',
                'url_params' => ['loan_id' => $data['loan']->getKey()]
            ],
        ];
    }
}
