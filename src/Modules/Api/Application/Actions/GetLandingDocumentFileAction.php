<?php

namespace Modules\Api\Application\Actions;

use Illuminate\Support\Facades\Storage;
use Modules\Api\Domain\Exceptions\FileInfoNotFound;
use Modules\Api\Domain\Exceptions\FileIsMissing;
use Modules\Api\Domain\Exceptions\LandingDocIdIsMissing;
use Modules\Api\Domain\Exceptions\LandingDocNotFoundById;
use Modules\Api\Repositories\LandingDocRepository;
use Modules\Common\Http\Dto\Dto;
use Modules\Head\Repositories\FileRepository;

readonly class GetLandingDocumentFileAction implements ApiActionInterface
{
    public function __construct(
        private LandingDocRepository $repo,
        private FileRepository $fileRepository
    ){}

    public function execute(Dto $dto): array
    {
        //redundant?
        if(! trim($dto->landingDocId)){
            throw new LandingDocIdIsMissing();
        }

        $landingDoc = $this->repo->getById($dto->landingDocId);
        if(! $landingDoc){
            throw new LandingDocNotFoundById($dto->landingDocId);
        }

        //redundant?
        $file = $this->fileRepository->getById($landingDoc->file_id);
        if (!$file) {
            throw new FileInfoNotFound($landingDoc->file_id);
        }

        $downloadPath = $file->file_path . $file->file_name;
        if (!Storage::disk('public')->fileExists($downloadPath)) {
            throw new FileIsMissing($downloadPath);
        }

        return [Storage::disk('public')->path($downloadPath)];
    }
}