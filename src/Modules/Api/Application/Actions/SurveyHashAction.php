<?php

namespace Modules\Api\Application\Actions;

use Modules\Api\Database\Entities\ClientPoll;
use Modules\Api\Domain\Exceptions\ClientPollNotFoundByHash;
use Modules\Api\Domain\Exceptions\InvalidSurveyRate;
use Modules\Api\Http\Dto\SurveyHashDto;
use Modules\Common\Http\Dto\Dto;

class SurveyHashAction implements ApiActionInterface
{
    public function execute(SurveyHashDto|Dto $dto): array
    {
        if (! $dto->rate) {
            throw new InvalidSurveyRate($dto->rate);
        }

        if (! ClientPoll::setRate($dto->hash, $dto->rate)) {
            throw new ClientPollNotFoundByHash($dto->hash);
        }

        return [];
    }
}