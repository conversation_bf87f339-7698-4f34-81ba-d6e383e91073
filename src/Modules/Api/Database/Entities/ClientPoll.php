<?php

namespace Modules\Api\Database\Entities;

use Modules\Common\Models\BaseModel;

class ClientPoll extends BaseModel
{
    protected $table = 'client_poll';
    protected $primaryKey = 'client_poll_id';

    protected $fillable = [
        'client_id',
        'rate',
        'loan_id',
        'email_template_id',
        'email_id',
        'hash',
        'active',
        'deleted',
        'created_at',
    ];

    public static function setHash(
        string $hash, 
        int $clientId, 
        int $emailTemplateId, 
        int $emailId, 
        string $date, 
        int $loanId = null
    ): bool {
        $result = false;

        $hashCheckupResult = ClientPoll::where('hash', $hash)->first();

        if (!empty($hashCheckupResult)) { 
            return $result; 
        }

        $convertDate = strtotime($date);
        $date = (
            empty($convertDate) ?
            date('Ymd H:i:s') :
            date('Ymd H:i:s', $convertDate)
        );

        $clientPollObject = new ClientPoll;
        $clientPollObject->fill([
            'client_id' => $clientId,
            'loan_id' => $loanId,
            'email_template_id' => $emailTemplateId,
            'email_id' => $emailId,
            'hash' => $hash,
            'active' => 1,
            'deleted' => 0,
            'created_at' => $date,
        ]);
        $clientPollObjectStatus = $clientPollObject->save();

        $result = (
            !empty($clientPollObjectStatus) &&
            $clientPollObjectStatus === true
        );

        return $result;
    }

    public static function hashCheckup(string $hash): array
    {
        $result = [];

        $hashCheckupResult = ClientPoll::where([
            'hash' => $hash,
            'active' => 1
        ])->first();

        if (empty($hashCheckupResult)) { 
            return $result; 
        }

        $result = $hashCheckupResult->attributes;
        return $result;
    }

    public static function setRate(string $hash, int $rate): bool
    {
        $hashCheckup = self::hashCheckup($hash);
        $result = false;

        if (
            empty($hashCheckup) ||
            empty($hashCheckup['client_poll_id'])
        ) { 
            return $result; 
        }

        $result = ClientPoll::where([
            'client_poll_id' => $hashCheckup['client_poll_id'],
        ])->update([
            'rate' => $rate,
            'active' => 0,
        ]);

        return !empty($result);
    }
}
