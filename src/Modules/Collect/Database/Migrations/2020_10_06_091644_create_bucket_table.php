<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;


return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bucket', function (Blueprint $table) {
            $table->increments('bucket_id')->from(0);
            $table->string('name');
            $table->string('description')->nullable();
            $table->enum('type', ['static', 'dynamic'])->index();
            $table->integer('overdue_days_from')->nullable();
            $table->integer('overdue_days_to')->nullable();
            $table->timestamp('created_at');
        });

        Schema::create('bucket_history', function (Blueprint $table) {
            $table->increments('bucket_history_id');
            $table->integer('bucket_id');
            $table->string('name');
            $table->string('description')->nullable();
            $table->enum('type', ['static', 'dynamic']);
            $table->integer('overdue_days_from')->nullable();
            $table->integer('overdue_days_to')->nullable();
            $table->timestamp('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bucket');
        Schema::dropIfExists('bucket_history');
    }
};
