<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('outer_collector_reports', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();
            $table->string('report_type')->default('daily')->index();
            $table->foreignIdFor(\Modules\Common\Models\File::class, 'file_id')
                ->constrained('file', 'file_id');

            $table->date('to_date')->index();
            $table->json('send_to');

            $table->unique(['report_type', 'to_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('outer_collector_reports');
    }
};
