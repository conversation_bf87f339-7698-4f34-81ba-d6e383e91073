<?php

namespace Modules\Collect\Tests\Integration\Application\Listeners;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\CashLoanSteps\ActiveCashLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Helpers\TestHelpers\TimeMachine;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Payment;
use Tests\TestCase;

class ExtendLoanListenerTest extends TestCase
{
    use DatabaseTransactions;

    public function testExtensionWithPayment()
    {
        $this->seed(ActiveCashLoanSeeder::class);
        $loan = ActiveCashLoanSeeder::$loan;
        $timeMachine = new TimeMachine();
        $client = $timeMachine->sendBackInTime($loan->client, 40);
        $initialDueDate = now()->subDays(10)->startOfDay();
        /** @var Installment $installment */
        $installment = $loan->installments->first();
        $this->assertEquals(
            $initialDueDate,
            $installment->refresh()->due_date
        );
        $payment = new Payment();
        $payment->amount = 1000;
        $payment->loan_id = $loan->getKey();
        $payment->direction = PaymentDirectionEnum::IN;
        $payment->purpose = PaymentPurposeEnum::LOAN_EXTENSION;
        $payment->source = PaymentSourceEnum::MANUAL_CREATION;
        $payment->payment_method_id = PaymentMethodEnum::BANK->id();
        $payment->currency_id = 1;
        $payment->save();

        // $expectedDueDate = now()->addDays(5)->startOfDay();
        // $this->assertEquals(
        //     $expectedDueDate,
        //     $installment->refresh()->due_date
        // );

    }
}
