<?php

namespace Modules\Collect\Tests\Acceptance;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Http\Response;
use Modules\Common\Database\Seeders\Test\AllSteps\AfterLoanActivationSeeder;
use Modules\Common\Enums\BucketEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Tests\TestCase;

class MoveLoanToDifferentBucketTest  extends TestCase
{
    use DatabaseTransactions;

    public function testCompanyCashNormalApplicationHappyApprove()
    {
        $this->seed(AfterLoanActivationSeeder::class);
        /** @var Loan $loan */
        $loan = Loan::find(AfterLoanActivationSeeder::LOAN_ID);
        $this->actingAs(Administrator::find(Administrator::SYSTEM_ADMINISTRATOR_ID));

        $response = $this->call(
            'GET',
            route('collect.loan-buckets.store'),
            [
                'bucket_id' => BucketEnum::WRITTEN_OFF->id(),
                'loans' => [$loan->getKey()],
                'comment' => 'test',
                'placed_manually' => 1
            ],
            $this->prepareCookiesForRequest(),
            [],
            $this->transformHeadersToServerVars([])
        );
        $response->assertStatus(302)->assertRedirect(route('collect.loan-buckets.list'));
        /** @var Response $response */
        $this->followRedirects($response)->assertSee(__('collect::bucketCrud.CreatedSuccessfully'));

        $loan->refresh();
        $this->assertEquals(LoanStatus::WRITTEN_OF_STATUS_ID, $loan->loan_status_id);
    }
}