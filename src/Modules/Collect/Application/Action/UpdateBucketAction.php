<?php

namespace Modules\Collect\Application\Action;

use Modules\Common\Models\Bucket;
use Modules\Collect\Domain\Exceptions\BucketNotFound;
use Modules\Collect\Http\Dto\BucketDto;

readonly class UpdateBucketAction
{
    public function __construct() {}

    public function execute(BucketDto $dto): Bucket
    {
        if (empty($dto->bucket_id)) {
            throw new \Exception('No bucket id provided for update');
        }

        $bucket = Bucket::find($dto->bucket_id);
        if (empty($bucket->bucket_id)) {
            throw new \BucketNotFound($dto->bucket_id);
        }

        $bucket->name = $dto->name;
        $bucket->description = $dto->description;
        $bucket->type = $dto->type;
        $bucket->from = $dto->from;
        $bucket->to = $dto->to;
        $bucket->save();

        return $bucket;
    }
}
