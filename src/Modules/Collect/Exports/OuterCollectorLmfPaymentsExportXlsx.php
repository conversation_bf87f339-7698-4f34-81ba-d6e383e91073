<?php

namespace Modules\Collect\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OuterCollectorLmfPaymentsExportXlsx implements FromArray, WithHeadings, ShouldAutoSize, WithMapping
{
    protected array $rows;

    public function __construct(array $rows)
    {
        $this->rows = $rows;
    }

    public function map($row): array
    {
        return $row;
    }

    public function headings(): array
    {
        return [
            'Дата',
            'Кредит №',
            'Клиент',
            'Офис',
            'Възложение',
            'Платена сума',
            'Дни просрочие',
            'Комисионна',
            'Фактура',
            '',
            'Общо плащания',
            'Общо фактура',
        ];
    }

    public function array(): array
    {
        return $this->rows;
    }
}
