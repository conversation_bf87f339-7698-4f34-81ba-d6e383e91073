<?php

namespace Modules\Collect\Exports;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Modules\Common\Models\BucketTask;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BucketTasksExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    /**
     * @var Collection
     */
    private Collection $rows;
    public ?Builder $builder = null;

    /**
     * LoanExport constructor.
     *
     * @param Collection $rows
     */
    public function __construct(Collection $rows)
    {
        $this->rows = $rows;
    }

    public function formatRow(array $row): array
    {
        try {
            $timer = Carbon::parse($row['bucket_task_finished_at'])->diffInMinutes(Carbon::parse($row['bucket_task_started_at']));
        } catch (\Exception $e){
            $timer = '';
        }
        return [
            $row['bucket_task_client_id'] ?? '',
            $row['bucket_task_loan_id'] ?? '',
            $row['product_name'] ?? '',
            amount($row['bucket_task_amount'] ?? 0),
            $row['loan_period_approved'] ?? '',
            $row['bucket_task_repaid_credits_count'] ?? '',
            $row['bucket_task_client_full_name'] ?? '',
            $row['bucket_task_pin'] ?? '',
            $row['bucket_task_phone'] ?? '',
            isset($row['bucket_task_bucket_id']) &&  $row['bucket_task_bucket_id'] == 0 ? __('table.paymentDateSoon') : __('table.clientHasOverdue'),
            amount($row['bucket_task_overdue_amount'] ?? 0),
            $row['bucket_task_overdue_days_count'] ?? 0,
            isset($row['bucket_name']) && $row['bucket_name'] ? __($row['bucket_name']) : '',
            isset($row['loan_bucket_placed_manually']) && $row['loan_bucket_placed_manually'] ? 'да' : '',
            isset($row['bucket_task_status']) && $row['bucket_task_status'] === BucketTask::STATUS_PROCESSING
                ? __('collect::bucketTask.processedBy', ['name' => ($row['administrator_first_name'] ?? '') . ' ' .  ($row['administrator_last_name'] ?? '')])
                : '',
            $row['bucket_task_history_created_at'] ?? '',
            isset($row['history_decision_name']) && $row['history_decision_name'] ? __($row['history_decision_name']) : '',
            $row['bucket_task_history_details'] ?? '',
            $timer
        ];
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    public function headings(): array
    {
        $translations = [
            __('table.ClientId'),
            __('table.ContractNumber4'),
            __('table.Product'),
            __('table.LoanAmount'),
            __('table.LoanPeriod'),
            __('table.RepaidLoansCount'),
            __('table.clientNames'),
            __('table.Pin'),
            __('table.Phone'),
            __('table.Task'),
            __('table.AmountDue'),
            __('table.overdueDays'),
            __('table.Bucket'),
            __('table.Manual'),
            __('table.Status'),
            __('table.LastUpdate'),
            __('table.Code'),
            __('table.Details'),
            __('table.Timer')
        ];
        foreach ($translations as $i=>$name){
            $translations[$i] = strip_tags($name);
        }

        return $translations;
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 8,
            'C' => 30,
            'D' => 10,
            'E' => 12,
            'F' => 20,
            'G' => 30,
            'H' => 10,
            'I' => 10,
            'J' => 20,
            'K' => 10,
            'L' => 10,
            'M' => 10,
            'N' => 10,
            'O' => 10,
            'P' => 10,
            'Q' => 10,
            'R' => 10,
            'S' => 10,
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        foreach ($sheet->getColumnIterator('K', 'L') as $column) {
//            $column->getWorksheet()
//                ->getStyle($column->getColumnIndex().'1:'.$column->getColumnIndex().$sheet->getHighestDataRow())
//                ->getAlignment()
//                ->setWrapText(true);
//        }
    }
}
