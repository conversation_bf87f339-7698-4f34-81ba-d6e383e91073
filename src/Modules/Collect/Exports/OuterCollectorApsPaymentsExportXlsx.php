<?php

namespace Modules\Collect\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OuterCollectorApsPaymentsExportXlsx implements FromArray, WithHeadings, ShouldAutoSize, WithMapping
{
    protected array $rows;
    protected array $headers;

    public function __construct(array $rows)
    {
        $this->rows = $rows;
        $this->headers = array_keys((array)$rows[0]);
    }

    public function map($row): array
    {
        return (array) $row;
    }

    public function headings(): array
    {
        return $this->headers;
    }

    public function array(): array
    {
        return $this->rows;
    }
}
