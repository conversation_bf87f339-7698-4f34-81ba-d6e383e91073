<?php

namespace Modules\Collect\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Modules\Sales\Domain\Events\DayHasPassedForClient;

// stopped for now
class DispatchNewDayForClientsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public function __construct(
        public Collection $clients
    )
    {
        //
    }

    public function handle(): void
    {
        foreach($this->clients as $client) {
            DayHasPassedForClient::dispatch($client);
        }
    }
}
