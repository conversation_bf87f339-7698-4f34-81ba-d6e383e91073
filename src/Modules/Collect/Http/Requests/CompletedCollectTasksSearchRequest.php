<?php

namespace Modules\Collect\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

final class CompletedCollectTasksSearchRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'created_at' => 'nullable|string|regex:' . self::$dateRangeRegex,
            'finished_at' => 'nullable|string|regex:' . self::$dateRangeRegex,
        ];
    }

    public function messages(): array
    {
        return [
            'created_at.regex' => __('Грешен формат. Пример: :exampleDate', [
                'exampleDate' => date('d-m-Y') . ' - ' . date('d-m-Y')
            ]),
            'finished_at.regex' => __('Грешен формат. Пример: :exampleDate', [
                'exampleDate' => date('d-m-Y') . ' - ' . date('d-m-Y')
            ]),
        ];
    }
}
