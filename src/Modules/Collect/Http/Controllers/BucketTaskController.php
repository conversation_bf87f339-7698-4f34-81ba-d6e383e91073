<?php

namespace Modules\Collect\Http\Controllers;

ini_set('max_execution_time', 9000);
ini_set('memory_limit', '2536M');

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Collect\Application\Action\BucketTaskExportAction;
use Modules\Collect\Application\Action\BucketTaskIndexAction;
use Modules\Collect\Application\Action\ProcessBucketTaskAction;
use Modules\Collect\Http\Requests\BucketTaskFilterRequest;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\BucketTask;

final class BucketTaskController extends BaseController
{
    public function list(
        BucketTaskIndexAction $action,
        BucketTaskFilterRequest $request
    ): RedirectResponse|View {
        return view(
            'collect::bucket-tasks.list',
            $action->execute(
                $request->validated()
            )
        );
    }

    public function export(
        BucketTaskFilterRequest $request,
        BucketTaskExportAction $bucketTaskExportAction
    ) {
        return $bucketTaskExportAction->execute(
            $request->validated()
        );
    }

    public function processTask(
        BucketTask              $bucketTask,
        ProcessBucketTaskAction $action
    ): RedirectResponse {

        $admin = Auth::user();
        $stopMessage = $admin->getProcessingTaskNotification('collect', $bucketTask->loan_id);
        if (!empty($stopMessage)) {
            return redirect()
                ->route('collect.bucket-tasks.list', [])
                ->with('fail', $stopMessage);
        }


        DB::beginTransaction();
        try {

            $bucketTask = $action->execute($bucketTask);

            DB::commit();

            return redirect()->route(
                'head.clients.cardProfile',
                [
                    'clientId' => $bucketTask->loan->client->client_id,
                    'second' => $bucketTask->loan->loan_id,
                    'third' => ClientCardRouteParamEnum::COLLECT->value,
                    'fourth' => $bucketTask->getKey(),
                ]
            );

        } catch (\Throwable $e) {

            DB::rollBack();

            return redirect()
                ->route('collect.bucket-tasks.list', [])
                ->with('fail', $e->getMessage());
        }
    }
}
