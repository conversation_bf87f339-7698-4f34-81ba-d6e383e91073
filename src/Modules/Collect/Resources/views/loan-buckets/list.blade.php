@extends('layouts.app')
@php
/**
 * @var \Modules\Common\Models\LoanBucket[] $loanBuckets
 */
@endphp

@section('content')
    <x-card-filter-form :filter-form="$filterForm"/>

    <x-card>
        <x-slot:title>{{__('other.TaskList')}}</x-slot:title>

        <div class="form-group">
            @if(getAdmin()->hasPermissionTo('collect.loan-buckets.send-sms-message'))
                <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#sendSmsModal">
                    {{ __('head::clientCard.SendSMS') }}
                </button>
            @endif
            @if(getAdmin()->hasPermissionTo('collect.loan-buckets.send-email-message'))
                <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#sendEmailModal">
                    {{ __('head::clientCard.SendEmail') }}
                </button>
            @endif
        </div>

        <form id="changeBucketForm" action="{{ route('collect.loan-buckets.store') }}" method="GET"
              data-parsley-validate="true">
            @csrf
            <div class="row form-group d-none" id="updateBuckets">
                <div class="col-lg-4">
                    <div class="form-group">
                        <select name="bucket_id" class="form-control w-100 mb-3" required="required">
                            <option value="">{{__('table.Bucket')}}</option>
                            @foreach($buckets_static as $bucketId => $bucketName)
                                <option value="{{$bucketId}}">
                                    {{__($bucketName)}}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <textarea name="comment" class="form-control"></textarea>
                    </div>

                    <button id="saveChanges" type="submit" class="btn btn-sm btn-success" style="float: right;">
                        <i class="fa fa-save"></i>&nbsp;
                        Запази
                    </button>
                    <!-- End ./btn-group -->
                </div>
            </div>
            <!-- End ./form-group -->

            <x-table>
                <x-slot:head>
                    <th>
                        <a href="javascript:void(0);" id="chooseAll">
                            [#] Select all
                        </a>
                    </th>
                    <th>{{__('table.Bucket')}}</th>
                    <th>{{__('table.Client')}}</th>
                    <th>{{__('table.Pin')}}</th>
                    <th>@sortablelink('loan_id', __('table.ContractNumber'))</th>
                    <th>{{__('table.StartDate')}}</th>
                    <th>{{__('table.FinalRepaidDate')}}</th>
                    <th>@sortablelink('amount_withdraw', __('table.AmountApproved'))</th>
                    <th>@sortablelink('amount_overdue', __('table.OverdueAmount'))</th>
                    <th>@sortablelink('days_overdue', __('table.OverdueDays'))</th>
                    <th>@sortablelink('installments_overdue', __('table.InstallmentsOverdue'))</th>
                    <th>{{__('table.Phone')}}</th>
                    <th>{{__('table.Address')}}</th>
                    <th>{{__('table.Comment')}}</th>
                    <th>{{__('table.Guarant')}}</th>
                    <th>{{__('table.Consultant')}}</th>
                    <th>{{__('table.Office')}}</th>
                </x-slot:head>

                @foreach($loanBuckets as $loanBucket)
                    <tr>
                        <td class="text-center">
                            <input class="loan-buckets-checkbox"
                                   type="checkbox"
                                   name="loans[]"
                                   value="{{$loanBucket->loan_id}}"
                            />
                        </td>
                        <td>{{ __('collect::buckets.bucket_' . $loanBucket->bucket_id) }}</td>
                        <td>{{ $loanBucket->client_name }}</td>
                        <td>{{ $loanBucket->pin }}</td>
                        <td>
                            <a target="_blank" href="{{ route(
                                'head.clients.cardProfile',
                                [
                                    'clientId' => $loanBucket->client_id,
                                    'second' => $loanBucket->loan_id],
                                ) }}">
                                {{ $loanBucket->loan_id }}
                            </a>
                        </td>
                        <td>{{ \Carbon\Carbon::parse($loanBucket->start_date)->format('d.m.Y') }}</td>
                        <td>{{ \Carbon\Carbon::parse($loanBucket->end_date)->format('d.m.Y') }}</td>
                        <td>{{ intToFloat($loanBucket->amount_withdraw) }}</td>
                        <td>{{ $loanBucket->amount_overdue }}</td>
                        <td>{{ $loanBucket->days_overdue }}</td>
                        <td>{{ $loanBucket->installments_overdue }}</td>
                        <td>{{ $loanBucket->phone }}</td>
                        <td>{{ $loanBucket->address }}</td>
                        <td><span class="comment-text">{{ $loanBucket->comment }}</span></td>
                        <td>
                            {!! $loanBucket->getGuarantorsLabel() !!}
                        </td>
                        <td>{{$loanBucket->loan?->getConsultantName()}}</td>
                        <td>{{getOfficeName($loanBucket->loan?->office_id)}}</td>
                    </tr>
                @endforeach

            </x-table>
        </form>

        <x-table-pagination :rows="$loanBuckets"/>

        <!-- Send SMS Modal -->
        <div class="modal fade" id="sendSmsModal" tabindex="-1" aria-labelledby="sendSmsModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sendSmsModalLabel">{{ __('head::clientCard.SendSMS') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        {!! form($smsMessageForm, [
                            'autocomplete' => 'off',
                            'id' => 'smsMessageForm',
                        ]) !!}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{{ __('table.Cancel') }}</button>
                        <button type="submit" form="smsMessageForm" class="btn btn-primary">
                            {{ __('head::clientCard.SendSMS') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Send Email Modal -->
        <div class="modal fade" id="sendEmailModal" tabindex="-1" aria-labelledby="sendEmailModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sendEmailModalLabel">{{ __('head::clientCard.SendEmail') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        {!! form($emailMessageForm, [
                            'autocomplete' => 'off',
                            'id' => 'emailMessageForm',
                        ]) !!}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{{ __('table.Cancel') }}</button>
                        <button type="submit" form="emailMessageForm" class="btn btn-primary">
                            {{ __('head::clientCard.SendEmail') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </x-card>
@endsection

@push('styles')
    <style>
        .popover-body {
            max-height: 30rem;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        [id^="show-full-"] {
            padding: 0;
        }
    </style>
@endpush

@push('scripts')
    <script>
        $(function () {
            $('#smsMessageForm, #emailMessageForm').on('submit', function (e) {
                e.preventDefault();

                if (confirm("{{ __('head::clients-without-active-loan.messages.confirm', ['count' => $countOfFilteredClients]) }}")) {
                    $('button[form="smsMessageForm"], button[form="emailMessageForm"]').prop('disabled', true);
                    e.currentTarget.submit();
                    $(".preloader").show();
                }
            });

            $("#email_create_discount").on('change', function (e) {
                $("#email_discount_percent,#email_discount_product_ids\\[\\],#email_valid_from_to").prop(
                    'disabled', !$(this).prop('checked')
                );
                $('#email_discount_product_ids\\[\\]').selectpicker('refresh');
            });

            $("#sms_create_discount").on('change', function (e) {
                $("#sms_discount_percent,#sms_discount_product_ids\\[\\],#sms_valid_from_to").prop(
                    'disabled', !$(this).prop('checked')
                );
                $('#sms_discount_product_ids\\[\\]').selectpicker('refresh');
            });

            $('#actionBtn').on('click', function (event) {
                event.preventDefault();
                $('div#updateBuckets').toggleClass('d-none');
            });

            $('#genDocsBtn').on('click', function (event) {
                event.preventDefault();

                let checkboxes = $(".loan-buckets-checkbox");
                let checkedCheckboxes = checkboxes.filter(":checked");

                if (checkedCheckboxes.length > 0) {
                    const genDocsURL = '{{ route('collect.legal-docs.choose')}}';
                    let $loans = [];

                    $('form#changeBucketForm input.loan-buckets-checkbox').filter(':checked').each(function ($index, $row) {
                        return $loans[$index] = $($row).val();
                    });

                    // loan-buckets-checkbox
                    window.location.replace(genDocsURL + '/?loans[]=' + $loans.join('&loans[]='));
                } else {
                    alert('Моля изберете кредити, за да генерирате документи за тях');
                }
            });

            $('#changeBucketForm').on('submit', function (event) {
                let checkboxes = $(".loan-buckets-checkbox");
                let checkedCheckboxes = checkboxes.filter(":checked");

                if (checkedCheckboxes.length === 0) {
                    event.preventDefault();
                    alert('Моля изберете кредити, за да приложете действие към тях');
                }
            });

            $('#exportBtn').on('click', function (event) {
                event.preventDefault();

                const exportUrlMain = '{{ route('collect.loan-buckets.export') }}';
                const formData = $('#BucketLoanFilterForm').serializeArray();

                window.location = exportUrlMain + '?' + $.param(formData);
            });

            $('.comment-text').each(function (i) {
                let text = $(this).text();
                if (text.length >= 100) {
                    $(this).html(text.slice(0, 100) + `
                        <a type="button" class="btn btn-link btn-sm" href="#!" id="show-full-${i}" data-toggle="popover"
                            data-trigger="focus" data-content="${text.replace(/"/g, '&quot;')}">...</a>
                    `);
                }
            })
        });

        document.getElementById('BucketLoanFilterForm').addEventListener('submit', function(event) {
            var submitButton = this.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            $('#changeBucketForm').html('<tbody><tr><td>Зареждане на данните..</td></tr></tbody>');
        });
    </script>
@endpush
