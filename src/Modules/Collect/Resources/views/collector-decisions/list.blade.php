@extends('layouts.app')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="collectorDecisionForm" class="form-inline card-body"
                      action="{{ route('collect.collector-decisions.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">
                        <div class="col-lg-2 mb-3">
                            <select name="type" class="form-control w-100 mb-3" type="text">
                                <option value="">{{__('table.FilterByType')}}</option>
                                @foreach($collectorDecisionTypes as $collectorDecisionType)
                                    <option value="{{$collectorDecisionType}}">{{__('collect::decisions.' . $collectorDecisionType)}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <x-select-active active="{{ session($cacheKey . '.active') }}"/>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="createdAt" class="form-control w-100"
                                   id="createdAt"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                                   placeholder="{{__('table.FilterByCreatedAt')}}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="updatedAt"
                                   class="form-control w-100" id="updatedAt"
                                   value="{{ session($cacheKey . '.updatedAt') }}"
                                   placeholder="{{__('table.FilterByUpdatedAt')}}">
                        </div>
                        <div class="col-lg-12">
                            <x-btn-filter/>
                            <div id="btns-panel" class="mb-3">
                                <x-btn-create
                                    url="{{ route('collect.collector-decisions.create') }}"
                                    name="{{ __('btn.Create') }}"
                                />
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive" id="collectorTable">
                        @include('collect::collector-decisions.list-table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script src="{{ asset('dist/js/bootstrap-select.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script>
        loadDateRangePicker($("#createdAt, #updatedAt"));
        let clientControllerUrl = '{{ route('collect.collector-decisions.refresh') }}';
        let formId = $("#collectorDecisionForm");
        let tableId = $('#collectorTable');
        loadSimpleDataGrid(clientControllerUrl,formId,tableId);
    </script>
@endpush()
