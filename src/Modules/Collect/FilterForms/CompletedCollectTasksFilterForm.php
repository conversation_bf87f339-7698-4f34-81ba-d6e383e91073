<?php

namespace Modules\Collect\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;

final class CompletedCollectTasksFilterForm extends BaseFilterForm
{
    protected static string $route = 'collect.completed-tasks';

    public function buildForm(): void
    {
        if (getAdmin()->hasPermissionTo('collect.completed-tasks.export')) {
            $this->setFormOptions(['exportRoute' => 'collect.completed-tasks.export']);
        }

        $this->addDateFilterFromTo('created_at', __('table.FilterByDateCreated'))
            ->addDateFilterFromTo('finished_at', __('table.processedFinish'));
    }
}
