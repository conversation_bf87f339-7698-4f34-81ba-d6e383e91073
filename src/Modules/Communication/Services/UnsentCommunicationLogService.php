<?php

declare(strict_types=1);

namespace Modules\Communication\Services;

use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Models\UnsentCommunicationLog;

final class UnsentCommunicationLogService
{
    public function createBySmsTemplateId(array $data, int $templateId): UnsentCommunicationLog
    {
        $template = new SmsTemplate();
        $template->sms_template_id = $templateId;

        return $this->create($data, $template);
    }

    public function createByEmailTemplateId(array $data, int $templateId): UnsentCommunicationLog
    {
        $template = new EmailTemplate();
        $template->email_template_id = $templateId;

        return $this->create($data, $template);
    }

    public function create(array $data, SmsTemplate|EmailTemplate $template): UnsentCommunicationLog
    {
        $model = new UnsentCommunicationLog();

        if (isset($data['reason'])) {
            $data['reason'] = mb_strcut($data['reason'], 0, 250);
        }

        $model->fill([
            'created_by' => getAdminId(),
            ...$data,
            'communication_type' => match ($template::class) {
                SmsTemplate::class => 'sms',
                EmailTemplate::class => 'email',
            }
        ]);

        $model->templatable()->associate($template);

        if (!$model->save()) {
            throw new RuntimeException('Failed to create unsent communication log');
        }

        return $model;
    }
}
