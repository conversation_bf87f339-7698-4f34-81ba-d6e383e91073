@extends('layouts.app')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="viberTemplateForm" class="form-inline card-body"
                      action="{{ route('communication.viberTemplate.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">
                        <div class="col-lg-2 mb-3">
                            <input name="key" class="form-control w-100 mb-3" type="text"
                                   placeholder="{{__('table.FilterByName')}}"
                                   value="{{ session($cacheKey . '.key') }}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <x-select-communication-type
                                :typeOptions="$getViberTypes"
                                type="{{ old('type') ?? (session($cacheKey . '.type') ?? '') }}"
                                componentName="type"/>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <x-select-active active="{{ session($cacheKey . '.active') }}"/>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="createdAt" class="form-control w-100"
                                   id="createdAt"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                                   placeholder="{{__('table.FilterByCreatedAt')}}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="updatedAt"
                                   class="form-control w-100" id="updatedAt"
                                   value="{{ session($cacheKey . '.updatedAt') }}"
                                   placeholder="{{__('table.FilterByUpdatedAt')}}">
                        </div>
                        <div class="col-lg-12">
                            <x-btn-filter/>
                            <div id="btns-panel" class="mb-3">
                                <x-btn-create url="{{ route('communication.viberTemplate.create') }}"
                                              name="{{ __('btn.Create') }}"/>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive" id="viberTemplateTable">
                        @include('communication::viber-template.list-table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script>
        loadDateRangePicker($("#createdAt, #updatedAt"));
        let viberRefreshUrl = '{{ route('communication.viberTemplate.refresh') }}';
        let formId = $('#viberTemplateForm');
        let tableId = $('#viberTemplateTable');
        loadSimpleDataGrid(viberRefreshUrl, formId, tableId);
    </script>
@endpush()
