<?php

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Communication\Models\SmsTemplate;

class SmsSendRequest extends BaseRequest
{
    /**
     * @return string[]
     */
    public function rules(): array
    {
        return [
            'sms_template_id' => 'required|numeric|exists:' . (new SmsTemplate())->getTable() . ',sms_template_id',
            'loan_id' => 'required|numeric|exists:loan,loan_id',
            'phone' => 'nullable',
        ];
    }
}
