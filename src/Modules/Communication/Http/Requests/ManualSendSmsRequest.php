<?php

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class ManualSendSmsRequest extends BaseRequest
{

    public function rules(): array
    {
        return [
            'importClients' => 'nullable|file|required_without:clientId',
            'clientId.*' => 'nullable|numeric|required_without:importClients',
            'sms_message' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'importClients.file' => 'Невалиден файл',
            'importClients.required_without' => 'Полето е задължително',

            'clientId.numeric' => 'ClientID може да съдържа само цифри',
            'clientId.required_without' => 'Полето е задължително',

            'sms_message.required' => 'Полето е задължително',
            'sms_message.string' => 'Невалиден стринг',
        ];
    }
}
