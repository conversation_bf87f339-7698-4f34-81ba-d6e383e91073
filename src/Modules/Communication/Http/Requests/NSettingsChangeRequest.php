<?php

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Head\Http\Commands\UpdateNotificationSettingsCommand;
use Modules\Sales\Http\Dto\NotificationSettingDto;

class NSettingsChangeRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'value' => 'required',
            'type' => 'min:2|max:100',
            'channel' => 'min:2|max:100',
        ];
    }

    /** @return NotificationSettingDto[] */
    public function asDtoArr(): array
    {
        $v = $this->validated();
        //TODO: Remove IF as soon as <PERSON><PERSON> ensures the array of arrays
        if(isset($v['value'])){
            $v['value'] = $v['value']=='true' ? 1 : 0;
            return [NotificationSettingDto::from($v)];
        }

        $a = [];
        foreach ($v as $notif) {
            $notif['value'] = $notif['value']=='true' ? 1 : 0;
            $a[] = NotificationSettingDto::from($notif);
        }
        return $a;
    }

    public function asCommand(int $clientId)
    {
        return new UpdateNotificationSettingsCommand(
            $clientId,
            $this->asDtoArr()
        );
    }

}
