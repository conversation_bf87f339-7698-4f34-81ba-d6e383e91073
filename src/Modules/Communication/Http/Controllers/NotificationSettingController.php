<?php

namespace Modules\Communication\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Response;
use Modules\Common\CommandBus\CommandBus;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\NotificationSetting;
use Modules\Communication\Http\Requests\ClientNSettingsRequest;
use Modules\Communication\Http\Requests\NotificationSettingEditRequest;
use Modules\Communication\Http\Requests\NSettingsChangeRequest;
use Modules\Communication\Repositories\NotificationSettingRepository;

class NotificationSettingController extends BaseController
{
    private CommandBus $bus;
    private NotificationSettingRepository $repo;

    public function __construct(
        NotificationSettingRepository $repo,
        CommandBus                    $bus
    )
    {
        $this->repo = $repo;
        $this->bus = $bus;
        parent::__construct();
    }

    public function edit(NotificationSettingEditRequest $request): RedirectResponse
    {
        foreach ($request->validated()['notification'] as $id => $value) {
            $this->repo->updateById($id, $value);
        }
        return redirect()->back();
    }

    public function communicationTabEdit(
        NSettingsChangeRequest $request,
        NotificationSetting    $notificationSetting
    ): JsonResponse
    {
        $clientId = $notificationSetting->client_id;
        $this->bus->dispatch($request->asCommand($clientId));

        return Response::json(
            $this->repo->getAllByClientId($clientId),
            self::HTTP_OK
        );
    }

    public function updateClientNotificationSettings(
        ClientNSettingsRequest $request
    )
    {
        $clientId = $request->validated('clientId');

        $this->bus->dispatch($request->asCommand($clientId));

        return $this->backSuccess(__('Success update settings'));
    }
}
