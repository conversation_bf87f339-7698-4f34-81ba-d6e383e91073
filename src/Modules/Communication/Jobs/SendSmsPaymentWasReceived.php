<?php

namespace Modules\Communication\Jobs;

use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Office;
use Modules\Common\Models\Loan;
use Modules\Communication\Enums\SmsTemplateKeyEnum;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Services\SmsService;

class SendSmsPaymentWasReceived
{
    public function __construct(
        private readonly SmsService $smsService
    ) {}

    public function handle($event)
    {
        $loan = $event->loan;
        $this->sendForLoan($loan);
    }

    public function sendForLoan(Loan $loan, float $paymentAmount = 0)
    {
        $sms = $this->smsService->sendByTemplateKeyAndLoan(
            SmsTemplateKeyEnum::SMS_TYPE_RECEIVED_PAYMENT->value,
            $loan,
            [
                'payment_received_amount' => $paymentAmount,
            ]
        );

        if ($sms) {
            $message = 'Sms(' . SmsTemplateKeyEnum::SMS_TYPE_RECEIVED_PAYMENT->value . ') send successfully';
            Log::channel('smsExec')->info($message);
        } else {
            $message = 'Failed to send sms(' . SmsTemplateKeyEnum::SMS_TYPE_RECEIVED_PAYMENT->value . ')';
            Log::channel('smsExec')->info($message);
        }
    }
}
