<?php

declare(strict_types=1);

namespace Modules\Communication\Console;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Email;
use Modules\Common\Models\Sms;
use Modules\Communication\Enums\TemplatableType;
use Modules\Communication\Models\SendingStats;

final class UpdateManualMailingsStatsCommand extends CommonCommand
{
    protected $name = 'script:communication:update-manual-mailings-stats';
    protected $description = 'Update manual mailings stats';

    public function handle(): void
    {
        $this->startLog($this->description);

        $query = SendingStats::query()
            ->select(['id', 'templatable_type', 'templatable_id'])
            ->where(function ($q) {
                $q->whereNull('successful_count')
                  ->orWhere('successful_count', 0);
            })
            ->where('created_at', '>=', now()->subHours(20));

        $total = $query->count();
        $processed = 0;

        $query->chunkById(50, function (Collection $stats) use (&$processed) {
            DB::transaction(function () use ($stats, &$processed) {
                $stats->each(function (SendingStats $stat) use (&$processed) {
                    $type = TemplatableType::from($stat->templatable_type);
                    if ($this->isInProgress($stat->templatable_id, $type)) {
                        return;
                    }

                    $stat->update([
                        'successful_count' => $this->getMessagesCount($stat->templatable_id, $type, true),
                        'unsuccessful_count' => $this->getMessagesCount($stat->templatable_id, $type, false),
                        'unsent_count' => $stat->unsentLogs()->count(),
                    ]);

                    $processed++;
                });
            });
        }, 'id');

        $this->finishLog(['Total: ' . $total, 'Processed: ' . $processed]);
    }

    private function getMessagesCount(
        int $id,
        TemplatableType $type,
        bool $isSuccessful
    ): int {

        $query = $this->getMessagesQueryByTemplate($id, $type);

        $resp = Email::POSITIVE_RESPONSE;
        if ($type->name === 'Sms') {
            $resp = '{"status":1004,"desc":"Message accepted"}';
        }

        return $query->where('response', $isSuccessful ? '=' : '!=', $resp)->count();
    }

    private function isInProgress(int $id, TemplatableType $type): bool
    {
        $query = $this->getMessagesQueryByTemplate($id, $type);

        $sentAtField = match ($type) {
            TemplatableType::Email => 'send_at',
            TemplatableType::Sms => 'sent_at',
        };

        return $query->doesntExist() || $query->where($sentAtField, '>', now()->subMinutes(5))->exists();
    }

    public function getMessagesQueryByTemplate(int $id, TemplatableType $type): Builder
    {
        return match ($type) {
            TemplatableType::Email => Email::query()->where('email_template_id', $id),
            TemplatableType::Sms => Sms::query()->where('sms_template_id', $id),
        };
    }
}
