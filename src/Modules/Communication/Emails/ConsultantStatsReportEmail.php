<?php

namespace Modules\Communication\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Attachment;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ConsultantStatsReportEmail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public string $exportFilePath
    ) {
    }

    public function envelope(): Envelope
    {
        $env = strtoupper(env('APP_ENV'));
        $prj = strtoupper(env('PROJECT'));

        return new Envelope(
            subject: $prj . ' - ' . ' consultant stats. (' . $env . ')',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'communication::mail.consultant-stats.report',
        );
    }

    public function attachments()
    {
        return [
            Attachment::fromPath($this->exportFilePath)
        ];
    }
}
