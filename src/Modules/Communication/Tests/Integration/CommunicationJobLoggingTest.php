<?php

namespace Modules\Communication\Tests\Integration;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class CommunicationJobLoggingTest extends TestCase
{
    public function testLogEmail()
    {
        $dateSuffix = '-'. Carbon::now()->format('Y-m-d').'.log';
        Log::channel('emailExec')->info('test');
        $this->assertFileExists(storage_path('logs/email/exec'.$dateSuffix), 'test');
    }
}
