<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('marketing_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parent_id')->nullable()->constrained('marketing_tasks');
            $table->foreignId('client_id')->constrained('client', 'client_id');
            $table->integer('discount')->nullable();
            $table->json('variables')->nullable();
            $table->foreignId('last_repaid_loan_id')->nullable()->constrained('loan', 'loan_id');
            $table->integer('days_without_loan')->nullable();
            $table->integer('days_after_last_application')->nullable();
            $table->json('client_discount_actual_ids')->nullable();
            $table->foreignId('sale_task_id')->nullable()->constrained('sale_task', 'sale_task_id');
            $table->text('details')->nullable();

            $table->string('status')->default(\Modules\Communication\Enums\MarketingTaskStatusEnum::NEW->value);
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('created_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_tasks');
    }
};
