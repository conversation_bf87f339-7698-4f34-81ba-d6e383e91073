<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('marketing_tasks', static function (Blueprint $table) {
            $table->dropForeign('marketing_tasks_sale_task_id_foreign');
            $table->unique('sale_task_id');
        });
    }

    public function down(): void
    {
        Schema::table('marketing_tasks', static function (Blueprint $table) {
            $table->dropUnique('marketing_tasks_sale_task_id_unique');
            $table->foreign('sale_task_id')->references('sale_task_id')->on('sale_task');
        });
    }
};
