<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('email', static function (Blueprint $table) {
            $table->dropColumn('email_campaign_id');
        });

        Schema::dropIfExists('email_campaign');

        Schema::dropIfExists('email_source');
    }

    public function down(): void
    {
        //
    }
};
