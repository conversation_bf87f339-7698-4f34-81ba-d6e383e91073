<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('marketing_tasks_history', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->index();
            $table->unsignedBigInteger('parent_id')->index()->nullable();
            $table->unsignedBigInteger('client_id')->index();
            $table->integer('discount')->nullable();
            $table->json('variables')->nullable();
            $table->unsignedBigInteger('last_repaid_loan_id')->index()->nullable();
            $table->integer('days_without_loan')->nullable();
            $table->integer('days_after_last_application')->nullable();
            $table->json('client_discount_actual_ids')->nullable();
            $table->unsignedBigInteger('sale_task_id')->index()->nullable();
            $table->text('details')->nullable();

            $table->string('status');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('created_at')->nullable();

            $table->timestamp('archived_at')->useCurrent();
            $table->foreignId('archived_by')->constrained('administrator', 'administrator_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('marketing_tasks_history');
    }
};
