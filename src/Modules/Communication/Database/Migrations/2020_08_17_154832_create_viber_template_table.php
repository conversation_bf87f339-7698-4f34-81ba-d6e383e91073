<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;
use Modules\Communication\Models\ViberTemplate;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'viber_template',
            function ($table) {
                $table->bigIncrements('viber_template_id');
                $table->string('key');
                $table->string('description');
                $table->json('variables');
                $table->string('text');
                $table->enum('gender', config('communication.gender'))->nullable();
                $table->enum('type', ViberTemplate::templateTypes())->nullable();

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('viber_template');
    }
};
