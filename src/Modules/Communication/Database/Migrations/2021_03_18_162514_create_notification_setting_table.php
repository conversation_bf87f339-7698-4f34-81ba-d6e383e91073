<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\NotificationSetting;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'notification_setting',
            function ($table) {
                $table->bigIncrements('notification_setting_id');
                $table->integer('client_id')->unsigned()->nullable();
                $table->enum('type', NotificationSetting::getTypes())->nullable();
                $table->enum('channel',NotificationSetting::getChannels())->nullable();
                $table->tinyInteger('value')->nullable()->default(1);
                $table->tableCrudFields();
                $table->foreign('client_id')->references('client_id')->on('client');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'notification_setting',
            function (Blueprint $table) {
                $table->dropForeign('notification_setting_client_id_foreign');
            }
        );

        Schema::dropIfExists('notification_setting');
    }
};
