<?php

namespace Modules\Accounting\Mail;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Modules\Common\ModelFilters\Payment\TypeFilter;
use Modules\Common\Models\BankAccount;

final class AccountingStatsExported extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        private readonly string $userEmail,
        private readonly string $userFullName,
        private readonly array $userFilters,
        private readonly string $reportFileUrl
    ) {
    }

    /**
     * Get the message envelope.
     *
     * @return Envelope
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            to: $this->userEmail,
            subject: 'Accounting Stats Exported',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return Content
     */
    public function content(): Content
    {
        return new Content(
            view: 'accounting::email.stats',
            with: $this->getViewData(),
        );
    }

    private function getViewData(): array
    {
        $filters = [];
        $filterLabels = $this->getFilterLabels();

        foreach ($this->userFilters as $key => $value) {
            if (empty($value)) {
                continue;
            }

            $tableKey = $filterLabels[$key] ?? $key;

            if (!is_array($value)) {
                $filters[$tableKey] = match ($key) {
                    'officeId' => getOfficeName((int) $value),
                    'activityStatus' => $value ? __('table.Deleted') : __('table.Active'),
                    default => $value,
                };

                continue;
            }

            foreach ($value as $k => $v) {
                $filters[$tableKey][$k] = match ($key) {
                    'type' => TypeFilter::getTypeLabelsMap()[$v] ?? $v,
                    'source' => BankAccount::find($v)->name,
                    default => $v,
                };
            }
        }

        return [
            'adminNames' => $this->userFullName,
            'filters' => $filters,
            'createdAt' => Carbon::now()->format('Y-m-d H:i:s'),
            'url' => $this->reportFileUrl,
        ];
    }

    private function getFilterLabels(): array
    {
        return [
            'dateFromTo' => __('table.FilterByDateCreated'),
            'officeId' => __('table.Office'),
            'activityStatus' => __('table.Status'),
            'paymentId' => __('table.PaymentId'),
            'loanId' => __('table.LoanId'),
            'clientNames' => __('table.clientNames'),
            'amountFrom' => __('table.AmountFrom'),
            'amountTo' => __('table.AmountTo'),
            'type' => __('table.AccountingStatsType'),
            'source' => __('table.Source'),
        ];
    }
}
