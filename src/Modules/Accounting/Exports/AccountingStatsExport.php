<?php

namespace Modules\Accounting\Exports;

use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Models\Payment;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final class AccountingStatsExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithCustomChunkSize
{
    use Exportable;

    public function __construct(private readonly Builder $query)
    {
    }

    public function query(): Builder
    {
        return $this->query;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function headings(): array
    {
        return [
            __('table.Date'),
            __('table.Office'),
            __('table.AccountingStatsType'),
            __('table.Status'),
            __('table.PaymentId'),
            __('table.LoanId'),
            __('table.Juridical'),
            __('table.Client'),
            __('table.Source'),
            __('table.TotalPaid'),
            __('table.Principal'),
            __('table.AccountingStatsInterest'),
            __('table.Penalty'),
            __('table.AccountingStatsLateInterest'),
            __('table.OtherIncomes'),
        ];
    }

    /**
     * @param Payment $row
     */
    public function map($row): array
    {
        $statsType = match ($row->purpose) {
            PaymentPurposeEnum::SERVICE_FEE => __('accounting::stats.ServiceFee'),
            PaymentPurposeEnum::LOAN_EXTENSION => __('accounting::stats.LoanExtension'),
            PaymentPurposeEnum::LOAN_EARLY_REPAYMENT, PaymentPurposeEnum::LOAN_PAYMENT,
            PaymentPurposeEnum::REFUND, PaymentPurposeEnum::UNKNOWN => __('accounting::stats.LoanPayment'),
            default => null,
        };

        $statsType = $statsType ?? ($row->direction === PaymentDirectionEnum::IN
            ? __('accounting::stats.UndefinedPayment') : __('accounting::stats.LoanPayout'));

        $deliveryDetails = getExplainedPaymentDetails($row->getRawOriginal('delivery'));

        return [
            $row->significant_date,
            $row->loan?->office?->name,
            $statsType,
            $row->deleted_at ? __('table.Deleted') : __('table.Active'),
            $row->payment_id,
            $row->loan_id,
            $row->loan?->juridical ? __('table.Yes') : __('table.No'),
            $row->client?->getFullName(),
            $row->bankAccount?->name,
            intToFloat($row->amount),
            (string) $deliveryDetails['principal'],
            (string) $deliveryDetails['interest'],
            (string) $deliveryDetails['penalty'],
            (string) $deliveryDetails['late_interest'],
            (string) $deliveryDetails['late_penalty_and_taxes'],
        ];
    }
}
