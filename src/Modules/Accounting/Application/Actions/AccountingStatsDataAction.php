<?php

declare(strict_types=1);

namespace Modules\Accounting\Application\Actions;

use Carbon\CarbonImmutable;
use Modules\Accounting\Forms\AccountingStatsFilterForm;
use Modules\Accounting\Repositories\AccountingStatsRepository;

final readonly class AccountingStatsDataAction
{
    public function __construct(
        private AccountingStatsRepository $repository
    ) {
    }

    public function execute(array $filters = [], int $perPage = 10): array
    {
        if (!isset($filters['dateFromTo'])) {
            $today = CarbonImmutable::today()->format('d-m-Y');
            $filters['dateFromTo'] = "{$today} - {$today}";
        }

        return [
            'accountingStats' => $this->repository->getPaginatorByFilters($filters, $perPage),
            'accountingStatsFilterForm' => AccountingStatsFilterForm::create(),
        ];
    }
}
