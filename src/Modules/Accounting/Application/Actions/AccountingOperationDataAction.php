<?php

namespace Modules\Accounting\Application\Actions;

use Modules\Accounting\Forms\AccountingOperationsFilterForm;
use Modules\Accounting\Repositories\AccountingPaymentRepository;

readonly class AccountingOperationDataAction
{
    public function __construct(
        private AccountingPaymentRepository $repository
    ) {
    }

    public function execute(array $filters, int $perPage): array
    {
        $accountingPaymentRows = $this->repository->getBuilderByFilters($filters)->paginate($perPage);

        return [
            'rows' => $accountingPaymentRows,
            'filterForm' => AccountingOperationsFilterForm::create(),
        ];
    }
}
