@extends('layouts.app')

@php
    use Modules\Common\Enums\Payment\PaymentPurposeEnum;
    use Modules\Common\Enums\PaymentDirectionEnum;

    /**
     * @var \Modules\Common\Models\Payment[] $accountingStats
     */
@endphp

@section('content')
    <x-card-filter-form :filter-form="$accountingStatsFilterForm"/>

    <x-card title="{{ __('menu.Statistics') }}">
        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{ __('table.Date') }}</th>
                    <th>{{ __('table.Office') }}</th>
                    <th>{{ __('table.AccountingStatsType') }}</th>
                    <th>{{ __('table.Status') }}</th>
                    <th>{{ __('table.PaymentId') }}</th>
                    <th>{{ __('table.LoanId') }}</th>
                    <th>{{ __('table.Juridical') }}</th>
                    <th>{{ __('table.Client') }}</th>
                    <th>{{ __('table.Source') }}</th>
                    <th>{{ __('table.TotalPaid') }}</th>
                    <th>{{ __('table.Principal') }}</th>
                    <th>{{ __('table.AccountingStatsInterest') }}</th>
                    <th>{{ __('table.Penalty') }}</th>
                    <th>{{ __('table.AccountingStatsLateInterest') }}</th>
                    <th>{{ __('table.OtherIncomes') }}</th>
                </tr>
            </x-slot:head>
            @foreach($accountingStats as $item)
                @php
                    $statsType = match ($item->purpose) {
                        PaymentPurposeEnum::SERVICE_FEE => __('accounting::stats.ServiceFee'),
                        PaymentPurposeEnum::LOAN_EXTENSION => __('accounting::stats.LoanExtension'),
                        PaymentPurposeEnum::LOAN_EARLY_REPAYMENT, PaymentPurposeEnum::LOAN_PAYMENT,
                        PaymentPurposeEnum::REFUND, PaymentPurposeEnum::UNKNOWN => __('accounting::stats.LoanPayment'),
                        default => null,
                    };

                    $statsType = $statsType ?? ($item->direction === PaymentDirectionEnum::IN
                        ? __('accounting::stats.UndefinedPayment') : __('accounting::stats.LoanPayout'));

                    $deliveryDetails = getExplainedPaymentDetails($item->getRawOriginal('delivery'));
                @endphp
                <tr>
                    <td>{{ $item->significant_date }}</td>
                    <td>{{ $item->loan?->office?->name }}</td>
                    <td>{{ $statsType }}</td>
                    <td>{{ $item->deleted_at ? __('table.Deleted') : __('table.Active') }}</td>
                    <td>{{ $item->payment_id }}</td>
                    <td>{{ $item->loan_id }}</td>
                    <td>{{ $item->loan?->juridical ? __('table.Yes') : __('table.No') }}</td>
                    <td>{{ $item->client?->getFullName() }}</td>
                    <td>{{ $item->bankAccount?->name }}</td>
                    <td>{{ intToFloat($item->amount) }}</td>
                    <td>{{ $deliveryDetails['principal'] ?? null }}</td>
                    <td>{{ $deliveryDetails['interest'] ?? null }}</td>
                    <td>{{ $deliveryDetails['penalty'] ?? null }}</td>
                    <td>{{ $deliveryDetails['late_interest'] ?? null }}</td>
                    <td>{{ $deliveryDetails['late_penalty_and_taxes'] ?? null }}</td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$accountingStats"/>
    </x-card>
@endsection

@push('scripts')
    <script>
        $(() => $('#exportBtn').click(function (event) {
            $(this).addClass("disabled");
            $(this).attr("aria-disabled", true);
            event.preventDefault();
            const form = $('#AccountingStatsFilterForm');
            form.attr('action', $(this).attr('href'));
            form.submit();
            form.attr('action', form.attr('action'));
        }));
    </script>
@endpush
