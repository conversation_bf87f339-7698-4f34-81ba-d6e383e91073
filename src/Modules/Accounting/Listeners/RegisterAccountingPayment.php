<?php

namespace Modules\Accounting\Listeners;

use Modules\Accounting\Services\AccountingService;
use Modules\Payments\Domain\Events\LoanPayoutWasDelivered;
use Modules\Payments\Domain\Events\LoanPayInWasDelivered;
use Modules\Payments\Domain\Events\LoanPaymentWasDeleted;

// - влизаме тука при:
// - отпускане на заем, създаваме изходящо плащане в статус DELIVERED за Банк и Кеш, или SENT за Изипей
// - при получаване на входящо плащане, след разнасяне
// - при REFUND на Изипей, създаваме входящо плащане за баланс
// - при изтриване на плащаме, правим отрицателен счетоводен запис
class RegisterAccountingPayment
{
    public function handle(
        LoanPayoutWasDelivered // out
        |LoanPayInWasDelivered // in
        |LoanPaymentWasDeleted
         $event
    ) {
        // delete flow
        if ($event instanceof LoanPaymentWasDeleted) {
            AccountingService::addAccountingRowForDeletedPayment($event->payment);

            return ;
        }


        // normal flow
        AccountingService::addAccountingRowForCreatedPayment($event->payment);
    }
}
