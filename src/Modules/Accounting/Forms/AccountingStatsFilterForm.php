<?php

declare(strict_types=1);

namespace Modules\Accounting\Forms;

use Carbon\CarbonImmutable;
use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\BankAccount;

final class AccountingStatsFilterForm extends BaseFilterForm
{
    private const DATE_FROM_TO_FILTER_NAME = 'dateFromTo';

    protected static string $route = 'acc.stats.index';

    public function buildForm(): void
    {
        $this->addDateFilterFromTo(self::DATE_FROM_TO_FILTER_NAME, __('table.FilterByDateCreated'))
            ->addOfficeIdsFilter('loan_office_ids')
            ->addSimpleSelectFilter('activityStatus', [
                '1' => __('table.Active'),
                '0' => __('table.Deleted')
            ], __('table.Status'))
            ->add('paymentId', 'text', ['label' => __('table.PaymentId')])
            ->addLoanIdFilter('loanId')
            ->addClientNameFilter()
            ->addAmountFilterFromTo('amountFrom', 'amountTo')
            ->add('type', 'select', [
                'label' => __('table.AccountingStatsType'),
                'empty_value' => '',
                'attr' => ['data-boostrap-selectpicker' => 'true', 'multiple' => 'true'],
                'selected' => $this->request->get('type', ''),
                // strict order of elements of the "choices" array
                'choices' => [
                    __('accounting::stats.ServiceFee'),
                    __('accounting::stats.LoanExtension'),
                    __('accounting::stats.LoanPayment'),
                    __('accounting::stats.LoanPayout'),
                    __('accounting::stats.UndefinedPayment'),
                ],
            ])->add('source', 'select', [
                'label' => __('table.Source'),
                'empty_value' => '',
                'attr' => ['data-live-search' => 'true', 'multiple' => 'true'],
                'selected' => $this->request->get('source', ''),
                'choices' => BankAccount::query()->where('active', 1)->get(['bank_account_id', 'name'])->pluck(
                        'name',
                        'bank_account_id'
                    )->toArray(),
            ]);

        $this->configure();
    }

    private function configure(): void
    {
        if (getAdmin()->hasPermissionTo('acc.stats.export')) {
            $this->setFormOptions([
                'exportRoute' => 'acc.stats.export',
            ]);
        }

        $today = CarbonImmutable::today()->format('d-m-Y');
        $this->modify(self::DATE_FROM_TO_FILTER_NAME, 'text', [
            'value' => $this->request->get('dateFromTo', "$today - $today"),
            'attr' => [
                'data-max-daterange' => $today,
            ],
        ]);
    }
}
