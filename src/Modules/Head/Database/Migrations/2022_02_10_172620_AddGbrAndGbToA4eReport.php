<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

class AddGbrAndGbToA4eReport extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'a4e_report',
            function ($table) {
                $table->string('gbr', 255)->nullable();
                $table->string('gb', 255)->nullable();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
