<?php

declare(strict_types=1);

namespace Modules\Head\Forms;

use Modules\Common\FilterForms\BaseFilterForm;

final class OverdueStatsPhysicalOfficesFiltersForm extends BaseFilterForm
{
    protected static string $route = 'head.clients.overdue-stats-physical-offices';

    public function buildForm(): void
    {
        $this->addSimpleFilter('loan_id', __('table.LoanId'))
            ->addSimpleFilter('client_name', __('table.ClientFullName'))
            ->addSimpleFilter('client_pin', __('table.Pin'))
            ->addSimpleFilter('client_phone', __('table.Phone'))
            ->addSimpleFilter('first_guarantor_name', __('table.GuarantorFullName') . ' 1')
            ->addSimpleFilter('second_guarantor_name', __('table.GuarantorFullName') . ' 2')
            ->addSimpleFilter('from_overdue_amount', __('table.FromOverdueAmount'))
            ->addSimpleFilter('to_overdue_amount', __('table.ToOverdueAmount'))
            ->addSimpleFilter('from_overdue_days', __('table.FromOverdueDays'))
            ->addSimpleFilter('to_overdue_days', __('table.ToOverdueDays'));

        $this->configure();
    }

    private function configure(): void
    {
        if (getAdmin()->hasPermissionTo('head.clients.without-active-loan.export')) {
            $this->setFormOptions([
                'exportRoute' => 'head.clients.overdue-stats-physical-offices.export',
            ]);
        }
    }
}
