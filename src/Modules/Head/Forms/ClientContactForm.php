<?php

namespace Modules\Head\Forms;

use <PERSON>\LaravelFormBuilder\Form;

class ClientContactForm extends Form
{

    /**
     * @return void
     */
    public function buildForm(): void
    {
        $this
            ->add('name', 'text', [
                'label' => __('table.Name'),
                'attr' => [
                    'required' => 'required',
                    'maxlength' => 40,
                    'data-parsley-pattern' => "/^[a-zA-Zа-яА-Я\s-]+$/i",
                    'data-parsley-trigger' => 'keyup',
                ]
            ])
            ->add('phone', 'text', [
                'label' => __('table.Phone'),
                'attr' => [
                    'required' => 'required',
                    'minlength' => 5,
                    'maxlength' => 20,
                    'data-parsley-pattern' => config('validation.requestRules.commonPhoneParsley'),
                    'data-parsley-pattern-message' => __('Невалиден телефонен номер.'),
                ]
            ]);


        /// add hidden fields
        $this->add('contact_type_id', 'hidden', [
            'value' => 1
        ]);
        if ($this->getData('client_id', false)) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }

        if ($this->getData('loan_id', false)) {
            $this->add('loan_id', 'hidden', [
                'value' => $this->getData('loan_id')
            ]);
        }

        if ($this->getData('seq_num') !== null) {
            $this->add('seq_num', 'hidden', [
                'value' => $this->getData('seq_num')
            ]);
        }
    }
}
