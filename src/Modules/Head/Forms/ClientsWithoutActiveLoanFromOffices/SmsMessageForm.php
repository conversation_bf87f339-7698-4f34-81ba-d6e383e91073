<?php

declare(strict_types=1);

namespace Modules\Head\Forms\ClientsWithoutActiveLoanFromOffices;

use <PERSON>\LaravelFormBuilder\Field;
use <PERSON>\LaravelFormBuilder\Form;
use Modules\Product\Repository\ProductRepository;

final class SmsMessageForm extends Form
{
    public function buildForm(): void
    {
        $this->setUrl(route('head.clients.offices.without-active-loan.send-message.sms'))
            ->setMethod('post')
            ->add('message', Field::TEXTAREA, ['label' => __('table.Message'), 'required' => true])
            ->add('sms_create_discount', 'checkbox', ['label' => __('table.DiscountCreation')])
            ->add('sms_discount_product_ids', 'select', [
                'label' => __('table.Product'),
                'empty_value' => '',
                'selected' => '',
                'choices' => app(ProductRepository::class)->getProductsByAdmin()
                    ->pluck('name', 'product_id')->toArray(),
                'attr' => [
                    'data-live-search' => 'true',
                    'multiple' => true,
                    'required' => true,
                    'disabled' => true,
                ],
            ])
            ->add('sms_valid_from_to', 'text', [
                'label' => __('table.ValidFromTill'),
                'attr' => [
                    'data-daterange-picker' => 'true',
                    'data-min-daterange' => now()->format('d-m-Y'),
                    'data-drops' => 'up',
                    'required' => true,
                    'disabled' => true,
                ]
            ])
            ->add('sms_discount_percent', 'number', [
                'label' => __('table.DiscountPercent'),
                'attr' => [
                    'min' => 1,
                    'max' => 100,
                    'step' => 1,
                    'required' => true,
                    'disabled' => true,
                ]
            ])
            ->add('filters', Field::HIDDEN, ['value' => serialize($this->getData('filters', []))]);
    }
}
