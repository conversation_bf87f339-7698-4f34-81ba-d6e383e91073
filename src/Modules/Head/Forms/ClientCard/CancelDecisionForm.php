<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;

class CancelDecisionForm extends Form
{

    public function buildForm(): void
    {
        $decisionReasons = __("payments::paymentTaskDecisionReason");
        if ($this->request->route('task') === 'sales') {
            unset(
                $decisionReasons['fake_request'],
                $decisionReasons['no_income'],
                $decisionReasons['family_relation'],
            );
        }

        $this
            ->add('approve_decision_reasons_id', 'select', [
                'label' => __('head::clientCard.Choose'),
                'empty_value' => __('table.SelectOption'),
                'choices' => $decisionReasons,
                'attr' => [
                    'required' => 'required'
                ]
            ])
            ->add('details', 'textarea', [
                'label' => __('head::clientCard.Comment'),
                'attr' => [
                    'rows' => 4
                ]
            ]);

        /// todo переделать ето не ок
        $this->add('decision', 'hidden', [
            'value' => ApproveDecision::APPROVE_DECISION_CANCELED
        ]);
        $this->add('client_id', 'hidden', [
            'value' => $this->getData('client_id', 0)
        ]);
        $this->add('loan_id', 'hidden', [
            'value' => $this->getData('loan_id', 0)
        ]);
    }
}
