<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\CollectorDecision;

class CollectCallMeLaterForm extends Form
{
    public function buildForm(): void
    {
        $this->add('loan_id', 'hidden', [
            'value' => $this->getData('loan_id', 0)
        ]);

        $this->add('collector_decision_id', 'hidden', [
            'value' => CollectorDecision::CALL_LATER
        ]);

        $this
            ->add('show_after', 'text', [
                'label' => __('table.Date'),
                'attr' => [
                    'required' => 'required',
                    'autocomplete' => 'off',
                    'data-date-picker-callmelater' => 'true'
                ]
            ])
            ->add('call_after_number', 'text', [
                'label' => __('head::clientCard.OrAfter'),
                'attr' => [
                    'required' => 'required',
                    'autocomplete' => 'off',
                    'data-parsley-pattern' => '^[0-9]*$',
                    'data-parsley-pattern-message' => __('Minutes accepts numbers only')
                ]
            ]);
    }
}
