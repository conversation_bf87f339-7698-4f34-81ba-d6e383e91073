<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;

class ResetDueDatesForm extends Form
{
    public function buildForm(): void
    {
        $this->add('new_date', 'text', [
            'label' => __('head::clientCard.resetDueDatesMakeChoice'),
            'value' => '',
            'attr' => [
                'required' => 'required',
                'data-date-picker' => 'true',
                'autocomplete' => 'off'
            ]
        ]);

        if ($this->getData('loan_id')) {
            $this->add('loan_id', 'hidden', [
                'value' => $this->getData('loan_id')
            ]);
        }
    }
}
