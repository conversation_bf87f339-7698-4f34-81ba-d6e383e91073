<?php

namespace Modules\Head\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientRepresentor;
use Modules\Common\Repositories\BaseRepository;

class ClientRepresentorRepository extends BaseRepository
{

    public function save(ClientRepresentor $cr): ?ClientRepresentor
    {
        ClientRepresentor::where(['client_id' => $cr->client_id, 'last' => 1])->update(['last' => 0]);
        return $cr->save() ? $cr : null;
    }

    public function getLastByClient(Client $client): ?ClientRepresentor
    {
        return ClientRepresentor::where([
            'client_id' => $client->getKey(),
            'last'=>1,
            'active'=>1,
            'deleted'=>0
        ])->first();
    }
}