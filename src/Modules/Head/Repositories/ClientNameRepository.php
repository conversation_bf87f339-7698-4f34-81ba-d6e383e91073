<?php

namespace Modules\Head\Repositories;

use Mo<PERSON>les\Api\Http\Dto\UpdateIdCardDto;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientName;
use Modules\Common\Repositories\BaseRepository;

class ClientNameRepository extends BaseRepository
{
    public function __construct(
        protected ClientName $clientName
    )
    {
    }

    /**
     * @param Client $client
     * @param UpdateIdCardDto $dto
     * @return void
     * @uses \Modules\Api\Application\Actions\UpdateIdCardDataAction
     */
    public function createIfNotExistsAndUpdateByClient(Client $client, UpdateIdCardDto $dto): void
    {
        /// 1st if client never have log in client_names
        /// create log from client db model values
        if (!$this->hasClientNames($client->getKey())) {
            $this->create([
                'client_id' => $client->getKey(),
                'first_name' => $client->first_name,
                'middle_name' => $client->middle_name,
                'last_name' => $client->last_name,
            ]);
        }

        /// 2st create income client names
        /// standart create log incoming names
        $this->create([
            'client_id' => $dto->client_id,
            'first_name' => $dto->first_name,
            'middle_name' => $dto->middle_name,
            'last_name' => $dto->last_name,
        ]);

        /// 3st update client names in table client
        $client->setAttribute('first_name', $dto->first_name);
        $client->setAttribute('middle_name', $dto->middle_name);
        $client->setAttribute('last_name', $dto->last_name);
        $client->saveQuietly();
    }

    public function hasClientNames(int $clientId): bool
    {
        return $this->clientName->where('client_id', $clientId)->count() > 0;
    }

    public function getByClientId(int $clientId): ?ClientName
    {
        return ClientName::where(
            [
                'client_id' => $clientId,
                'last' => 1
            ]
        )->first();
    }

    /**
     * @param Client $client
     */
    public function updateClientName(Client $client)
    {
        $this->clientName::where(
            [
                'client_id' => $client->client_id,
                'last' => 1,
            ]
        )->update(['last' => 0]);

        $clientName = new ClientName();
        $clientName->fill(
            [
                'client_id' => $client->client_id,
                'first_name' => $client->first_name,
                'middle_name' => $client->middle_name,
                'last_name' => $client->last_name,
                'last' => 1,
            ]
        );

        $clientName->save();
    }

    public function getLastByClient(Client $client): ?ClientName
    {
        return ClientName::where([
            'client_id' => $client->getKey(),
            'last' => 1,
            'active' => 1,
            'deleted' => 0
        ])->first();
    }

    public function setLastToZeroForOthers(ClientName $clientName)
    {
        $this->clientName
            ->where('client_name_id', '!=',
                $clientName->exists ? $clientName->getKey() : 0
            )
            ->where('client_id', '=', $clientName->client_id)
            ->where('last', '=', 1)
            ->update(['last' => 0]);
    }

    public function create(array $data): ?ClientName
    {
        $this->clientName->fill($data);
        return $this->save($this->clientName);
    }

    public function save(ClientName $clientName): ?ClientName
    {
        $this->setLastToZeroForOthers($clientName);
        $clientName->last = 1;
        $clientName->active = 1;
        return $clientName->save() ? $clientName : null;
    }
}
