<?php

namespace Modules\Head\Repositories\Loan;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanGuarantActual;
use Modules\Common\Models\LoanGuarantHistory;
use Modules\Common\Repositories\BaseRepository;

class LoanGuarantRepository extends BaseRepository
{
    public function getByLoanIdAndGuarantId(int $loanId, int $guarantId): ?LoanGuarantActual
    {
        return LoanGuarantActual::where(['loan_id' => $loanId, 'guarant_id' => $guarantId])->first();
    }

    private function copyToHistory(LoanGuarantActual $lgActual)
    {
        $h = new LoanGuarantHistory();
        $h->fill($lgActual->getOriginal());
        $h->save();
    }

    public function save(LoanGuarantActual $lgActual): ?LoanGuarantActual
    {
        if($lgActual->exists && $lgActual->isDirty()){
            $this->copyToHistory($lgActual);
        }
        return $lgActual->save()? $lgActual : null;
    }


    public function getAllByLoanId(int $loanId): Collection
    {
        return LoanGuarantActual::where(['loan_id'=>$loanId])->get();
    }

    public function moveToHistory(Loan $loan, ?Collection $loanGuarantors = null)
    {
        if(! $loanGuarantors) {
            $loanGuarantors = $this->getAllByLoanId($loan->getKey());
        }
        /** @var LoanGuarantActual $cc */
        foreach ($loanGuarantors as $lg){
            $this->copyToHistory($lg);
            $lg->delete();
        }
    }

    public function remove(LoanGuarantActual $lg): void
    {
        $this->copyToHistory($lg);
        $lg->delete();
    }
}