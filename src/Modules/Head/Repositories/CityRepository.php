<?php

namespace Modules\Head\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\City;
use Modules\Common\Repositories\BaseRepository;

class CityRepository extends BaseRepository
{
    public function getAll(): Collection
    {
        return City::orderBy('name', 'ASC')->get();
    }

    public static function getAllActiveWithIdAsKey(): array
    {
        $rows = City::where('active', '1')
            ->with('municipality')
            ->orderBy('name', 'ASC')
            ->get();

        $cities = [];
        $rows->map(function (City $city) use (&$cities) {
            if (str_contains($city->name, 'с.')) {
                $cities[$city->getKey()] = "{$city->name}, общ. {$city->municipality?->name}, обл {$city->municipality?->area?->name}";
            } else {
                $cities[$city->getKey()] = $city->name;
            }
        });

        return $cities;
    }

    public function getCityByCode(string $code): ?City
    {
        return City::where('code', $code)->first();
    }

    public function getCityByName(string $name): Collection
    {
        return City::where('name', 'ILIKE', "%{$name}%")
            ->limit(10)
            ->orderBy('name', 'ASC')
            ->get();
    }

    public function getCachedCityByName(string $name): ?City
    {
        return Cache::get('city.' . strtolower($name), function () use ($name) {
            return $this->getOneCityByName($name);
        });
    }

    public function getOneCityByName(string $name): ?City
    {
        $city = City::where('name', 'ILIKE', "%{$name}%")->first();

        Cache::put('city.' . strtolower($name), $city, 500);

        return $city;
    }
}
