<?php

namespace Modules\Head\Repositories;

use Illuminate\Support\Collection;
use Modules\Common\Models\Bank;
use Modules\Common\Repositories\BaseRepository;

class BankRepository extends BaseRepository
{
    public function __construct(
        protected Bank $bank
    )
    {
    }

    public function getAll(): Collection
    {
        return $this->bank->orderBy('bank_id', 'DESC')->get();
    }

    public function getBankById(int $id): ?Bank
    {
        return $this->bank->where('bank_id', $id)->first();
    }

    public function getBankByBic(string $bic): ?Bank
    {
        return $this->bank->where('bic', 'like', '%' . $bic . '%')->first();
    }
}
