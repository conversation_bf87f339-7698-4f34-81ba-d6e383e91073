<?php

namespace Modules\Head\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Collect\Domain\Entities\Installments;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Head\Application\Actions\RecalculateClientStatsAction;
use Modules\Head\Application\Actions\RecalculateLoanStatsAction;
use Modules\Payments\Application\Actions\RecalculatePaymentStatsAction;

class RecalculateStatsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $logChannel = 'statsRecalculationChanges';
    private $clientAction = null;
    private $loanAction = null;
    private $paymentAction = null;
    private $installments = null;

    public function __construct(
        public Collection $clients
    ) {
        $this->clientAction  = app(RecalculateClientStatsAction::class);
        $this->loanAction    = app(RecalculateLoanStatsAction::class);
        $this->paymentAction = app(RecalculatePaymentStatsAction::class);
        // $this->installmentsDomain  = app(Installments::class);
    }

    public function handle(): void
    {
        foreach ($this->clients as $client) {
            $this->recalculateClient($client);
        }
    }

    public function recalculateClient(? Client $client): void
    {
        if (!$client) {
            return;
        }

        $changes = $this->clientAction->execute($client);
        Log::channel($this->logChannel)->info($changes);

        foreach ($client->loans as $loan){
            $this->recalculateLoan($loan);
        }
    }

    public function recalculateLoan(?Loan $loan): void
    {
        if (!$loan) {
            return;
        }

        $changes = $this->loanAction->execute($loan);
        Log::channel($this->logChannel)->info($changes);

        // INFO: commented because installment do not have stats!
        // app(Installments::class)->buildFromExisting(
        //     $loan->installments,
        //     $loan->getCredit()->freshInstallmentCollection()
        // )->recalculate();

        foreach ($loan->payments as $payment){
            $this->recalculatePayment($payment);
        }

    }

    public function recalculatePayment(?Payment $payment): void
    {
        if (!$payment || $payment->direction !== PaymentDirectionEnum::IN) {
            return;
        }

        $changes = $this->paymentAction->execute($payment);
        Log::channel($this->logChannel)->info($changes);
    }
}
