<?php

namespace Modules\Head\Jobs;

use Modules\ThirdParty\Services\CurlEasyPayService;
use \Throwable;
use Carbon\Carbon;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Models\Payment;

class EasypayRefundStateJob extends CommonJob
{
    const DELAY = 0;
    const MAX_ATTEMPT_COUNT = 3;

    private $payment = null;
    private $attempt = null;

    protected $logChannel = 'easypay';
    protected $queueName = 'easypay';

    public function __construct(
        Payment $payment = null,
        int     $attempt = 1
    )
    {
        $this->payment = $payment;
        $this->attempt = $attempt;
    }

    public function handle()
    {
        if (empty($this->payment->payment_id)) {
            $this->log('There is no payment');
            return false;
        }


        try {

            $curlEasyPayService = app(CurlEasyPayService::class);
            $result = $curlEasyPayService->refundState($this->payment);
            $isRefunded = $result['success'] ?? false;

            if (!$isRefunded && $this->attempt < self::MAX_ATTEMPT_COUNT) {

                // resend task
                $this->pushToQueue(
                    $this->payment,
                    ($this->attempt + 1)
                );

            }

            $this->log('- processed (payment #' . ($this->payment->payment_id ?? '') . '): ' . ($isRefunded ? 'success' : 'fail'));
            return true;

        } catch (Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            $this->log($msg);
            return false;
        }
    }

    /**
     * Used in: PaymentController->refund()
     *
     * @param Payment $payment
     * @param int $attempt
     * @param int|null $delayInSec
     * @return bool
     */
    public function pushToQueue(
        Payment $payment,
        int     $attempt = 1,
        int     $delayInSec = 0
    ): bool
    {

        $queueName = $this->getQueueName();

        if ($delayInSec > 0) {
            $now = Carbon::now();

            self::dispatch($payment, $attempt)
                ->onQueue($queueName)
                ->delay($now->addSeconds($delayInSec));

            return true;
        }

        self::dispatch($payment, $attempt)->onQueue($queueName);

        return true;
    }
}
