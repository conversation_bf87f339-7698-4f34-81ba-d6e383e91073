<?php

namespace Modules\Head\Console;

use Modules\Common\Console\CommonCommand;
use Modules\Head\Services\LoanRefundService;
use RuntimeException;

final class ManualRefundCommand extends CommonCommand
{
    protected $signature = 'script:manual-refund {loan_id}';
    protected $description = 'Manual refund of active loan, create negative payment, make loan canceled, refinanced loans make active(optional)';

    public function handle(LoanRefundService $service): void
    {
        $this->startLog();

        $loanId = (int) $this->argument('loan_id');
        if (!$loanId) {
            throw new RuntimeException('No loan id provided');
        }

        $service->cancelById($loanId);

        $this->finishLog();
    }
}
