<?php

namespace Modules\Head\Console;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\Support\Facades\Mail;
use Modules\Common\Console\CommonCommand;
use Modules\Head\Emails\DirectServiceSendEmail;
use Modules\Head\Services\DirectService;
use Symfony\Component\Console\Command\Command;

/**
 * Uploand csv file - clients with overdue to SFTP server
 */
class DirectServiceSend extends CommonCommand
{
    const OVERDUE_BORDERS = [30, 45, 90];

    protected $name = 'script:direct-service:upload';
    protected $signature = 'script:direct-service:upload';
    protected $description = 'Uploand csv file - clients with overdue to SFTP server';

    protected string $logChannel = 'directService';

    public function handle(): int
    {
        $this->startLog();

        $done = 0;
        foreach (self::OVERDUE_BORDERS as $daysCount) {
            $this->log("--- Handling " . $daysCount . " ---");

            try {
                $done += app(DirectService::class)
                    ->setOverdueDays($daysCount)
                    ->build();
            } catch (\Exception $t) {
                $msg = 'Error:' . $t->getMessage()
                    . ', file: ' . $t->getFile()
                    . ', line: ' . $t->getLine();

                $this->log($msg, true);
            }
        }

        Mail::to(config('collect.direct_service.' . app()->environment()))
            ->queue(
                (new DirectServiceSendEmail())
                    ->from(config('mail.from.address'))
                    ->onQueue('email')
            );

        $this->finishLog(
            [$this->executionTimeString()],
            count(self::OVERDUE_BORDERS),
            $done,
            'Send export to DirectService'
        );

        return Command::SUCCESS;
    }
}
