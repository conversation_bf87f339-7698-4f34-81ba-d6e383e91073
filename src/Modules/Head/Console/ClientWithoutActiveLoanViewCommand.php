<?php

namespace Modules\Head\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

final class ClientWithoutActiveLoanViewCommand extends Command
{
    /**
     * @var string
     */
    protected $signature = 'script:db-view:client-without-active-loan {action=up : Operation to be performed with view (up|down)}';

    /**
     * @var string
     */
    protected $description = 'Manage client_without_active_loan view';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::statement(
            $this->argument('action') === 'up'
                ? <<<'SQL'
create or replace view client_without_active_loan AS
WITH
ranked_discounts AS (
    SELECT
        cda.client_discount_actual_id,
        cda.client_id,
        cda."percent",
        ROW_NUMBER() OVER (
            PARTITION BY cda.client_id
            ORDER BY cda.client_discount_actual_id DESC
        ) as rn
    FROM client_discount_actual cda
    WHERE
        cda.active = 1
        AND (NOW() between cda.valid_from AND cda.valid_till)
),
last_repaid_loan AS (
    SELECT
        l.loan_id,
        l.client_id,
        l.amount_approved,
        l.product_id,
        l.repaid_at,
        l.created_at,
        ROW_NUMBER() OVER (
            PARTITION BY l.client_id
            ORDER BY l.repaid_at DESC
        ) as rn
    FROM loan l
    WHERE
        l.office_id = 1 --online office
        AND l.loan_status_id = 7 --repaid
),
canceled_loans_for_last_30_days AS (
    SELECT
        l.client_id,
        COUNT(l.loan_id) AS number_of_canceled
    FROM loan l
    WHERE
        l.loan_status_id = 8 --canceled
        AND l.last_status_update_date >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY l.client_id
)
SELECT
    c.client_id,
    c.phone,
    c.email,
    coalesce(rd.percent, 0) as active_discount,
    coalesce(tmp_lcount.no_repaid_loans, 0) as no_repaid_loans,
    lrl.loan_id,
    lrl.amount_approved,
    lrl.product_id,
    lrl.repaid_at,
    coalesce((CURRENT_DATE - lrl.repaid_at::date), 0) as days_since_last_loan,
    llast.created_at,
    coalesce(la.max_overdue_days, 0) as max_overdue_days,
    coalesce(сll30.number_of_canceled, 0) as number_of_canceled
FROM client c
JOIN last_repaid_loan lrl ON c.client_id = lrl.client_id AND lrl.rn = 1
LEFT JOIN loan_actual_stats la ON lrl.loan_id = la.loan_id
LEFT JOIN (
    SELECT llast.client_id, max(llast.created_at) as created_at
    FROM loan llast
    WHERE llast.office_id = 1
    GROUP BY llast.client_id
) as llast ON llast.client_id = c.client_id
LEFT JOIN (
    SELECT lcount.client_id, count(lcount.loan_id) as no_repaid_loans
    FROM loan lcount
    WHERE lcount.loan_status_id = 7 --repaid loan
    GROUP BY lcount.client_id
) as tmp_lcount ON tmp_lcount.client_id = c.client_id
LEFT JOIN ranked_discounts rd ON c.client_id = rd.client_id AND rd.rn = 1
LEFT JOIN canceled_loans_for_last_30_days сll30 ON сll30.client_id = c.client_id
WHERE
    c.active = 1
    AND c.disabled_at is null
    AND c.deleted_at is null
    AND c.deleted = 0
    AND not exists (
        SELECT 1
        FROM loan l1
        WHERE
            c.client_id = l1.client_id
            AND l1.loan_status_id = 6 --active
    ) --to exclude clients with active credit
ORDER BY c.client_id
SQL
                : 'DROP VIEW client_without_active_loan'
        );
    }
}
