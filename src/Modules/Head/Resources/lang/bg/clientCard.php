<?php

return [
    'collectorFee' => 'Такса колектор. Просрочена сума (:overdueAmount)',
    'calculatorEarlyRepayment' => 'Предсрочно погасяване',
    'clientEarlyRepayment' => 'Предсрочно погасяване клиент',
    'generalInformation' => 'Обща информация',
    'BirthDate' => 'Рожденна дата',
    'addAmounts' => 'Добави суми',
    'repaymentPlan' => 'План за погасяване',
    'earlyRepayment' => 'Предсрочно погасяване',
    'totalAmountToPay' => 'Обща сума за плащане',
    'period' => 'Период',
    'installmentsCount' => 'Брой вноски',
    'installmentSinglePlural' => '{1} вноска|[2,*] вноски',
    'installmentPeriodSinglePlural' => '{1} месец|[2,*] месеца',
    'paydayPeriodSinglePlural' => '{1} ден|[2,*] дни',
    'noChangesMade' => 'Не са извършени промени',
    'dateOfIssue' => 'Дата на отпускане',
    'repaymentDate' => 'Дата на погасяване',
    'earlyRepaymentSum' => 'Сума при предсрочно погасяване',
    'methodOfRelease' => 'Начин на отпускане',
    'otherExpenses' => 'Други разходи',
    'layoutNotFound' => 'Не съществува такъв темплейт 6',
    'layoutCreationFailed' => 'Грешка при създаването на темплейт',
    'clientLoanNotFound' => 'Не съществува такъв клиентски кредит',
    'loanCalculateFailed' => 'Грешка при преизчисляването на кредита',
    'date' => 'Дата',
    'performedTheAction' => 'Извършил действието',
    'deviceAndOperatingSystem' => 'Устройство и операционна система',
    'ipAddress' => 'ИП Адрес',
    'action' => 'Действие',
    'value' => 'Стойност',
    'openTasks' => 'Отворени задачи',
    'skipCreatingBucketTask' => 'Отложени бъкет задачи',
    'process' => 'Обработи',
    'manualCommunication' => 'Ръчна комуникация',
    'lastBucketTasks' => 'Последни задачи за събиране',
    'sendMessageTo' => 'Изпрати съобщение до клиента',
    'exitFromContact' => 'Изхот от контакт',
    'addNewContact' => 'Добави контакт',
    'updateContact' => 'Редактирай контакт',
    'busy' => 'Заето',
    'callLater' => 'Обаждане по-късно',
    'somethingElse' => 'Друго',
    'refused' => 'Отказан',
    'noAnswer' => 'Няма отговор',
    'waitingDocuments' => 'Чака документи',
    'wrongPhone' => 'Грешен телефон',
    'queryParameters' => 'Параметри на заявка',
    'loanParameters' => 'Параметри на кредит',
    'sendSms' => 'Изпрати СМС',
    'product' => 'Продукт',
    'wantedAmount' => 'Искана сума',
    'dateOfFirstInstallment' => 'Дата на първа вноска',
    'firstInstallmentAmount' => 'Сума на вноска',
    'paymentMethod' => 'Начин на плащане',
    'office' => 'Офис',
    'createdBy' => 'Създал',
    'and' => ' и ',
    'cash' => 'В брой',
    'easypay' => 'ИзиПей',
    'bank' => 'По банков път',
    'bankAccount' => 'Банкова сметка',
    'historyOfApplicationsAndLoans' => 'История на заявки и кредити',
    'employmentHistory' => 'Информация за работодател',
    'paymentHistory' => 'История на последните плащания',
    'amountDue' => 'Дължима сума',
    'overdueDays' => 'Дни просрочие',
    'overdueInstallmentsCount' => 'Бр. просрочени вноски',
    'maturityDate' => 'Дата на падеж',
    'lastPromise' => 'Последно обещание',
    'lastPaidSum' => 'Последна платена сума',
    'lastPaymentDate' => 'Дата на посл. плащане',
    'beforeXdays' => 'Преди :days дни',
    'lastPromiseDesc' => ':amount лв. на :date',
    'promiseAndCallLater' => '{2} :amount лв. на :date|{4} на :date',
    'communicationTable' => 'Комуникация с клиента',
    'customerInformation' => 'Информация за клиента',
    'from' => 'от',
    'to' => 'до',
    'egn' => 'ЕГН',
    'numberOfPersonalID' => 'Номер на ЛК',
    'userPhone' => 'Телефон',
    'addUserPhone' => 'Добави Телефон',
    'noPhone' => 'Клиентът няма телефон',
    'noActiveLoan' => 'Клиентът няма активен кредит',
    'secondPhone' => 'Втори телефон',
    'creditLimit' => 'Кредитен лимит',
    'addressByPersonalID' => 'Адрес по ЛК',
    'currentAddress' => 'Настоящ адрес',
    'nameOfContactPerson' => 'Име на лице за контакт',
    'phoneOfContactPerson' => 'Телефон на ЛК',
    'ContactPerson' => 'Лице за контакт',
    'scoringInformation' => 'Информация за скоринг',
    'upToSalary' => 'До заплата',
    'installments' => 'На вноски',
    'amount' => 'Сума',
    'installment' => 'Вноска',
    'interest' => 'Лихва',
    'penalty' => 'Неустойка',
    'lateInterest' => 'Лихва просрочие',
    'latePenalty' => 'Неустойка просрочие',
    'lateAmount' => 'Наказателна лихва',
    'days' => 'Дни',
    'daysLower' => 'дни',
    'applications' => '{1} :applicationCount заявка|[0,*]:applicationCount заявки',
    'lastApplication' => '{0} няма заявки|{1} посл. заявка преди :days дни',
    'loans' => '{1} :loanCount кредит|[0,*]:loanCount кредита',
    'lastPaidBefore' => '{0} няма плащане|{1}посл. плащане преди :days дни',
    'discount' => 'Намаление',
    'repaymentPlanInfo' => 'Виж погасителен план',
    'biggerDiscountPercentFrom' => 'Клиент с допълнителна отстъпка',
    'manualReport' => 'Ръчна справка',
    'module' => 'Модул',
    'contentOfInformation' => 'Съдържание на информацията',
    'leaveComment' => 'Добави кометар',
    'Approve' => 'Одобри',
    'sendForSign' => 'Изпрати за подпис',
    'Back' => 'Назад',
    'Identification' => 'Идентификация',
    'ChangeInAmounts' => 'Промяна в суми',
    'WriteComment' => 'Въведете коментар',
    'ChooseDateTime' => 'Изберете ден и час',
    'ExitCalls' => 'Изход от обаждане',
    'ExitTask' => 'Изход от задача',
    'ReasonForRefusal' => 'Причина за отказ',
    'Actions' => 'Действия',
    'RefuseLoan' => 'Откажи',
    'Choose' => 'Избери',
    'ChooseType' => 'Изберете тип',
    'LoanNotOnThisClient' => 'Избраният кредит не е на този клиент',
    'No.' => 'No.',
    'Maturity' => 'Падеж',
    'Status' => 'Статус',
    'Principal' => 'Главница',
    'restPrincipal' => 'Оставаща главница',
    'Interest' => 'Лихва',
    'Penalty' => 'Неустойка',
    'OtherExpenses' => 'Други разходи',
    'Total' => 'Общо',
    'Payment' => 'Плащане',
    'Paid' => 'Платено',
    'RemainingDue' => 'Остава да дължи',
    'OverdueInDays' => 'Дължимо в (дни)',
    'LoanCosts' => 'Добавени разходи към кредита',
    'RepaymentSchedule' => 'Погасителен план',
    'DateCreated' => 'Дата на Създаване',
    'Remaining' => 'Остава',
    'AddingLoanCost' => 'Добавяне на разход',
    'DeleteLoanCost' => 'Триене на разход',
    'ClientWantsLaterCall' => 'Клиентът желае обаждане по-късно',
    'Minutes' => 'минути',
    'In' => 'в',
    'OrAfter' => 'или след',
    'HoursMinutesSeparator' => ':',
    'ExtendLoan' => 'Удължаване на кредит',
    'resetDueDatesForHalfPartial' => 'Частично предсрочно погасяване',
    'resetDueDatesMakeChoice' => 'Избери дата от която ще започне първа неплатена вноска',
    'changeDates' => 'Промени дати',
    'ExtendLoanForbidden' => 'Удължаване забранено от настройки на продукта',
    'ExtendLoanExistsToday' => 'Само 1 удължаване на ден',
    'ExtensionFee' => 'Такса',
    'Comment' => 'Коментар',
    'RepaymentOfPreviousDebts' => 'Погасяване на предходни задължения',
    'OK' => 'ОК',
    'ClientActiveLoansDueAmount' => 'С този кредит се погасяват задължения по активни кредити на клиента в размер от :previous_loans_due_amount',
    'NewClient' => 'Нов Клиент',
    'OldClient' => 'Стар Клиент',
    'objPrevState' => 'Промяна от',
    'objCurState' => 'Промяна към',
    'browser' => 'Браузър',
    'TotalToDate' => 'Обща сума за плащане към',
    'SendSMS' => 'Изпрати SMS',
    'SendEmail' => 'Изпрати Email',
    'Recalculate' => 'Преизчисли',
    'extensionMessage' => '{0} Погасителният план е удължен с :days дни на :date |{1} Погасителният план е удължен с :days ден на :date',
    'send' => 'Изпрати',
    'check' => 'Прегледай',
    'lastChange' => 'Последна промяна',
    'documentNum' => 'Документ номер',
    'undistributedPaymentId' => 'Payment ID',
    'undistributedPaymentTaskId' => 'Payment task ID',
    'paymentDetails' => 'Детайли за плащането',
    'paymentBreakdown' => 'Разбивка на плащането',
    'tabLoadingFailed' => 'Грешка при зареждането на страницата',
    'successfullyUpdated' => 'Успешно обновено',

    //Tab communication
    'clientNotificationsPanelName' => 'Спри получаването на съобщения от клиента',
    'eventDate' => 'Дата на Събитие',
    'channel' => 'Канал',
    'classification' => 'Класификация',
    'content' => 'Съдържание',
    'addComment' => 'Добави коментар за клиента',
    'marketing' => 'Маркетинг',
    'collect' => 'Събиране',
    'call' => 'Обаждане',
    'sms' => 'SMS',
    'email' => 'Email',
    'mail' => 'Писмо',

    //Error messages
    'DateToFarAway' => 'Избрана прекалено далечна дата',
    'ApproveAttemptFailed' => 'Неуспешно одобряване',
    'DisapproveAttemptFailed' => 'Неуспешно неодобряване',
    'SubmitAttemptFailed' => 'Неуспешно приключване',
    'clientIdRouteParamMustBeInt' => 'Раутинг параметъра за клиентско ид трябва да е цяло число',
    'invalidSecondRoutingParam' => 'Невалиден втори раутинг параметър',
    'invalidThirdRoutingParam' => 'Невалиден трети раутинг параметър',
    'noneRoutesMatchCriteria' => 'Нито един от раутовете не отговаря на критериите',
    'documentPrintingFailed' => 'Неуспешно разпечатване на документ/и.',

    //Collect
    'CollectorDecision' => 'Колекторски код',
    'SkipTill' => 'Изчакай до',
    'PromisedAmount' => 'Обещана сума',
    'LastCollectorAttempt' => 'Предишна връзка с длъжника',

    'FailedTaskExit' => 'Неуспешен изход от задача',
    'printDocuments' => 'Принтирай документи',
    'printDocumentsOnRequest' => 'Принтирай документи при заявка',
    'printDocumentsOnApprove' => 'Принтирай документи при одобрение',
    'AttachDocument' => 'Прикачване на документ',
    'Documents' => 'Документи',
    'Pictures' => 'Снимки',
    'BlockedReason' => 'Причини за блокиране',
    'blockClientSuccessful' => 'Успешно блокиране на потребител',
    'unblockClientSuccessful' => 'Успешно активиране на потребител',
    'activateUser' => 'Активирай потребител',
    'Fraudster' => 'Измамник',
    'AssociatedWithPEP' => 'Свързан с PEP',
    'RequestOfAClient' => 'По желание на клиент',
    'SuspiciousActivity' => 'Подозрителна дейност',
    'BlockClient' => 'Блокирай клиент',
    'HistoryBlockClient' => 'История на блокирани клиенти',
    'BlockedClient' => 'Блокирани клиенти',
    'ReasonForBlock' => 'Причина за блокиране',
    'Yes' => 'Да',
    'No' => 'Не',
    'NotificationSettings' => 'Известия настройки',

    'AccruedExpense' => 'Начислен Разход',
    'TotalPaid' => 'Общо платено',
    'RemainsDue' => 'Остава да дължи',
    'deleteCost' => 'Изтриване на разход',
    'Term' => 'Брой дни',

    // Sales
    'saleTaskIsNotForThisClient' => 'Задачата за продажба не е на този клиент',

    'successfullySaveComment' => 'Успешно запазен коментар',
    'communicationNotFound' => 'Няма намерена комуникация',
    'saveCommunicationFailed' => 'Неуспешно запазване на коментар',

    // Schedule payments
    'deleteTaxFailed' => 'Неуспешно изтриване на разход. Сумата надхвърля вашият лимит за това действие.',
    'deleteTaxSuccess' => 'Успешно изтрит разход.',
    'deleteTaxNotApprove' => 'Неуспешно изтриване на разход. Кредита не подлежи на изтриване на разход.',
    'deleteTaxFail' => 'Разхода надвишава позволената сума за изтриване.',

    'clientPromisePayment' => 'Клиентът обещава да плати',
    'sumOf' => 'сумата от',
    'leva' => 'лева',
    'on' => 'на',
    'in' => 'в',
    'today' => 'днес',
    'tomorrow' => 'утре',
    'after7days' => 'след 7 дни',
    'after14days' => 'след 14 дни',
    'clientWantsLaterCall' => 'Клиентът желае обаждане по-късно',
    'orAfter' => 'или след',
    'minutes' => 'минути',
    'reasonToEndTheTask' => 'Причина за приключване на задачата',
    'postponeReminderBucket' => 'Отложи напомняне за просрочие',
    'nextCallOn' => 'Следващо обаждане на',
    'lv' => 'лв',

    // Open tasks
    'overdue' => 'Просрочие',
    'dueDateComming' => 'Наближаваща падежна дата',
    'request' => 'Заявка ',
    'openTaskProcessing' => ' - Обработва се от ',
    'openTaskNew' => ' - За обработка - ',
    'openTaskExit' => 'Изход:',
    'noOpenTasks' => 'Няма отворени задачи за този клиент',
    'new1' => 'Нов клиент',
    'new0' => 'Стар клиент',
    'Add' => 'Добави',
    'Remove' => 'Изтрий',
    'Block' => 'Блокирай',
    'Unblock' => 'Отблокирай',
    'docsAttorney' => 'Дог. поръчка',
    'LetterRemainingSum' => 'Писмо оставащо задължение',
    'LetterPaidLoan' => 'Писмо удост. за липса на задължение',
    'LetterPaidLoanDescription' => 'Системата ще генерира Удостоверение за липса на задължения към днешна дата.',
    'LawDocs' => 'Документи за съд',
    'UploadDoc' => 'Прикачи документ',
    'Other' => 'Други',
    'id_card_1' => 'Лична карта',
    'id_card_2' => 'Лична карта',
    'passport' => 'Паспорт',
    'viber' => 'Viber',
    'whatsapp' => 'WhatsUp',
    'telegram' => 'Telegram',
    'mvr' => 'МВР',

    'pin' => 'ЕГН',
    'client' => 'Клиент',
    'clientId' => 'ID на клиент',
    'delivery' => 'Разпределение',
    'deliveredAmount' => 'Разпределена сума',
    'notDeliveredAmount' => 'Неразпределена сума',
    'loanRemainingAmount' => 'Оставащо по кредита',
    'dueAmount' => 'Дължима сума',
    'amountUponExtension' => 'Сума при удължаване',
    'earlyRepaymentAmount' => 'Сума предсрочно пог',
    'daysAfterMaturity' => 'Просрочени дни',
    'resto' => 'Ресто',
    'noLoansFound' => 'Няма намерени заеми за този клиент',

    'paidAmount' => 'Платена сума',
    'paidAmountCash' => 'Получено в брой',
    'amountForThisLoan' => 'Разнеси по този кредит',
    'documentNumber' => 'Документ номер',
    'basis' => 'Основание',


    'agentNotificationUnsignedLoan' => 'Клиента не е подписал договора си за заем.',
    'agentNotificationUreceivedMoney' => 'Клиента не е взел парите си от ИзиПей.',
    'loanId' => 'Номер договор',
    'sentAt' => 'Дата превод',

    'saleInformation' => 'Информация продажба',
    'saleClientDiscount' => 'Клиентът има отстъпка от',
    'saleProdName' => 'Продукт',
    'saleProdPeriod' => 'Валидна',
    'saleClientDiscountBottom' => 'Отстъпка',
    'cashDesk' => 'Каса',

    'foundActiveLoans' => 'Намерени активни кредити',
    'Refinance' => 'Рефинансирай',
    'Limited' => 'Срочен',
    'Unlimited' => 'Безсрочен',
    'sales_task' => 'Продажба',
    'collect_task' => 'Събиране',
    'approve_task' => 'Одобрение',
    'approve_task_web' => 'Одобрение',
    'chooseTemplate' => 'Изберете темплейт',

    //noi
    'refreshNoi2' => 'Опресни НОИ 2<br>(Заплата)',
    'refreshNoi7' => 'Опресни НОИ 7<br>(Договори)',
    'refreshNoi51' => 'Опресни НОИ 51<br>(Пенсия)',
    'refreshNoiAll' => 'Опресни всички<br>&nbsp;',
    'headingNoiReport_noi2' => 'Информация за осигурителния доход към ',
    'headingNoiReport_noi7' => 'Информация за трудови договори към ',
    'headingNoiReport_noi51' => 'Информация за пенсионен доход към ',
    'NoiReport_Employer' => 'Работодател',
    'NoiReport_BULSTAT' => 'Булстат',
    'NoiReport_Headquarters' => 'Седалище',
    'NoiReport_Phone' => 'Телефон',
    'NoiReport_Contract' => 'Договор',
    'NoiReport_AppointmentDate' => 'Дата на назначаване',
    'NoiReport_Period' => 'Срок',
    'NoiReport_TerminationDate' => 'Дата на прекратяване',
    'NoiReport_Salary' => 'Заплата',
    'NoiReport_Status' => 'Статус',
    'NoiReport_Year' => 'Година',
    'NoiReport_Month' => 'Месец',
    'NoiReport_WorkedDays' => 'Изработени дни',
    'NoiReport_DeclarationDays' => 'Дата на деклариране',
    'NoiReport_Date' => 'Дата на справка:',
    'NoiReport_DateLastChange' => 'Дата последна промяна:',
    'NoiReport_PensionerStatus' => 'Статус на пенсионера:',
    'NoiReport_PensionerTotalPension' => 'Общ размер на пенсията:',
    'NoiReport_PensionerPensionType' => 'Вид на пенсията:',
    'NoiReport_PensionerPensionNumber' => 'Поредност на пенсията:',
    'NoiReport_PensionerPensionKSO' => 'Размер на пенсията съгласно чл.101, ал.3 КСО:',
    'NoiReport_PensionerStartDate' => 'Начална дата:',
    'NoiReport_PensionerPensionPeriod' => 'Срок на пенсията:',
    'NoiReport_PensionerPensionBasicAmount' => 'Основен размер на пенсията:',
    'NoiReport_PensionerPensionActualAmount' => 'Действителен размер на пенсията:',
    'NoiReport_PensionerPensionPaymentAmount' => 'Сума на изплащане:',
    'NoReportsToDisplayNoi51' => 'Няма налични справки за пенсионен доход ',
    'NoReportsToDisplayNoi2' => 'Няма налични справки за трудови договори ',
    'NoReportsToDisplayNoi7' => 'Няма налични справки за осигурителния доход ',
    'NoServiceInformationForEGN' => 'Услугата няма информация за ЕГН:',
    'NoReportsRemaining' => 'Нямате право на повече репорти',

    //mvr

    'MvrNoResponse' => 'Услугата няма информация за клиента.',
    'MvrNoData' => 'МВР справка не съдържи необходима информация.',
    'MvrWrongData' => 'МВР справка съдържи грешна информация(ЕГН/ЛКН).',
    'MvrWrongValidDate' => 'МВР справка върна изтекла ЛК.',

    'NoDataForClient' => 'Няма данни за клиента',
    'IssueDate' => 'Дата на издаване',
    'ValidDate' => 'Дата на валидност',
    'issuedBy' => 'ЛК издадена от',
    'Sex' => 'Пол',
    'address' => 'Адрес',
    'byDistrict' => 'От община',
    'name' => 'Име на лицето',
    'MvrPictureNotSaved' => 'Проблем със запазването на снимката',

    //payment_plan
    'extend' => 'Такса удължаване',
    'successCreateClientContact' => 'Успешно създаен контакт.',
    'successUpdateClientContact' => 'Успешно обновен контакт.',
    'successDeleteClientContact' => 'Успешно изтрит контакт.',
    'failedCreateClientContact' => 'Неуспешно създаване на контакт.',
    'failedUpdateClientContact' => 'Неуспешно създаване на контакт.',
    'failedDeleteClientContact' => 'Неуспешно изтриване на контакт.',
    'successStorePhone' => 'Успешно добавен телефон.',
    'ibanValidationMessage' => 'IBAN може да съдържа само главни латински букви и цифри',
    'ibanMinMessage' => 'Невалиден IBAN',
    'ibanMaxMessage' => 'Невалиден IBAN',

    'btnDownloadAccountingInfo' => 'Свали счет. информация',
    'btnExportLegalDocs' => 'Генерирай съдебни документи',
];
