<li class="nav-item" role="presentation">
    <button class="nav-link dropdown-toggle no-radius bg-primary text-white"
            data-toggle="dropdown"
            role="button"
            aria-haspopup="true"
            aria-expanded="false"
    >
        {{ __('menu.Menu') }}
        <i class="fa fa-arrow-down"></i>
    </button>

    <div class="dropdown-menu">
        <div class="dropdown-item">
            <select name="select-client-loan" class="form-control no-read-only"
                    onclick="event.stopPropagation();">
                <option value="">{{__('Select loan')}}</option>
                @foreach($clientLoans as $clientLoan)
                    <option
                            value="{{ route('head.clients.cardProfile', [$clientLoan->client_id, $clientLoan->loan_id]) }}"
                            @if($loanId == $clientLoan->loan_id)selected="selected"@endif
                    >
                        {{$clientLoan->getKey()}}
                        ({{ __('head::loanStatus.' . $clientLoan->loan_status_id) }}) - {{ getOfficeName($clientLoan->office_id) }}
                    </option>
                @endforeach
            </select>
        </div>


        @if($client->isBlocked())
            <a href="#clientUnblockModal"
               class="dropdown-item"
               data-toggle="modal"
               data-target="#clientUnblockModal"
            >
                {{__('head::clientCard.Unblock')}}
            </a>
        @else
            <a href="#clientBlockModal"
               class="dropdown-item"
               data-toggle="modal"
               data-target="#clientBlockModal"
            >
                {{__('head::clientCard.Block')}}
            </a>
        @endif

        <a href="#letterRemainingSumModal"
           class="dropdown-item"
           data-toggle="modal"
           data-target="#letterRemainingSumModal"
        >
            {{__('head::clientCard.LetterRemainingSum')}}
        </a>

        <a href="#letterPaidLoan"
           class="dropdown-item"
           data-toggle="modal"
           data-target="#letterPaidLoan"
        >
            {{__('head::clientCard.LetterPaidLoan')}}
        </a>

        <a href="#docsAttorney"
           class="dropdown-item"
           data-toggle="modal"
           data-target="#docsAttorney"
        >
            {{__('head::clientCard.docsAttorney')}}
        </a>

        <a href="#uploadClientDocument"
           class="dropdown-item"
           data-toggle="modal"
           data-target="#uploadClientDocument"
        >
            {{__('head::clientCard.UploadDoc')}}
        </a>

        <a href="#paidLoanCertificateModal"
           class="dropdown-item text-danger"
           data-toggle="modal"
           data-target="#paidLoanCertificateModal"
        >
            {{ __('table.taxFee') }}
        </a>

    </div>
</li>
