<?php use Modules\Common\Models\CcrReport ?>
<div class="card-body text-secondary bg-white">
    <div id="errorContainer">
        @if(!$hasCcrReportPermission)
            {{__('Нямате право за повече опити за ръчни ЦКР справки')}}
        @endif
    </div>
    <a href="#" data-check="noServiceInformationCcr"
       class="btn btn-success {{$hasCcrReportPermission === false ? 'disabled' : ''}} mb-3">
        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
        {{ __('head::clientCard.manualReport') }}
    </a>
    <div id="ckrReportsContainer" class="myaccordion">
        @if (!empty($ccrReports))
            @php /** @var CcrReport $ccrReport */ @endphp
            @foreach($ccrReports as $ccrReport)
                @php
                    /** @var stdClass $loop */
                    $show = $loop->first == 1 ? 'show' : '';
                    $sign = $show ? 'minus-square' : 'plus-square';
                    $report = json_decode($ccrReport->parsed_data);
                @endphp
                @if(empty($report))
                    <div id="noServiceInformationCcr"
                         style="display: none">{{ 'ЦКР : ' . __('head::clientCard.NoServiceInformationForEGN') . ' ' .$ccrReport->pin }}</div>
                    @break
                @endif
                <div class="card">
                    <div class="card-header" id="headingCkrReport{{$loop->iteration}}">
                        <h2 class="mb-0">
                            <button class="d-flex align-items-center justify-content-between btn btn-link"
                                    data-toggle="collapse"
                                    data-target="#collapse{{$loop->iteration}}"
                                    aria-controls="collapse{{$loop->iteration}}"
                            >
                                <h3>{{ 'Информация за кредитна задлъжност към ' . showDate($ccrReport->created_at) }}</h3>
                                <span class="fa-stack fa-sm">
                                <i class="fa fa-{{$sign}}"></i>
                            </span>
                            </button>
                        </h2>
                    </div>
                    <div id="collapse{{$loop->iteration}}"
                         class="collapse {{$show}}"
                         aria-labelledby="headingCkrReport{{$loop->iteration}}"
                         data-parent="#ckrReportsContainer"
                    >
                        <div class="card-body ckr-reports" style="color: black; margin-left: 10px;">
                            @php
                                $bCredCount = $report->bank->active_credits->stats->count ?? 0;
                                $bCredSCount = $report->bank->active_credits->stats->source_count ?? 0;
                                $bLine = $bCredCount . ' кредит' . (substr($bCredCount, -1) == 1 ? '' : 'а')
                                    . ' в ' . $bCredSCount . ' банк' . (substr($bCredSCount, -1) == 1 ? '' : 'и');

                                $nbCredCount = $report->non_bank->active_credits->stats->count ?? 0;
                                $nbCredSCount = $report->non_bank->active_credits->stats->source_count ?? 0;
                                $nbLine = $nbCredCount . ' кредит' . (substr($nbCredCount, -1) == 1 ? '' : 'а') . ' в ' . $nbCredSCount . ' ФИ';
                            @endphp

                            <h4 style="font-weight: 600;">Кредити към банки: {{ $bLine }}</h4>
                            <h4 style="font-weight: 600;">Кредити към ФИ: {{ $nbLine }}</h4>
                            <h4 style="font-weight: 600;">Справка е за отчетния период
                                към {{ showDate($report->date) }}</h4>
                            <br/>
                            <h3 style="font-weight: 700;">Кредити от банки</h3>
                            @include('head::ccr-report-history.ccr_report_template', ['object' => $report->bank])
                            <br>
                            <hr/>
                            <br>
                            <h3 style="font-weight: 700;">Кредити от финансови институции</h3>
                            @include('head::ccr-report-history.ccr_report_template', ['object' => $report->non_bank])
                        </div>
                    </div>
                </div>
            @endforeach
        @else

            <div style="color: #ff4f70">No CCR reports for this client</div>
        @endif

        <div id="ccrPermissionHiddenDiv"
             style="display: none">{{ $hasCcrReportPermission === false ? 'disabled' : '' }}</div>

    </div>
</div>

<script>
    window.createNewCcrReportRoute = '{{route('head.ccr-report-history.create')}}';
    window.createNewCcrReportParams = {
        loanId: '{{ $loan?->getKey() }}',
        clientId: '{{ $client->getKey() }}',
        reportType: '{{ $reportTypes['ccr'] }}',
    };
</script>
