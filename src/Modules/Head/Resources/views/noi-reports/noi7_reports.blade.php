<div class="card">
    <div class="card-header" id="headingNoiReport_noi7">
        <h2 class="mb-0">
            <button class="d-flex align-items-center justify-content-between btn btn-link"
                    data-toggle="collapse"
                    data-target="#collapse_noi7-{{$loop->iteration}}"
                    aria-controls="collapse_noi7-{{$loop->iteration}}">
                    {{ __('head::clientCard.headingNoiReport_noi7') . $noiReport7['created_at'] }}
                    <span class="fa-stack fa-sm"><i class="fa fa-{{$sign}}"></i></span>
            </button>
        </h2>
    </div>
    <div id="collapse_noi7-{{$loop->iteration}}"
         class="collapse"
         aria-labelledby="headingNoiReport_noi7"
         data-parent="#noiReportsContainer">
        @if (!empty($noiReport7['data']))
        <table class="table">
            <thead>
            <tr>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Employer') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_BULSTAT') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Headquarters') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Phone') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_AppointmentDate') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Period') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_TerminationDate') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Salary') }}
                </th>
                <th scope="col" class="not-bold">
                    {{ __('head::clientCard.NoiReport_Status') }}
                </th>
            </tr>
            </thead>
            <tbody>
                @php
                    if (!empty($el['data']) && count($el['data']) > 1) {
                        usort($el['data'], "compareByStartDate");
                    }
                @endphp
                @foreach ($noiReport7['data'] as $dataNoiRows => $dataNoi7)
                    <tr>
                        <td>
                            {{$dataNoi7['name']}}
                        </td>
                        <td>
                            {{$dataNoi7['bulstat']}}
                        </td>
                        <td>
                            {{$dataNoi7['address']}}
                        </td>
                        <td>
                            {{$dataNoi7['phone']}}
                        </td>
                        <td>
                            {{$dataNoi7['start_date']}}
                        </td>
                        <td>

                        </td>
                        <td>
                            {{$dataNoi7['end_date']}}
                        </td>
                        <td>
                            {{amount(floatToInt($dataNoi7['salary']))}}
                        </td>
                        <td class="paymentAmount {{$dataNoi7['status']}}">
                            <div>
                                {{$dataNoi7['status']}}
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        @else
            <b>Няма данни за клиента:</b> {{$noiReport7['raw']}}
        @endif
    </div>
</div>
