@extends('layouts.app')

@section('content')
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <div class="dashboard-table">
                    <table
                        id="table"
                        data-toggle="table"
                        data-toolbar="#toolbar"
                        data-show-columns="true"
                        data-filter-control="true"
                        data-side-pagination="server"
                        data-pagination="true"
                        data-click-to-select="true"
                        data-show-export="true"
                        data-export-types="['csv','excel']"
                        data-export-options='{"fileName": "export"}'
                        data-page-list="[10, 20, 50, 100, ALL]"
                        data-url="{{route('head.dashboard.showData')}}">
                        <thead>
                        <tr>
                            <th data-field="id" data-filter-control="input">Номер на заявка</th>
                            <th data-field="name" data-filter-control="select">Тип кредит</th>
                            <th data-field="price" data-filter-control="input">Заявена Сума</th>
                            <th data-field="test0" data-filter-control="input">Период</th>
                            <th data-field="test1" data-filter-control="input">Заявен продукт</th>
                            <th data-field="test2" data-filter-control="input">Телефонен номер</th>
                            <th data-field="test3" data-filter-control="input">Имейл</th>
                            <th data-field="test4" data-filter-control="input">Статус</th>
                            <th data-field="test5" data-filter-control="input">Име Презиме Фамилия</th>
                            <th data-field="test6" data-filter-control="input">ЕГН</th>
                            <th data-field="test7" data-filter-control="input">Тип на клиент</th>
                            <th data-field="test8" data-filter-control="input">Брой одобрени кредити до сега</th>
                            <th data-field="test9" data-filter-control="input">Брой отказани заявки до сега</th>
                            <th data-field="test10" data-filter-control="input">Брой просрочени вноски до сега</th>
                            <th data-field="test11" data-filter-control="input">Кредитен лимит</th>
                            <th data-field="test12" data-filter-control="input">Текущ адрес – град</th>
                            <th data-field="test13" data-filter-control="input">Текущ адрес – детайли</th>
                            <th data-field="test14" data-filter-control="input">Постоянен адрес – град</th>
                            <th data-field="test15" data-filter-control="input">Постоянен адрес – детайли</th>
                            <th data-field="test16" data-filter-control="input">Постоянен адрес - пощенски код</th>
                            <th data-field="test17" data-filter-control="input">Лична карта номер</th>
                            <th data-field="test18" data-filter-control="input">Лична карта дата на валидност</th>
                            <th data-field="test19" data-filter-control="input">Час и дата на създаване</th>
                            <th data-field="test20" data-filter-control="input">Час и дата на последна промяна на статус
                            </th>
                            <th data-field="test21" data-filter-control="input">Име на бизнес потребител</th>
                            <th data-field="test22" data-filter-control="input">Източник на заявката</th>
                            <th data-field="test23" data-filter-control="input">IP Address</th>
                            <th data-field="test24" data-filter-control="input">Канал</th>
                            {{-- after some condition we will adding this rows--}}
                            <th data-field="test25" data-filter-control="input">Час и дата на одобрение</th>
                            <th data-field="test26" data-filter-control="input">Номер на кредит</th>
                            <th data-field="test27" data-filter-control="input">Одобрен продукт</th>
                            <th data-field="test28" data-filter-control="input">Одобрена сума</th>
                            <th data-field="test29" data-filter-control="input">Брой вноски</th>
                            <th data-field="test30" data-filter-control="input">Дата на следващ падеж</th>
                            <th data-field="test31" data-filter-control="input">Дни просрочие</th>
                            <th data-field="test32" data-filter-control="input">Максимално просрочие по кредита в дни
                            </th>
                            <th data-field="test33" data-filter-control="input">Максимално просрочие по кредита в лева
                            </th>
                            <th data-field="test34" data-filter-control="input">Сума на вноска</th>
                            <th data-field="test35" data-filter-control="input">Начислени текущи лихви</th>
                            <th data-field="test36" data-filter-control="input">Начислени текущи такси</th>
                            <th data-field="test37" data-filter-control="input">Начислени текущи други разходи</th>
                            <th data-field="test38" data-filter-control="input">Общо дължимо към днешна дата</th>
                            <th data-field="test39" data-filter-control="input">Обща сума на плащания по кредит</th>
                            <th data-field="test40" data-filter-control="input">Общо платени лихви</th>
                            <th data-field="test41" data-filter-control="input">Общо платени такси</th>
                            <th data-field="test42" data-filter-control="input">Общо платени други разходи</th>
                            <th data-field="test43" data-filter-control="input">Дата на последно плащане</th>
                            {{--  end row from conditions--}}
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts');
{{--<script src="https://unpkg.com/bootstrap-table@1.17.1/dist/bootstrap-table.min.js"></script>--}}
<script src="{{ asset('dist/js/tableExport.min.js') }}"></script>
{{--<script src="https://unpkg.com/tableexport.jquery.plugin/libs/jsPDF/jspdf.min.js"></script>--}}
{{--<script src="https://unpkg.com/tableexport.jquery.plugin/libs/jsPDF-AutoTable/jspdf.plugin.autotable.js"></script>--}}
<script src="{{ asset('dist/js/bootstrap-table.min.js') }}"></script>
<script src="{{ asset('dist/js/bootstrap-table-export.min.js') }}"></script>
<script src="{{ asset('dist/js/bootstrap-table-filter-control.min.js') }}"></script>
<script src="{{ asset('dist/js/bootstrap-datepicker.min.js') }}"></script>

<script>

    let $table = $('#table')
    $(function () {

        let dataTable = [
            'test0',
            'test1',
            'test2',
            'test3',
            'test4',
            'test5',
            'test6',
            'test7',
            'test8',
            'test9',
            'test10',
            'test11',
            'test12',
            'test13',
            'test14',
            'test15',
            'test16',
            'test17',
            'test18',
            'test19',
            'test20',
            'test21',
            'test21',
            'test22',
            'test23',
            'test24',
            'test25',
            'test26',
            'test27',
            'test28',
            'test29',
            'test30',
            'test31',
            'test32',
            'test33',
            'test34',
            'test35',
            'test36',
            'test37',
            'test38',
            'test39',
            'test40',
            'test41',
            'test42',
            'test43',
        ];

        for (let i = 2; i < dataTable.length; i++) {
            $table.bootstrapTable('hideColumn', dataTable[i]);
        }

        $('.bootstrap-table-filter-control-test0').daterangepicker({});
    })
</script>
@endpush
