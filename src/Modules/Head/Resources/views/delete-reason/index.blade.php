@extends('layouts.app')
@section('content')
    @php
        /**
* @var \Modules\Common\Models\DeleteReason $deleteReason
 */
    @endphp
    <x-card>
        <div class="form-group">
            <x-btn-create url="{{ route('head.deleteReason.create') }}" name="{{ __('btn.Create') }}"/>
        </div>
        <!-- End ./form-group -->

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.Name')}}</th>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.Active')}}</th>
                    <th>{{__('table.Actions')}}</th>
                </tr>
            </x-slot:head>

            @foreach($deleteReasons as $deleteReason)
                <tr
                        @if(!$deleteReason->active)
                            class="not-active"
                        @endif
                >
                    <td>{{ $deleteReason->name }}</td>
                    <td>{{ $deleteReason->created_at }}</td>
                    <td>{{ $deleteReason->isActiveLabel() }}</td>
                    <td>
                        <x-btn-edit
                                url="{{ route('head.deleteReason.edit', $deleteReason->getKey()) }}"/>
                        <x-btn-delete
                                url="{{ route('head.deleteReason.delete', $deleteReason->getKey()) }}"/>
                        @if($deleteReason->isActive())
                            <x-btn-disable
                                    url="{{ route('head.deleteReason.disable', $deleteReason->getKey()) }}"/>
                        @else
                            <x-btn-enable
                                    url="{{ route('head.deleteReason.enable', $deleteReason->getKey()) }}"/>
                        @endif
                    </td>
                </tr>
            @endforeach
        </x-table>
    </x-card>
@endsection
