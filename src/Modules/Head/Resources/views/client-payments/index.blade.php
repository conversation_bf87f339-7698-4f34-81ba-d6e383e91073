<x-card>
    <x-filter-form :filterForm="$filterForm"/>
</x-card>
<x-card>
    <div class="mb-3">
        @if($loan?->isJuridical())
            <div class="alert alert-danger">
                <h4 class="mb-0">{{__('Loan is juridical')}}</h4>
            </div>
        @endif
        <a href="{{route('payment.manual-payment.index',['client_id' => $client->getKey()])}}"
           class="btn btn-sm btn-primary"
        >
            <i class="fa fa-plus"></i>&nbsp;
            {{ __('menu.NewPayment') }}
        </a>
    </div>

    <x-table>
        <x-slot:head>
            <tr>
                <th>{{__('table.createdDate')}}</th>
                <th>{{__('table.direction')}}</th>
                <th>{{__('table.Office')}}</th>
                <th>{{__('table.paymentSum')}}</th>
                <th>{{__('table.source')}}</th>
                <th>{{__('table.numberLoan')}}</th>
                <th>{{__('table.reasonPayment')}}</th>
                <th>{{__('table.Status')}}</th>
                <th>{{__('table.Actions')}}</th>
            </tr>
        </x-slot:head>
        @php
            /**
* @var \Modules\Common\Models\Payment $payment
 */
        @endphp
        @forelse($payments as $payment)
            <tr>
                <td>{{ $payment->created_at ? formatDate($payment->created_at,'d.m.Y H:i:s') : '' }}</td>
                <td>{{ $payment->direction->getLabel() }}</td>
                <td>{{ $payment->office->name }}</td>
                <td class="{{ $payment->direction->getCssClass() }} text-center text-dark">
                    {{ intToFloat($payment->amount) }}
                </td>
                <td>
                    @if(!empty($payment->source))
                    @if($payment->source->value == \Modules\Common\Enums\Payment\PaymentSourceEnum::LOAN_REFINANCE->value)
                        {{ __('payments::PaymentSource.refinance') }}
                    @elseif($payment->payment_method_id == \Modules\Common\Models\PaymentMethod::PAYMENT_METHOD_OFFSET)
                        {{ __('payments::PaymentSurce.offset') }}
                    @else
                        {{ __('payments::paymentMethods.' . $payment->payment_method_id) }}
                    @endif
                    @endif
                </td>
                <th>{{$payment->loan_id}}</th>
                <td>{{$payment->description}}</td>
                <td class="text-center">
                    {{ $payment->active ? __('table.Active') : __('table.NotActive')  }}
                </td>
                <td class="p-0 text-center">
                    <div class="btn-group">
                        <a href="#collapse-{{$payment->getKey()}}"
                           class="btn btn-sm btn-warning"
                           data-toggle="collapse"
                           aria-controls="collapse-{{$payment->getKey()}}"
                        >
                            <i class="fa fa-eye"></i>&nbsp;
                            {{ __('btn.Show') }}
                        </a>
                    </div>
                </td>
            </tr>
            @include('head::client-payments.partials.collapsed')
        @empty
            <tr>
                <td colspan="9">{{ __('No available payments') }}</td>
            </tr>
        @endforelse

    </x-table>
</x-card>
<script>
    $(document).ready(function () {
        window.DateRangePicker.init();
        window.Select2Handler.init();
    });
</script>
