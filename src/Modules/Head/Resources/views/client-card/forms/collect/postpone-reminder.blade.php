<x-common::modal
        modal-id="postponeReminderBucket"
        modal-title="{{__('head::clientCard.postponeReminderBucket')}}"
>

    <div class="card-body">
        <form action="{{route('collect.skip-bucket.storeSkipBucketTask')}}" method="POST">
            @csrf

            <input type="hidden" name="client_id" value="{{$loan?->client_id}}"/>
            <input type="hidden" name="loan_id" value="{{$loan?->getKey()}}"/>
            <div class="form-group">
                <label for="skip_days">{{__('table.Days')}}</label>
                <input type="number"
                       class="form-control"
                       max="14"
                       required="required"
                       name="skip_days"
                       id="skip_days"
                />
            </div>
            <!-- End ./form-group -->

            <div class="form-group">
                <label for="details">{{__('table.Comment')}}</label>
                <textarea name="details" id="details" rows="5" class="form-control" required="required"></textarea>
            </div>
            <!-- End ./form-group -->

            <x-common::modal-footer/>
        </form>
    </div>

</x-common::modal>
