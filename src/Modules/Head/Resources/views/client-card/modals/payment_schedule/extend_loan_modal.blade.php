<div id="extendLoanModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="extendLoanModalLabel">{{__('head::clientCard.ExtendLoan')}}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="extendLoanForm" action="#" class="pl-3 pr-3">
                    @csrf
                    <div class="form-group">

                        <label for="extensionTerm">{{__('head::clientCard.Term')}}:</label>
                        <input id="extensionTerm" name="tax[term]" class="form-control no-read-only" type="text"
                                value="30" required>
                        <br>
                        <br>
                        <label for="extensionFee">{{__('head::clientCard.ExtensionFee')}}:</label> <span id="extensionFeeText">{{ number_format($defaultLoanExtensionFee,2) }}</span> {{ __('head::clientCard.lv') }}
                        <input id="extensionFee" name="tax[amount]" class="form-control no-read-only" type="hidden"
                               value="{{ $defaultLoanExtensionFee}}" required>
                    </div>
                    <input type="hidden" name="tax[min_amount]" value="{{ $defaultLoanExtensionFee }}">
                    <input type="hidden" name="tax[client_id]" value="{{ $client->getKey() }}">
                    <input type="hidden" name="tax[loan_id]" value="{{ $loan->getKey() }}">
                    <div class="form-group text-center">
                        <button type="button" class="btn btn-danger" data-dismiss="modal" aria-label="Close">
                            {{__('btn.Close')}}
                        </button>
                        <button class="btn btn-success" type="submit">
                            {{__('btn.Extend')}}
                        </button>
                    </div>
                </form>
{{--                <div id="extendLoanErrorContainer"></div>--}}
            </div>
        </div>
    </div>
</div>
