@php
    /**
* @var \Modules\Common\Models\Loan $loan
 * @var \Modules\Collect\Models\BucketTaskSkip $bucketTaskSkip
 * @var \Modules\Collect\Models\BucketTaskSkip $bucketTaskSkipLast
 */
    $bucketTaskSkipLast = $loan->bucketTaskSkip->last();
@endphp
<x-card>
    <x-slot:title>{{__('head::clientCard.skipCreatingBucketTask')}}</x-slot:title>
    <x-slot:cardOptions>
        <span class="cursor-move" title="{{ __('click and move me') }}">
            <i class="fa fa-arrows-alt"></i>
        </span>
    </x-slot:cardOptions>

    @if($loan && $loan->isActive())
        <div class="form-group">
            @if($bucketTaskSkipLast?->disableSkipBtn())
                <a href="javascript:;"
                   class="btn btn-sm btn-primary disabled"
                >
                    <i class="fa fa-calendar"></i>&nbsp;
                    {{__('btn.NextDelayBucketTask')}}&nbsp;{{$bucketTaskSkipLast->till_date->format('d.m.Y')}}
                </a>
            @else
                <a href="#postponeReminderBucket"
                   data-toggle="modal"
                   data-target="#postponeReminderBucket"
                   class="btn btn-sm btn-primary"
                >
                    <i class="fa fa-calendar"></i>&nbsp;
                    {{__('btn.DelayBucketTask')}}
                </a>
            @endif
        </div>
        <!-- End ./form-group -->
    @endif

    @if($loan->bucketTaskSkip->count())
        <x-table>

            <x-slot:head>
                <tr>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.toDate')}}</th>
                    <th>{{__('table.Days')}}</th>
                    <th>{{__('table.CreatedBy')}}</th>
                </tr>
            </x-slot:head>

            @foreach($loan->bucketTaskSkip as $bucketTaskSkip)
                <tr>
                    <td>{{$bucketTaskSkip->created_at->format('d.m.Y H:i')}}</td>
                    <td>{{$bucketTaskSkip->till_date->format('d.m.Y')}}</td>
                    <td>{{$bucketTaskSkip->skip_days}}</td>
                    <td>{{$bucketTaskSkip->creator->getFullNames()}}</td>
                </tr>
            @endforeach
        </x-table>
    @endif

</x-card>
