@extends('layouts.app')
@section('content')

    <x-card-filter-form :filter-form="$filterForm"/>

    <x-card>
        <div class="form-group">
            <a href="{{route('head.client-black-list.list')}}"
               class="btn btn-sm btn-warning"
            >
                <i class="fa fa-arrow-left"></i>&nbsp;
                {{__('head::clientCard.BlockedClient')}}
            </a>
        </div>
        <!-- End ./form-control -->

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.ClientId')}}</th>
                    <th>{{__('table.clientNames')}}</th>
                    <th>{{__('table.Pin')}}</th>
                    <th>{{__('table.BlackListFrom')}}</th>
                    <th>{{__('table.BlackListТо')}}</th>
                    <th>{{__('table.BlackListBy')}}</th>
                    <th>{{__('table.Comment')}}</th>
                    <th>{{__('table.Action')}}</th>
                </tr>
            </x-slot:head>

            @php
                /**
                * @var \Modules\Common\Models\Client $client
                */
            @endphp
            @foreach($clients as $client)
                @php
                    $clientBlockHistories = $client->clientBlockHistories->first();
                    $clientUnblockBlockHistories = $client->clientUnblockBlockHistories->first();
                @endphp
                <tr>
                    <td>{{$client->getKey()}}</td>
                    <td>{{$client?->getFullName()}}</td>
                    <td>{{$client->pin}}</td>
                    <td>{{$clientBlockHistories?->created_at}}</td>
                    <td>
                        @if($client->isBlocked() && !$client->blocked_to_date)
                            {{__('table.ToManualExit')}}
                        @endif

                        @if($client->blocked_to_date)
                            {{$client->blocked_to_date}} {{__('table.WillBeExitedAutomatic')}}
                        @endif

                        @if(!$client->isBlocked() && $clientUnblockBlockHistories?->created_at)
                            {{$clientUnblockBlockHistories?->created_at}} {{__('table.WasExitedOn')}}
                        @endif
                    </td>
                    <td>{{$clientBlockHistories?->creator?->getFullNames()}}</td>
                    <td>{{$clientBlockHistories?->comment}}</td>
                    <td>
                        <a href="{{route('head.client-black-list.blackListDetails', $client->getKey())}}"
                           class="btn btn-sm btn-warning">
                            <i class="fa fa-eye"></i>&nbsp;
                            {{__('table.History')}}
                        </a>
                    </td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$clients"/>
    </x-card>
@endsection
