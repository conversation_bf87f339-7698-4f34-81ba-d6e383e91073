<div class="row">
    <div class="col-lg-12">
        <x-card>

            <div class="form-group">
                <button type="button"
                        class="btn btn-primary"
                        data-toggle="modal"
                        data-target="#uploadClientDocument"
                >
                    <i class="fa fa-upload"></i>&nbsp;
                    {{__('head::clientCard.UploadDoc')}}
                </button>
            </div>
            <!-- End ./form-group -->

            <x-table>
                <x-slot:head>
                    <tr>
                        <th>{{__('table.CreatedAt')}}</th>
                        <th>{{__('table.CreatedBy')}}</th>
                        <th>{{__('table.DocumentType')}}</th>
                        <th>{{__('communication::notificationSetting.download')}}</th>
                        <th>{{__('table.Basis')}}</th>
                    </tr>
                </x-slot:head>

                @php
                    /**
* @var \Modules\Common\Models\ClientDocument $clientDocument
 */
                @endphp
                @foreach($documents as $clientDocument)
                    <tr>
                        <td>{{$clientDocument->created_at}}</td>
                        <td>{{$clientDocument->creator->getFullNames()}}</td>
                        <td>{{__('product::product.'.$clientDocument->file->fileType->name)}}</td>
                        <td>
                            <a href="{{$clientDocument->file->downloadFilePath()}}" target="_blank">
                                <i class="fa fa-download"></i>&nbsp;{{__('communication::notificationSetting.download')}}
                            </a>
                        </td>
                        <td>{{$clientDocument->file->comment}}</td>
                    </tr>
                @endforeach
            </x-table>

        </x-card>
    </div>
    <!-- End ./col -->
</div>
<!-- End ./row -->
