<tr>
    @foreach($sortingArray as $table => $columns)
        @foreach($columns as $column => $translate)
            <th scope="col" class="sorting">
                <input type="text" name="order[{{$table}}][{{$column}}]"
                       value="{{session($cacheKey . '.order.'.$table.'.'.$column) ?: 'desc'}}">
                {{$translate}}
                <i class="fa {{session($cacheKey . '.order.'.$table.'.'.$column) ?
                                                            'fa-sort-'.session($cacheKey . '.order.'.$table.'.'.$column) : ''}}"
                   aria-hidden="true"></i>
            </th>
        @endforeach
    @endforeach
    <th scope="col">
        {{__('table.Actions')}}
    </th>
</tr>
