@php
    /**
* @var \StikCredit\Calculators\Installments\DefaultInstallment $installment
 */
@endphp
<x-card>
    <x-slot:title>{{ __('head::clientCard.RepaymentSchedule') }}</x-slot:title>
    <x-table>
        <x-slot:head>
            <tr>
                <th class="tableHeader font-weight-bold">{{ __('head::clientCard.No.') }}</th>
                <th>{{ __('table.DueDate') }}</th>

                <th>{{ __('table.Principal') }}</th>
                <th>{{ __('table.Interest') }}</th>
                <th>{{ __('table.Penalty') }}</th>
                <th>{{ __('table.OtherExpenses') }}</th>
                <th>{{__('table.TotalGeneral')}}</th>

                <th>{{ __('table.RestPrincipal') }}</th>
                <th>{{ __('table.RestInterest') }}</th>
                <th>{{ __('table.RestPenalty') }}</th>
                <th>{{__('table.RemainsToOwe')}}</th>

                <th>{{__('table.TotalPaid')}}</th>
                <th>{{__('table.LastPayment')}}</th>
                <th>{{__('table.CurrentOverdue')}}</th>
                <th>{{__('table.MaxOverdue')}}</th>
                <th>Покажи всички</th>
            </tr>
        </x-slot:head>
        @foreach($installments as $installment)
            @include('head::client-payment-schedule.includes.payment-schedule-row')
        @endforeach
    </x-table>
</x-card>
<script id="installment-dates">
    window.installmentDates = @json($installmentsDates);
</script>
