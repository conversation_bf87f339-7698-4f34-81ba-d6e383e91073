@php
    use Modules\Common\Enums\PaymentDistributionAimEnum;
    /**
    * @var \Modules\Common\Models\Payment $payment
     */
@endphp
@if($allPayments->count() > 0)
    <x-card>
        <x-slot:title>{{__('payments::payments.Delivery')}}</x-slot:title>
        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.PaymentId')}}</th>
                    <th>{{__('table.Identifier')}}</th>
                    <th>{{__('table.Description')}}</th>
                    <th>{{__('table.Date')}}</th>
                    <th>{{__('payments::payments.Distribution date')}}</th>
                    <th>{{__('payments::payments.Distributed Amount')}}</th>
                    <th>{{__('payments::payments.Distributed Amount Before')}}</th>
                    <th>{{__('payments::payments.Distributed Amount After')}}</th>
                </tr>
            </x-slot:head>
            @foreach($allPayments as $payment)
                <tr>
                    <th colspan="10">
                        <a href="{{route('payment.payments.details', $payment->getKey())}}" target="_blank">
                            ID&nbsp;{{$payment->getKey()}}
                        </a>
                        @if($payment->migration_id)
                            <small>mId:({{$payment->migration_id}})&nbsp;mDb:({{$payment->migration_db}})</small>
                        @endif

                        @if($payment->amount < 0)
                        <span style="color: #E52B06;">
                        @else
                        <span>
                        @endif
                            <span>&nbsp;-&nbsp;{{$payment->created_at->format('d.m.Y H:i:s')}}</span>
                            <span>&nbsp;-&nbsp;{{intToFloat($payment->amount)}}</span>
                            @if($payment->isCash())
                                <span>&nbsp;-&nbsp;В брой&nbsp;{{$payment->office->name}}</span>
                            @else
                                <span>&nbsp;-&nbsp;{{$payment->bankAccount?->name}}</span>
                            @endif
                            <span>&nbsp;-&nbsp;{{$payment->isManualLabel()}}</span>
                            <span>&nbsp;-&nbsp;{{$payment->creator?->getFullNames()}}</span>
                        </span>
                    </th>
                </tr>

                @foreach($payment->paymentDistribution as $paymentDistribution)
                    @php
                        $description = $paymentDistribution->description;
                        if (!$description) {
                            $description = match ($paymentDistribution->aim) {
                                PaymentDistributionAimEnum::LATE_PENALTY->value => __('head::clientCard.latePenalty'),
                                PaymentDistributionAimEnum::LATE_INTEREST->value => __('head::clientCard.lateInterest'),
                                default => null
                            };
                        }
                    @endphp
                    <tr class="@if($paymentDistribution->isLate()) text-danger @endif">
                        <td class="text-center">{{$paymentDistribution->payment_id}}</td>
                        <td>
                            {{$paymentDistribution->getLabel()}}
                        </td>
                        <td>{{ $description }}</td>
                        <td>{{formatDate($paymentDistribution->payment_date)}}</td>
                        <td>{{formatDate($paymentDistribution->distribution_date)}}</td>
                        <td class="text-success">{{amount($paymentDistribution->distributed_amount)}}</td>
                        <td>{{amount($paymentDistribution->distributed_amount_before)}}</td>
                        <td>{{amount($paymentDistribution->distributed_amount_after)}}</td>
                    </tr>
                @endforeach
            @endforeach
        </x-table>
    </x-card>
@endif
