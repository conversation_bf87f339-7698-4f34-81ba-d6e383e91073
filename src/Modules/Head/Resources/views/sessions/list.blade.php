@extends('layouts.app')

@section('content')
@if(session()->has('msg'))
    <div
        class="alert alert-{{ (session()->has('success') && 'ok' == session()->get('success') ? 'success' : 'danger') }}"
        style="display: inline-block; min-width: 300px;"
    >
        {{ session()->get('msg') }}
    </div>
@endif

<div class="row" id="container-row">
    <div class="col-lg-12">
        <div id="main-table" class="card">
            <div id="client-sessions-table" class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th scope="col">{{__('table.FilterById')}}</th>
                            <th scope="col">{{__('table.FilterByPin')}}</th>
                            <th scope="col">{{__('table.ClientFullName')}}</th>
                            <th scope="col">{{__('table.Token')}}</th>
                            <th scope="col">{{__('table.SaleTaskCreatedAt')}}</th>
                            <th scope="col">{{__('table.ValidTill')}}</th>
                            <th scope="col">{{__('table.Actions')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($rows as $row)
                            <tr>
                                <td>{{ $row->id }}</td>
                                <td>{{ $row->pin }}</td>
                                <td>{{ $row->client_name }}</td>
                                <td>{{ $row->token }}</td>
                                <td>{{ $row->created_at }}</td>
                                <td>{{ $row->valid_till }}</td>
                                <td>
                                    <form action="{{ route('head.clients.sessionDelete', $row->id) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-danger w-50">{{ __('btn.Disable') }}</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
