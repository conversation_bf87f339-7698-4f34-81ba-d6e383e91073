@extends('layouts.app')
@section('content')

    <x-card>
        <x-slot:title>Състояние на заема към момента</x-slot:title>

        <h3>Глобални сумми</h3>
        <x-table>
            <tr>
                <td>Заем №</td>
                <td>{{ $loan_id }}</td>
            </tr>
            <tr>
                <td>Пълно погасяване</td>
                <td>{{ intToFloat($total_amounts['regular']) }}</td>
            </tr>
            <tr>
                <td>Предсрочно погасяване</td>
                <td>{{ intToFloat($total_amounts['early']) }}</td>
            </tr>
            <tr>
                <td>Удължаване 30 дни</td>
                <td>{{ intToFloat($total_amounts['extension']) }}</td>
            </tr>
            <tr>
                <td>Общо дължимо по такси</td>
                <td>{{ intToFloat($total_amounts['total_unpaid_tax_amount']) }}</td>
            </tr>
            <tr>
                <td>Неплатена главница</td>
                <td>{{ intToFloat($total_amounts['total_outstanding_principal']) }}</td>
            </tr>
            <tr>
                <td>% лихва</td>
                <td>{{ $interest_percent }}</td>
            </tr>
            <tr>
                <td>% неустойка</td>
                <td>{{ $penalty_percent }}</td>
            </tr>
            <tr>
                <td>% late interest</td>
                <td>{{ (!empty($settings['late_interest']) ? $settings['late_interest'] : 'NONE') }}</td>
            </tr>
            <tr>
                <td>% late penalty</td>
                <td>{{ (!empty($settings['late_penalty']) ? $settings['late_penalty'] : 'NONE') }}</td>
            </tr>
             <tr>
                <td>% late penalty days</td>
                <td>{{ (!empty($settings['late_penalty_days']) ? $settings['late_penalty_days'] : 'NONE') }}</td>
            </tr>
            <tr>
                <td>ЕГН</td>
                <td>{{ $client_pin }}</td>
            </tr>
            <tr>
                <td>Имена</td>
                <td>{{ $client_name }}</td>
            </tr>
        </x-table>

        <h3>Вноски с колекторски такси</h3>
        <x-table>
            <x-slot:head>
                <tr>
                    <th>Вноска ID</th>
                    <th>Вноска номер</th>
                    <th>Дата</th>
                    <th>Обща сума на вноска</th>
                    <th></th>
                    <th>principal</th>
                    <th>paid principal</th>
                    <th>rest principal</th>
                    <th></th>
                    <th>interest</th>
                    <th>accrued interest</th>
                    <th>paid interest</th>
                    <th>rest interest</th>
                    <th></th>
                    <th>penalty</th>
                    <th>accrued penalty</th>
                    <th>paid penalty</th>
                    <th>rest penalty</th>
                    <th></th>
                    <th>late interest</th>
                    <th>paid late interest</th>
                    <th>rest late interest</th>
                    <th></th>
                    <th>late penalty</th>
                    <th>paid late penalty</th>
                    <th>rest late penalty</th>
                    <th></th>
                    <th>Платена</th>
                    <th>Платена на</th>
                </tr>
            </x-slot:head>

            @foreach($installments as $installment)

                <tr>
                    <td>{{ $installment->installment_id }}</td>
                    <td>{{ $installment->seq_num }}</td>
                    <td>{{ $installment->due_date->format('Y-m-d') }}</td>
                    <td>{{ $installment->total_amount }}</td>
                    <td></td>
                    <td>{{ $installment->principal }}</td>
                    <td>{{ $installment->paid_principal }}</td>
                    <td>{{ $installment->rest_principal }}</td>
                    <td></td>
                    <td>{{ $installment->interest }}</td>
                    <td>{{ $installment->accrued_interest }}</td>
                    <td>{{ $installment->paid_interest }}</td>
                    <td>{{ $installment->rest_interest }}</td>
                    <td></td>
                    <td>{{ $installment->penalty }}</td>
                    <td>{{ $installment->accrued_penalty }}</td>
                    <td>{{ $installment->paid_penalty }}</td>
                    <td>{{ $installment->rest_penalty }}</td>
                    <td></td>
                    <td>{{ $installment->late_interest }}</td>
                    <td>{{ $installment->paid_late_interest }}</td>
                    <td>{{ $installment->rest_late_interest }}</td>
                    <td></td>
                    <td>{{ $installment->late_penalty }}</td>
                    <td>{{ $installment->paid_late_penalty }}</td>
                    <td>{{ $installment->rest_late_penalty }}</td>
                    <td></td>
                    <td>{{ ($installment->paid == 1 ? 'Да' : 'Не') }}</td>
                    <td>{{ $installment->paid_at }}</td>
                </tr>

                @if (!empty($taxes_collect_inst_map[$installment->installment_id]))
                    <tr>
                        <td colspan="4">Такси колектор към вноската</td>
                        <td colspan="26"></td>
                    </tr>

                    @foreach ($taxes_collect_inst_map[$installment->installment_id] as $collectorTax)
                        <tr>
                            <td>{{ $collectorTax->tax_id }}</td>
                            <td></td>
                            <td>{{ $collectorTax->created_at }}</td>
                            <td></td>
                            <td></td>
                            <td>{{ intToFloat($collectorTax->amount) }}</td>
                            <td>{{ intToFloat($collectorTax->paid_amount) }}</td>
                            <td>{{ intToFloat($collectorTax->rest_amount) }}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>{{ ($collectorTax->paid == 1 ? 'Да' : 'Не') }}</td>
                            <td>{{ $collectorTax->paid_at }}</td>
                        </tr>
                    @endforeach

                @endif

            @endforeach
        </x-table>

        <h3>Такси</h3>
        <x-table>
            <x-slot:head>
                <tr>
                    <th>ID</th>
                    <th>Tип</th>
                    <th>Дата</th>
                    <th>amount</th>
                    <th>paid amount</th>
                    <th>rest amount</th>
                    <th>Платена</th>
                    <th>Платена на</th>
                </tr>
            </x-slot:head>

            @if (!empty($taxes_collect_ids_lost))
                <tr>
                    <td>Такси колектор (lost)</td>
                    <td colspan="5"></td>
                </tr>
                @foreach ($taxes_collect_inst_map[$installment->installment_id] ?? [] as $lostInstId)
                    @if (!empty($taxes_collect_inst_map[$lostInstId]))
                        @foreach ($taxes_collect_inst_map[$lostInstId] as $collectorTax)
                            <tr>
                                <td>{{ $collectorTax->tax_id }}</td>
                                <td>{{ $collectorTax->type }}</td>
                                <td>{{ $collectorTax->created_at }}</td>
                                <td>{{ intToFloat($collectorTax->amount) }}</td>
                                <td>{{ intToFloat($collectorTax->paid_amount) }}</td>
                                <td>{{ intToFloat($collectorTax->rest_amount) }}</td>
                                <td>{{ ($collectorTax->paid == 1 ? 'Да' : 'Не') }}</td>
                                <td>{{ $collectorTax->paid_at }}</td>
                            </tr>
                        @endforeach
                    @endif
                @endforeach
            @endif

            @if(!empty($taxes_without_collect) && $taxes_without_collect->count() > 0)
                <tr>
                    <td>Други такси</td>
                    <td colspan="5"></td>
                </tr>
                @foreach ($taxes_without_collect as $tax)
                    <tr>
                        <td>{{ $tax->tax_id }}</td>
                        <td>{{ $tax->type }}</td>
                        <td>{{ $tax->created_at }}</td>
                        <td>{{ intToFloat($tax->amount) }}</td>
                        <td>{{ intToFloat($tax->paid_amount) }}</td>
                        <td>{{ intToFloat($tax->rest_amount) }}</td>
                        <td>{{ ($tax->paid == 1 ? 'Да' : 'Не') }}</td>
                        <td>{{ $tax->paid_at }}</td>
                    </tr>
                @endforeach
            @endif

        </x-table>


    </x-card>

@endsection
