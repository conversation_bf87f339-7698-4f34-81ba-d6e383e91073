@php use StikCredit\Calculators\Calculator; @endphp
@extends('layouts.app')

@section('content')
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">
                                    {{ __('head::installment.seqNum') }}
                                </th>
                                <th scope="col">
                                    {{ __('head::installment.totalAmount') }}
                                </th>
                                <th scope="col">
                                    {{ __('head::installment.interest') }}
                                </th>
                                <th scope="col">
                                    {{ __('head::installment.penalty') }}
                                </th>
                                <th scope="col">
                                    {{ __('head::installment.principal') }}
                                </th>
                                <th scope="col">
                                    {{ __('head::installment.dueDate') }}
                                </th>
                            </tr>
                            </thead>
                            <tbody id="preliminaryPaymentPlan">
                            @foreach($installments as $installment)
                                <tr>
                                    <td>{{ $installment['seq_num'] }}</td>
                                    <td>{{ $installment['total_amount'] }}</td>
                                    <td>{{ $installment['interest'] }}</td>
                                    <td>{{ $installment['penalty'] }}</td>
                                    <td>{{ Calculator::round($installment['principal']) }}</td>
                                    <td>{{ formatDate($installment['due_date'], 'd-m-Y') }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
