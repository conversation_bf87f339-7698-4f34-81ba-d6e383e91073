@extends('layouts.app')
@section('style')
    <link rel="stylesheet" href="{{ asset('css/document-templates.css') }}">
@endsection
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div id="create-new-document" class="mb-3">
                    <x-btn-create url="{{ route('head.autoProcessRules.create') }}" name="{{ __('btn.NewRule') }}"/>
                </div>
                <form id="autoProcessRulesForm" class="form-inline card-body"
                      action="{{ route('head.autoProcessRules.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">

                        <div class="col-lg-2">
                            <label for="name">{{ __('head::autoProcess.name') }}:</label>
                            <input name="name" class="form-control w-100 mb-3" type="text" value="{{ session($cacheKey . '.name') }}">
                        </div>

                        <div class="col-lg-2">
                            <label for="type">{{ __('head::autoProcess.rule') }}:</label>
                            <select name="type" id="type" class="form-control w-100 mb-3">
                                <option value=""></option>
                                @foreach($ruleTypes as $ruleType)
                                    <option
                                        @if(session($cacheKey . '.$ruleType') == $ruleType)
                                        selected
                                        @endif
                                        value="{{ $ruleType }}">{{__('head::autoProcess.auto.' . $ruleType)}}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-2">
                            <label for="office_id">{{ __('head::autoProcess.office') }}:</label>
                            <select name="office_id" id="office_id" class="form-control w-100 mb-3">
                                <option value=""></option>
                                @foreach($offices as $office)
                                    <option
                                        @if(session($cacheKey . '.$office->office_id') == $office->office_id)
                                        selected
                                        @endif
                                        value="{{ $office->office_id }}">{{ $office->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-2">
                            <label for="active">{{ __('head::autoProcess.status') }}:</label>
                            <select name="active" id="active" class="form-control w-100 mb-3">
                                <option value="">{{__('head::autoProcess.all')}}</option>
                                <option value="1">{{__('head::autoProcess.active')}}</option>
                                <option value="0">{{__('head::autoProcess.not_active')}}</option>
                            </select>
                        </div>

                        <div class="col-lg-2">
                            <label for="new_client">{{ __('head::autoProcess.client') }}:</label>
                            <select name="new_client" id="new_client" class="form-control w-100 mb-3">
                                <option value="">{{__('head::autoProcess.all')}}</option>
                                <option value="0">{{__('head::autoProcess.client.new')}}</option>
                                <option value="1">{{__('head::autoProcess.client.old')}}</option>
                            </select>
                        </div>

                        <div class="col-lg-2">
                            <label for="created_at">{{ __('head::autoProcess.created_at') }}:</label>
                            <input type="text" autocomplete="off" name="created_at"
                                   class="form-control w-100 singleDataPicker"
                                   value="{{ session($cacheKey . '.created_at') }}"
                            >
                        </div>

                        <div class="col-lg-2">
                            <label for="updated_at">{{ __('head::autoProcess.updated_at') }}:</label>
                            <input type="text" autocomplete="off" name="updated_at"
                                   class="form-control w-100 singleDataPicker"
                                   value="{{ session($cacheKey . '.updated_at') }}"
                            >
                        </div>

                        <div class="col-lg-2">
                            <label for="created_by">{{ __('head::autoProcess.created_by') }}:</label>
                            <input name="created_by" class="form-control w-100 mb-3" type="text" value="{{ session($cacheKey . '.created_by') }}">
                        </div>

                        <div class="col-lg-2">
                            <label for="updated_by">{{ __('head::autoProcess.updated_by') }}:</label>
                            <input name="updated_by" class="form-control w-100 mb-3" type="text" value="{{ session($cacheKey . '.updated_by') }}">
                        </div>

                        <div class="col-lg-12">
                            <x-btn-filter/>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">

                    <div class="table-responsive" id="autoProcessRulesTable">
                        @include('head::auto-reject-approve-rules.list-table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/loadSingleDataPicker.js') }}"></script>
    <script>
        let urlRules = '{{ route('head.autoProcessRules.refresh') }}';
        let formId = $("#autoProcessRulesForm");
        let tableId = $('#autoProcessRulesTable');
        loadSimpleDataGrid(urlRules, formId, tableId);

        loadDateRangePicker($('.singleDataPicker'));
    </script>
@endpush()
