<div class="row">
    <form id="ruleHistoryForm" class="form-inline card-body"></form>
    <div class="col-lg-12">
        <div id="main-table" class="card">
            <div class="card-body">
                <div class="table-responsive" id="ruleHistoryTable">
                    @include('head::auto-reject-approve-rules.history-list-table')
                </div>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script>
        let ruleHistoryUrl = '{{ route('head.autoProcessRules.refresh', $rule->getKey()) }}';
        let formId = $("#ruleHistoryForm");
        let tableId = $('#ruleHistoryTable');
        loadSimpleDataGrid(ruleHistoryUrl, formId, tableId);
    </script>
@endpush()
