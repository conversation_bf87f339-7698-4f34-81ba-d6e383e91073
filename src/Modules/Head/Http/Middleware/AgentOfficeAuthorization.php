<?php

namespace Modules\Head\Http\Middleware;

use Modules\Common\Models\Client;
use Modules\Head\Services\ClientService;

class AgentOfficeAuthorization
{
    public function __construct(private ClientService $clientService){}

    public function check(Client $client): bool
    {
        $officeIds = $client->clientOffices();
        if (empty($officeIds)) {
            return true;
        }
        return $this->clientService->adminBelongsToClientOffices($officeIds);
    }
}