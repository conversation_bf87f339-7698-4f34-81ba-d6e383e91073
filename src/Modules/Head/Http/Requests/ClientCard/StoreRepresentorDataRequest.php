<?php

namespace Modules\Head\Http\Requests\ClientCard;

use Modules\Common\Http\Requests\BaseRequest;

class StoreRepresentorDataRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|min:3|max:255',
            'last_name' => 'required|string|min:3|max:255',
            'middle_name' => 'required|string|min:3|max:255',
            'phone' => 'required|numeric',
            'phone_additional' => 'required|numeric',
            'email' => 'required|email',
        ];
    }
}