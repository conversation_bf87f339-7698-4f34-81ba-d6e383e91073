<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;

class InterestTermSearchRequest extends BaseRequest implements ListSearchInterface
{
    /**
     * @return array
     */
    public function rules()
    {
        return [
            'productId' => 'nullable|exists:product,product_id',
            'name' => 'nullable|string',
            'productGroupId' => 'nullable|exists:product_group,product_group_id',
            'period_from' => 'nullable|numeric|min:0',
            'period_to' => 'nullable|numeric|min:0',
            'interest_rate' => 'nullable|numeric|min:0',
        ];
    }
}

