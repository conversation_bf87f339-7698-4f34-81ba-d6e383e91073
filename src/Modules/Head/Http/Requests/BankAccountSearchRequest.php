<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;
use Modules\Common\Traits\DateBuilderTrait;

class BankAccountSearchRequest extends BaseRequest implements ListSearchInterface
{
    /**
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'nullable|string|max:255',
            'phone' => $this->getConfiguration('requestRules.phoneSearch'),
            'email' => $this->getConfiguration('requestRules.emailNullable'),
            'bank_id' => 'nullable|integer',
            'payment_method_id' => 'nullable|integer',
            'active' => $this->getConfiguration('requestRules.active'),
            'createdAt' => ['nullable','regex:' . DateBuilderTrait::$dateValidation],
            'updatedAt' => ['nullable','regex:' . DateBuilderTrait::$dateValidation],
            'order.*' => 'nullable',
        ];
    }
}
