<?php

declare(strict_types=1);

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

final class OverdueStatsPhysicalOfficesFilterRequest extends BaseRequest
{
    public function rules(): array
    {
        $rules['to_overdue_amount'] = ['nullable', 'integer', 'min:1'];

        if ($this->input('from_overdue_amount')) {
            $rules['to_overdue_amount'][] = 'gte:from_overdue_amount';
        }

        $rules['to_overdue_days'] = ['nullable', 'integer', 'min:1'];

        if ($this->input('from_overdue_days')) {
            $rules['to_overdue_days'][] = 'gte:from_overdue_days';
        }

        return [
            ...$rules,
            'from_overdue_amount' => 'nullable|integer|min:0',
            'from_overdue_days' => 'nullable|integer|min:0',
            'loan_id' => 'nullable|integer',
            'client_name' => 'nullable|string',
            'client_pin' => 'nullable|string',
            'client_phone' => 'nullable|string',
            'first_guarantor_name' => 'nullable|string',
            'second_guarantor_name' => 'nullable|string',
        ];
    }

    /**
     * The override is needed to combine input from two fields into one for the FromToNumberOfDaysSinceLastLoanFilter filter.
     *
     * from_to_overdue_amount = [from => from_overdue_amount, to => to_overdue_amount]
     *
     * Fields from_overdue_amount and to_overdue_amount are removed from the result.
     */
    public function validated($key = null, $default = null)
    {
        $result = [
            ...parent::validated(),
            'from_to_overdue_amount' => [
                'from' => $this->input('from_overdue_amount'),
                'to' => $this->input('to_overdue_amount')
            ],
            'from_to_overdue_days' => [
                'from' => $this->input('from_overdue_days'),
                'to' => $this->input('to_overdue_days')
            ],
        ];
        unset(
            $result['from_overdue_amount'],
            $result['to_overdue_amount'],
            $result['from_overdue_days'],
            $result['to_overdue_days'],
        );

        if ($key) {
            return $result[$key] ?? $default;
        }

        return $result;
    }

    public function messages(): array
    {
        return [
            ...parent::messages(),
            'to_overdue_amount.gte' => __('messages.toOverdueAmountGte'),
            'to_overdue_days.gte' => __('messages.toOverdueDaysGte'),
        ];
    }
}
