<?php

namespace Modules\Head\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Common\Http\Requests\BaseRequest;

class BlockDeleteReasonRequest extends BaseRequest
{
    public function rules(): array
    {
        $rules['name'] = [
            'required',
            'min:2',
            'max:40',
        ];

        $rules['name'][] = $this->is('*/block-reasons/*') ?
            Rule::unique('block_reason')
                ->ignore(!empty($this->blockReason) ? $this->blockReason->getKey() : null, 'block_reason_id')
            : Rule::unique('delete_reason')
                ->ignore(!empty($this->deleteReason) ? $this->deleteReason->getKey() : null, 'delete_reason_id');
        
        return $rules;
    }
}
