<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Modules\Common\Models\File;
use Modules\Common\Services\StorageService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FileController
{
    /**
     * @var StorageService
     */
    protected StorageService $storageService;

    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * @param File $file
     * @return BinaryFileResponse
     * @throws Exception
     */
    public function download(File $file): BinaryFileResponse
    {
        $this->isValidExtensionForDownload($file);
        return response()->download(
            $this->storageService->buildDocumentLink(
                $file,
                $this->storageService->getDocumentFileTypeExtension($file->file_name)
            )
        );
    }

    /**
     * @param File $file
     * @return BinaryFileResponse
     * @throws Exception
     */
    public function view(File $file): BinaryFileResponse
    {
        $this->isValidExtensionForView($file);
        return response()->file(
            $this->storageService->buildDocumentLink(
                $file,
                $this->storageService->getDocumentFileTypeExtension($file->file_name)
            )
        );
    }

    /**
     * @param File $file
     * @return string
     */
    protected function isValidExtensionForDownload(File $file): string
    {
        return $this->storageService->isValidFileExtension($file, StorageService::DOWNLOAD_FILE_FORMATS);
    }

    /**
     * @param File $file
     * @return string
     */
    protected function isValidExtensionForView(File $file): string
    {
        return $this->storageService->isValidFileExtension($file, StorageService::VIEW_FILE_FORMATS);
    }
}
