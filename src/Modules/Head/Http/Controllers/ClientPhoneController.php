<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Traits\ValidationTrait;
use Modules\Head\Forms\ClientPhoneForm;
use Modules\Head\Http\Requests\CreateClientPhoneRequest;
use Modules\Head\Repositories\ClientPhoneRepository;

class ClientPhoneController extends BaseController
{
    use ValidationTrait;

    /**
     * @param ClientPhoneRepository $clientPhoneRepository
     */
    public function __construct(
        private readonly ClientPhoneRepository $clientPhoneRepository,
        private readonly FormBuilder           $formBuilder
    )
    {
    }

    /**
     * @param CreateClientPhoneRequest $request
     * @return RedirectResponse
     */
    public function storePhone(CreateClientPhoneRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $clientPhone = $this->clientPhoneRepository->create($data);

        if ($clientPhone->exists) {
            return back()->with('success', __('head::clientCard.successStorePhone'));
        }

        return back()->with('error', __('error-create-message'));
    }

    /**
     * @param ClientPhone $client_phone
     * @return View
     */
    public function edit(ClientPhone $client_phone): View
    {
        $data['client_phone'] = $client_phone;
        $data['clientPhoneForm'] = $this->formBuilder->create(ClientPhoneForm::class, [
            'data-parsley-validate' => 'true',
            'route' => ['head.client-phone.update', $client_phone->getKey()],
            'method' => 'PUT',
            'model' => $client_phone
        ]);

        return view('head::client-phone.edit', $data);
    }


    /**
     * @param Request $request
     * @param ClientPhone $client_phone
     * @return RedirectResponse
     */
    public function update(Request $request, ClientPhone $client_phone)
    {
        $data = $request->validate([
            'number' => $this->getConfiguration('requestRules.commonPhone')
        ]);

        try {
            $client = $client_phone->client;
            $client->phone = $data['number'];
            $client->save();

            $this->clientPhoneRepository->updateClientPhone($client);

            return back()->with('success', 'Телефон успешно променен');
        } catch (\Throwable $e) {
            return back()->with('fail', 'Възникна грешка при промяна на телефон. Error: ' . $e->getMessage());
        }
    }


    /**
     * @param ClientPhone $client_phone
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Exception
     */
    public function destroy(ClientPhone $client_phone): RedirectResponse
    {
        /// @todo delete phone logic here or in service
        if ($client_phone->exists) {
            $client_phone->delete();

            return back()->with('success', __('success-delete-message'));
        }

        return back()->with('error', __('error-delete-message'));
    }
}
