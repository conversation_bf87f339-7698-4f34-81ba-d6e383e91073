<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;

class BaseClientCardController extends BaseController
{

    /**
     * Get parsed URL params
     *
     * Possible variants:
     * - clientCard/{client_id}
     * - clientCard/{client_id}/{loan_id}
     * - clientCard/{client_id}/{loan_id}/{task(sales|approve|collect)}
     * - clientCard/{client_id}/{loan_id}/{task(sales|approve|collect)}/{task_id}
     *
     * - clientCard/{client_id}/{sales|approve|collect}
     * - clientCard/{client_id}/{sales|approve|collect}/{task_id}
     */
    public function getRouteParams(): array
    {
        $route = request()->route();
        $clientId = $route->parameter('clientId');

        // Check if client id is not numeric
        if (!$route->hasParameter('clientId') || !is_numeric($clientId)) {
            throw new ProblemException(
                __('head::clientCard.clientIdRouteParamMustBeInt')
            );
        }

        $routeParams = [
            'clientId' => (int) $clientId,
        ];

        if ($route->hasParameter('loanId')) {
            $routeParams['loanId'] = intval($route->parameter('loanId'));
        }

        if ($route->hasParameter('task')) {
            $routeParams['task'] = $route->parameter('task');
        }

        if ($route->hasParameter('taskId')) {
            $routeParams['taskId'] = intval($route->parameter('taskId'));
        }

        return $routeParams;
    }
}