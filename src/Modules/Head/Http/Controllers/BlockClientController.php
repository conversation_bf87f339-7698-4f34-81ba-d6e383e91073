<?php

namespace Modules\Head\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\BlockClientRequest;
use Modules\Head\Http\Requests\UnBlockClientRequest;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Services\ClientService;

final class BlockClientController extends BaseController
{
    public function __construct(
        public ClientService $clientService,
        public ClientRepository $clientRepository,
    ) {
        parent::__construct();
    }

    public function blockClient(BlockClientRequest $request): RedirectResponse
    {
        try {
            $client = $this->clientRepository->getById($request->validated('client_id'));

            if ($this->clientService->block($client, $request->validated())) {
                if ($request->validated('to_date')) {
                    $client->setAttribute(
                        'blocked_to_date',
                        Carbon::parse($request->validated('to_date'))->format('Y-m-d')
                    );
                    $client->saveQuietly();
                }

                return $this->backSuccess(__('head::clientCard.blockClientSuccessful'));
            }
        } catch (Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return $this->backError(__('messages.generalErrorSomethingWrong') . ': ' . $e->getMessage());
        }

        return $this->backError(__('messages.generalErrorSomethingWrong'));
    }

    public function unBlockClient(UnBlockClientRequest $request): RedirectResponse
    {
        try {
            $client = $this->clientRepository->getById($request->validated('client_id'));

            if ($this->clientService->unblock($client, $request->validated())) {
                return $this->backSuccess(__('head::clientCard.unblockClientSuccessful'));
            }
        } catch (Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return $this->backError(__('messages.generalErrorSomethingWrong') . ': ' . $e->getMessage());
        }

        return $this->backError(__('messages.generalErrorSomethingWrong'));
    }
}
