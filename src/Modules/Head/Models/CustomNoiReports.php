<?php

namespace Modules\Head\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\File;

/**
 * @mixin IdeHelperCustomNoiReports
 */
class CustomNoiReports extends BaseModel
{
    protected $table = 'custom_noi_report';
    protected $primaryKey = 'custom_noi_report_id';

    protected $fillable = [
        'imported_file_id',
        'exported_file_id',
        'created_by',
        'processed',
        'processed_at',
        'processed_by',
        'imported_loan_ids',
    ];

    protected $casts = [
        'imported_loan_ids' => 'array'
    ];


    public function importedFile(): BelongsTo
    {
        return $this->belongsTo(File::class, 'imported_file_id', 'file_id');
    }

    public function exportedFile(): BelongsTo
    {
        return $this->belongsTo(File::class, 'exported_file_id', 'file_id');
    }

    public function isProcessing(): bool
    {
        if (empty($this->processed_at)) {
            return false;
        }

        if (!empty($this->processed)) {
            return false;
        }
        $started = Carbon::parse($this->processed_at);

        if (!$started->addMinutes(30)->lt(now())) {
            return true;
        }

        return false;
    }
}
