<?php

declare(strict_types=1);

namespace Modules\Head\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Modules\Common\Traits\FilterModelTrait;

/**
 * @property-read ?Carbon $repaid_at
 * @property-read Carbon $created_at
 * @mixin IdeHelperClientWithoutActiveLoan
 */
final class ClientWithoutActiveLoan extends Model
{
    use FilterModelTrait;

    public $casts = [
        'created_at' => 'datetime',
        'repaid_at' => 'datetime',
    ];

    protected $table = 'client_without_active_loan';
}
