<?php

namespace Modules\Head\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\Payment;

class ClientPaymentsFilterForm extends BaseFilterForm
{
    public $name = 'paymentsFilterForm';

    /**
     * @return mixed|void
     */
    public function buildForm()
    {
        $paymentsFilterForm = $this->request->get('paymentsFilterForm');

        $this
            ->add('createdAt', 'text', [
                'label' => __('table.createdDate'),
                'value' => $paymentsFilterForm['createdAt'] ?? '',
                'attr' => [
                    'data-daterange-picker' => 'true'
                ]
            ])
            ->add('direction', 'select', [
                'label' => __('table.inOut'),
                'selected' => $paymentsFilterForm['direction'] ?? '',
                'empty_value' => __('Select option'),
                'choices' => [
                    'in' => __('table.inDirection'),
                    'out' => __('table.outDirection'),
                ],
                'attr' => []
            ])
            ->add('sumFrom', 'text', [
                'label' => __('table.AmountFrom'),
                'value' => $paymentsFilterForm['sumFrom'] ?? '',
                'attr' => []
            ])
            ->add('sumTo', 'text', [
                'label' => __('table.AmountTo'),
                'value' => $paymentsFilterForm['sumTo'] ?? '',
                'attr' => []
            ])
            ->add('paymentSources', 'select', [
                'label' => __('table.Source'),
                'choices' => $this->getAllPaymentSources(),
                'selected' => $paymentsFilterForm['paymentSources'] ?? '',
                'attr' => [
                    'name' => 'paymentsFilterForm[paymentSources][]',
                    'data-boostrap-selectpicker' => 'true',
                    'multiple' => 'multiple',
                ]
            ])
            ->add('loanId', 'text', [
                'label' => __('table.numberLoan'),
                'value' => $paymentsFilterForm['loanId'] ?? '',
                'attr' => []
            ])
            ->add('isActive', 'select', [
                'label' => __('table.Status'),
                'selected' => $paymentsFilterForm['isActive'] ?? '',
                'empty_value' => __('Select option'),
                'choices' => [
                    1 => __('Active'),
                    0 => __('InActive'),
                ],
            ]);
    }

    /**
     * @return array
     */
    private function getAllPaymentSources(): array
    {
        $sources = Payment::getAllPaymentSources();
        $out = [];
        foreach ($sources as $source) {
            $out[$source] = __('payments::PaymentSource.' . $source);
        }

        return $out;
    }
}
