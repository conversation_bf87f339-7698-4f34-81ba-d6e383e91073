<?php

namespace Modules\Head\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\Client;
use Modules\Common\Models\CreditLimit;
use Modules\Common\Models\CreditLimitRule;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Services\BaseService;
use Modules\Head\Libraries\CreditLimitScore;

// TODO: move recomendation to const and change them in unit test
class CreditLimitService extends BaseService
{
    private $debug = false;

    public $decisionLog = [
        'failed' => [],
        'passed' => [],
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /////////////////////////////// CREDIT LIMIT ///////////////////////////////

    public function addCreditLimitByLoanAndA4EReport(
        Loan      $loan,
        A4EReport $report,
        bool $debug = false
    ): ?CreditLimit {

        // $this->debug = $debug;


        $this->decisionLog['failed'] = [];
        $this->decisionLog['passed'] = [];

        // we use credit limits only for online
        if (!$loan->isOnlineLoan()) {
            $loan->addMeta('credit_limit_fail', '!isOnlineLoan');
            $this->decisionLog['failed'][] = '!isOnlineLoan';
            return null;
        }

        if (empty($report->gb) || empty($report->gbr)) {
            $loan->addMeta('credit_limit_fail', '!gb || !gbr');
            $this->decisionLog['failed'][] = '!gb || !gbr';
            return null;
        }

        $client = $loan->client;

        $ruleType = CreditLimitScore::getRuleTypeByClient($client);
        if (empty($ruleType)) {
            $loan->addMeta('credit_limit_fail', '!$ruleType');
            $this->decisionLog['failed'][] = '!$ruleType';
            return null;
        }

        $rule = $this->getActiveRule($report->gb, $report->gbr, $ruleType);
        // exit if there is no rule for such quandrant
        if (empty($rule->rule_id)) {
            $loan->addMeta('credit_limit_fail', '!$rule');
            $this->decisionLog['failed'][] = '!$rule';
            return null;
        }


        // get decision
        $decisionData = $this->getCreditLimitDecision(
            $client,
            $rule,
            intToFloat($loan->amount_requested)
        );


        // check for mandatory amount
        $creditLimitAmount = $decisionData['amount_max'] ?? 0;
        if (empty($creditLimitAmount)) {
            $loan->addMeta('credit_limit_fail', '!$creditLimitAmount');
            $this->decisionLog['failed'][] = '!$creditLimitAmount';
            return null;
        }


        // add approve action task - used in Approve listing and in Client Card
        if ($loan->amount_approved >= floatToInt($creditLimitAmount)) {
            $loan->addCreditLimitApproveTask();
        }


        // save result in db
        $cl = $this->create([
            'client_id' => $client->client_id,
            'loan_id' => $loan->loan_id,
            'rule_id' => $rule->rule_id,
            'a4e_report_id' => $report->a4e_report_id,
            'params' => json_encode($decisionData),
            'amount' => $creditLimitAmount,
        ]);
        if (empty($cl->credit_limit_id)) {
            $loan->addMeta('credit_limit_fail', '!$cl');
            $this->decisionLog['failed'][] = '!$cl';
            return null;
        }


        return $cl;
    }

    public function create(array $data): CreditLimit
    {
        $data['last'] = 1;

        $cl = new CreditLimit();
        $cl->fill($data);

        if (!empty($cl->client_id) && !empty($cl->loan_id)) {
            CreditLimit::where([
                ['client_id', '=', $cl->client_id],
                ['loan_id', '=', $cl->loan_id],
                ['last', '=', 1],
            ])->update(['last' => 0]);
        }

        $cl->save();

        return $cl;
    }

    public function getCreditLimitForLoan(Loan $loan): ?CreditLimit
    {
        return CreditLimit::where('loan_id', $loan->loan_id)
            ->where('active', 1)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    ///////////////////////// CREDIT LIMIT RULES ///////////////////////////////


    public function getRulesByFilters(
        int   $limit,
        array $filters,
        array $order = ['rule_id' => 'DESC']
    )
    {
        $where = [
            ['credit_limit_rule.deleted', '=', '0'],
        ];
        if (!empty($filters)) {
            foreach ($filters as $field => $value) {
                if (!empty($value) || $value == "0") {
                    $where[] = ['credit_limit_rule.' . $field, '=', $value];
                }
            }
        }

        $builder = DB::table('credit_limit_rule');
        $builder->select(DB::raw('
            credit_limit_rule.*,
            administrator.first_name,
            administrator.last_name
        '));

        $builder->join(
            'administrator',
            'administrator.administrator_id',
            '=',
            'credit_limit_rule.created_by'
        );

        $builder->where($where);

        if (!empty($order)) {
            foreach ($order as $oK => $oV) {
                $builder->orderBy($oK, $oV);
            }
        }

        return $builder->paginate($limit);
    }

    public function createRule(array $data): CreditLimitRule
    {
        $rule = new CreditLimitRule();
        $rule->fill($data);

        if (
            $rule->active == 1
            && !empty($rule->card1)
            && !empty($rule->card2)
            && !empty($rule->type)
        ) {
            $this->findSimilarAndDeactivate(
                $rule->card1,
                $rule->card2,
                $rule->type
            );
        }

        $rule->created_at = now();
        $rule->created_by = getAdminId();

        $rule->save();

        return $rule;
    }

    public function updateRule(
        CreditLimitRule $rule,
        array           $data
    ): CreditLimitRule
    {

        $rule->fill($data);

        if (
            $rule->active == 1
            && !empty($rule->card1)
            && !empty($rule->card2)
            && !empty($rule->type)
        ) {
            $this->findSimilarAndDeactivate(
                $rule->card1,
                $rule->card2,
                $rule->type,
                $rule->rule_id
            );
        }

        $rule->updated_at = now();
        $rule->updated_by = getAdminId();
        $rule->save();

        return $rule;
    }

    public function getActiveRule(
        string $card1,
        string $card2,
        string $type,
        ?int   $ruleId = null
    ): ?CreditLimitRule
    {
        $condition = [
            ['card1', '=', $card1],
            ['card2', '=', $card2],
            ['type', '=', $type],
            ['active', '=', '1'],
            ['deleted', '=', '0'],
        ];

        if (!empty($ruleId)) {
            $condition[] = ['rule_id', '!=', $ruleId];
        }

        return CreditLimitRule::where($condition)->first();
    }

    public function findSimilarAndDeactivate(
        string $card1,
        string $card2,
        string $type,
        ?int   $ruleId = null
    ): bool
    {

        $rule = $this->getActiveRule($card1, $card2, $type, $ruleId);
        if (empty($rule->rule_id)) {
            return false;
        }

        $rule->active = 0;
        $rule->save();

        return true;
    }

    public function getCreditLimitDecision(
        Client          $client,
        CreditLimitRule $rule,
        float           $amountRequested
    ): array
    {

        $decision = [];
        $details  = [];

        // has repaid loans, needs recalculating of credit limits
        if (
            $rule->type == CreditLimitScore::RULE_TYPE_OLD
            || $rule->type == CreditLimitScore::RULE_1ST_ACTIVE
        ) {

            // get overdue vars
            $ruleMaxOverdueForUp = (int)$rule->overdue_days_up;
            $ruleMinOverdueForDown = (int)$rule->overdue_days_down;
            $maxOverdueDays = (int)$client->getMaxDaysOverdueForCreditLimit();
            $details['ruleMaxOverdueForUp'] = $ruleMaxOverdueForUp;
            $details['ruleMinOverdueForDown'] = $ruleMinOverdueForDown;
            $details['maxOverdueDays'] = $maxOverdueDays;


            // get last increase credit limit date
            $crUpdatedAt = $client->getClientCreditLimitLastUpdatedDate();

            $daysPassed = null;
            if (!empty($crUpdatedAt)) {
                $date = Carbon::parse($crUpdatedAt);
                $now = Carbon::now();
                $daysPassed = $date->diffInDays($now);

                $details['daysPassed'] = $daysPassed;
                $details['daysPassedBasedOn'] = 'crUpdatedAt';
                $details['crUpdatedAt'] = $date->format('Y-m-d H:i:s');
            }

            if (empty($daysPassed)) {
                //  Ако клиент има изплатени N на брой кредита в офис и кандидатства за кредит на сайта, то той ще бъде клиент с изплатени кредити,
                // но без кредитен лимит. В този случай за кредитен лимит се приема размера на последно изплатения кредит
                // и като дата на последна промяна на кредитния лимит се приема датата на усвояване на последния кредит.
                // Ако има активен кредит стойностите се вземат на база активния кредит.

                $lastLoan = $client->getLastActiveOrRepaidLoan();
                if ($lastLoan?->activated_at) {
                    $daysPassed = Carbon::parse($lastLoan->activated_at)->diffInDays(Carbon::now());

                    $details['daysPassed'] = $daysPassed;
                    $details['daysPassedBasedOn'] = 'lastLoan';
                    $details['lastLoanId'] = $lastLoan->loan_id;
                    $details['lastLoanActivatedAt'] = Carbon::parse($lastLoan->activated_at)->format('Y-m-d H:i:s');
                }
            }


            // check if we can use UP rules
            $couldIncrease = (
                null === $daysPassed // we dont have log about last credit limit increase
                || empty($rule->min_period_up) // rule has no value for key
                || ($daysPassed >= $rule->min_period_up) // rule passed
            );
            $overdueIsOk = (
                $ruleMaxOverdueForUp <= 0 // rule has no value for key
                || $maxOverdueDays <= $ruleMaxOverdueForUp // rule passed
            );

            $rule->step_up = (int) $rule->step_up;
            $rule->step_down = (int) $rule->step_down;

            $details['couldIncrease'] = (int) $couldIncrease;
            $details['overdueIsOk'] = (int) $overdueIsOk;


            // we trying to go UP
            if (
                $couldIncrease
                && $overdueIsOk
                && $rule->step_up >= 0
                && $rule->amount_min > 0
            ) {
                // Ако кредитният лимит трябва да се увеличи, се взема
                // Начален кредитен лимит на настройката + (стъпка при увеличение умножена по броя изплатени кредити към момента на заявка).
                $repaidLoansCount = $client->getRepaidLoansCount();
                $amountMax = $rule->amount_min + ($rule->step_up * $repaidLoansCount);

                $details['repaidLoansCount'] = $repaidLoansCount;
                $details['amountMax'] = $amountMax;
                $details['rule_step_up'] = $rule->step_up;
                $details['rule_amount_min'] = $rule->amount_min;

                $decision = [
                    'amount_min' => $rule->amount_min ?? null,
                    'amount_max' => $amountMax,
                    'amount_requested' => $amountRequested,
                    'action' => 'up',
                ];

            } else if (
                $rule->step_down >= 0
                && $ruleMinOverdueForDown > 0
                && $maxOverdueDays >= $ruleMinOverdueForDown
            ) { // we go DOWN

                $lastCreditLimit = $client->getCreditLimitOfLastActiveLoan();

                $details['lastCreditLimit'] = $lastCreditLimit;
                $details['rule_step_down'] = $rule->step_down;

                // important that previous loans should not be from office,
                // so they have the previous value - credit_limit
                // Ако кредитният лимит трябва да се понижи, се взема кредитният лимит на последният усвоен кредит
                // (а не одобрена заявка)  и от него се изважда стъпката за понижение.
                if (!empty($lastCreditLimit)) {
                    $decision = [
                        'amount_min' => $rule->amount_min ?? null,
                        'amount_max' => $lastCreditLimit - $rule->step_down,
                        'amount_requested' => $amountRequested,
                        'action' => 'down',
                        'reuse' => 'credit limit of last active loan as max border, = ' . $lastCreditLimit . ' - step_down',
                    ];
                } else {
                    $lastLoan = !empty($lastLoan->loan_id) ? $lastLoan : $client->getLastActiveOrRepaidLoan();
                    if (!empty($lastLoan->loan_id) && !empty($lastLoan->amount_approved)) {
                        $decision = [
                            'amount_min' => $rule->amount_min ?? null,
                            'amount_max' => (int) intToFloat($lastLoan->amount_approved) - $rule->step_down,
                            'amount_requested' => $amountRequested,
                            'action' => 'down',
                            'reuse' => 'approved amount of last active loan as max border, = ' . intToFloat($lastLoan->amount_approved) . ' - step_down',
                        ];
                    }
                }
            }


            // credit limits should be the same
            // Ако кредитният лимит трябва да се запази непроменен, се взема кредитният лимит на последния усвоен кредит.
            if (empty($decision)) {
                $lastCreditLimit = $client->getCreditLimitOfLastActiveLoan();
                if (!empty($lastCreditLimit)) {
                    $decision = [
                        'amount_min' => $rule->amount_min ?? null,
                        'amount_max' => $lastCreditLimit,
                        'amount_requested' => $amountRequested,
                        'action' => 'ignore',
                        'reuse' => 'credit limit of last active loan as max border, = ' . $lastCreditLimit,
                    ];
                } else {
                    $lastLoan = !empty($lastLoan->loan_id) ? $lastLoan : $client->getLastActiveOrRepaidLoan();
                    if (!empty($lastLoan->loan_id) && !empty($lastLoan->amount_approved)) {
                        $decision = [
                            'amount_min' => $rule->amount_min ?? null,
                            'amount_max' => (int) intToFloat($lastLoan->amount_approved),
                            'amount_requested' => $amountRequested,
                            'action' => 'ignore',
                            'reuse' => 'approved amount of last active loan as max border, = ' . intToFloat($lastLoan->amount_approved),
                        ];
                    }
                }
            }
        }

        // if new client or did not find a rule params for calc new credit limit => use defaults
        if (empty($decision)) {
            $details['no_decision_taken'] = 'use rule';
            $decision = $this->getCreditLimitDecisionByMinMax(
                $rule,
                $amountRequested
            );
        }


        // Костъль!
        if (!empty($decision['amount_min'])) {
            $decision['amount_min'] = (int)$decision['amount_min'];
        }
        if (!empty($decision['amount_max'])) {
            $decision['amount_max'] = (int)$decision['amount_max'];
        }


        $decision = $this->adoptLimits($rule, $decision);
        $decision['details'] = $details;

        return $this->addRecomendation($decision);
    }

    private function getCreditLimitDecisionByMinMax(
        CreditLimitRule $rule,
        float           $amountRequested
    ): array
    {

        $decision = [
            'amount_min' => $rule->amount_min ?? null,
            'amount_max' => $rule->amount_max ?? null,
            'amount_requested' => $amountRequested,
            'action' => 'init',
            'usage' => 'use rule borders',
        ];

        return $decision;
    }

    private function adoptLimits(CreditLimitRule $rule, array $decision): array
    {
        // case when max border is based on prev.loan amount or prev.credit limit
        if (
            !empty($rule->amount_min)
            && (!empty($decision['amount_max']) || (isset($decision['amount_max']) && $decision['amount_max'] == 0))
            && $decision['amount_max'] < $rule->amount_min
        ) {
            $decision['changed_max'] = 'max less then min: ' . $decision['amount_max'] . ' => ' . $rule->amount_min;
            $decision['amount_max'] = $rule->amount_min;
        }

        if (
            !empty($rule->amount_max)
            && (
                empty($decision['amount_max'])
                || (
                    !empty($decision['amount_max'])
                    && $decision['amount_max'] > $rule->amount_max
                )
            )
        ) {
            $decision['changed_max'] = 'max bigger then rule max: ' . $decision['amount_max'] . ' => ' . $rule->amount_max;
            $decision['amount_max'] = $rule->amount_max;
        }

        if (
            !empty($rule->amount_min)
            && (
                empty($decision['amount_min'])
                || (
                    !empty($decision['amount_min'])
                    && $decision['amount_min'] < $rule->amount_min
                )
            )
        ) {
            $decision['changed_max'] = 'min less then rule min: ' . $decision['amount_min'] . ' => ' . $rule->amount_min;
            $decision['amount_min'] = $rule->amount_min;
        }

        return $decision;
    }

    private function addRecomendation(array $decision): array
    {
        if (!empty($decision['amount_max']) && $decision['amount_requested'] > $decision['amount_max']) {
            $decision['recommendation'] = 'Заявката да се одобри за по-ниска сума - ' . $decision['amount_max'] . ' лева.';
        } else if (!empty($decision['amount_min']) && $decision['amount_requested'] < $decision['amount_min']) {
            $decision['recommendation'] = 'Заявката да се одобри за по-висока сума - ' . $decision['amount_min'] . ' лева.';
        } else {
            $decision['recommendation'] = 'Заявката да се одобри за заявената сума.';
        }

        return $decision;
    }
}
