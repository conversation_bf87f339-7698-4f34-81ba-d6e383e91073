<?php

namespace Modules\Head\Services;

use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\BlockReason;
use Modules\Common\Models\DeleteReason;
use Modules\Common\Services\BaseService;
use Modules\Head\Exceptions\HeadException;
use Modules\Head\Repositories\BlockReasonRepository;
use Modules\Head\Repositories\DeleteReasonRepository;

class ReasonService extends BaseService
{
    protected DeleteReasonRepository $deleteReasonRepository;
    protected BlockReasonRepository $blockReasonRepository;

    /**
     * BlockReasonService constructor.
     *
     * @param DeleteReasonRepository $deleteReasonRepository
     * @param BlockReasonRepository $blockReasonRepository
     */
    public function __construct(
        DeleteReasonRepository $deleteReasonRepository,
        BlockReasonRepository  $blockReasonRepository
    )
    {
        $this->deleteReasonRepository = $deleteReasonRepository;
        $this->blockReasonRepository = $blockReasonRepository;

        parent::__construct();
    }

    public function getDeleteReasons(array $where = ['active' => '1', 'deleted' => '0'])
    {
        return $this->deleteReasonRepository->getAll($where);
    }

    /**
     * @param array $data
     *
     * @throws ProblemException
     */
    public function createDeleteReason(array $data)
    {
        try {
            $this->deleteReasonRepository->create($data);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::deleteReasonCrud.CreationFailed'),
                $e
            );
        }
    }

    /**
     * @param DeleteReason $deleteReason
     * @param array $data
     *
     * @throws ProblemException
     */
    public function editDeleteReason(DeleteReason $deleteReason, array $data)
    {
        try {
            $this->deleteReasonRepository->edit($deleteReason, $data);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::deleteReasonCrud.UpdateFailed'),
                $e
            );
        }
    }

    /**
     * @param DeleteReason $deleteReason
     *
     * @throws ProblemException
     */
    public function deleteDeleteReason(DeleteReason $deleteReason)
    {
        try {
            $this->deleteReasonRepository->delete($deleteReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::deleteReasonCrud.DeletionFailed'),
                $e
            );
        }
    }

    /**
     * @param DeleteReason $deleteReason
     *
     * @throws ProblemException
     */
    public function enableDeleteReason(DeleteReason $deleteReason)
    {
        if ($deleteReason->isActive()) {
            throw new ProblemException(__('head::deleteReasonCrud.EnableForbidden'));
        }

        try {
            $this->deleteReasonRepository->enable($deleteReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::deleteReasonCrud.EnableFailed'),
                $e
            );
        }
    }

    /**
     * @param DeleteReason $deleteReason
     *
     * @throws ProblemException
     */
    public function disableDeleteReason(DeleteReason $deleteReason)
    {
        if (!$deleteReason->isActive()) {
            throw new ProblemException(__('head::deleteReasonCrud.DisableForbidden'));
        }

        try {
            $this->deleteReasonRepository->disable($deleteReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::deleteReasonCrud.DisableFailed'),
                $e
            );
        }
    }

    /**
     * @param array $where
     *
     * @return mixed
     */
    public function getBlockReasons(array $where = ['active' => '1', 'deleted' => '0'])
    {
        return $this->blockReasonRepository->getAll($where);
    }

    /**
     * @param array $data
     *
     * @throws ProblemException
     */
    public function createBlockReason(array $data)
    {
        try {
            $this->blockReasonRepository->create($data);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::blockReasonCrud.CreationFailed'),
                $e
            );
        }
    }

    /**
     * @param BlockReason $blockReason
     * @param array $data
     *
     * @throws ProblemException
     */
    public function editBlockReason(BlockReason $blockReason, array $data)
    {
        try {
            $this->blockReasonRepository->edit($blockReason, $data);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::blockReasonCrud.UpdateFailed'),
                $e
            );
        }
    }

    /**
     * @param BlockReason $blockReason
     *
     * @throws ProblemException
     */
    public function deleteBlockReason(BlockReason $blockReason)
    {
        try {
            $this->blockReasonRepository->delete($blockReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::blockReasonCrud.DeletionFailed'),
                $e
            );
        }
    }

    /**
     * @param BlockReason $blockReason
     *
     * @throws NotFoundException
     * @throws ProblemException
     */
    public function enableBlockReason(BlockReason $blockReason)
    {
        if ($blockReason->isActive()) {
            throw new ProblemException(__('head::blockReasonCrud.EnableForbidden'));
        }

        try {
            $this->blockReasonRepository->enable($blockReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::blockReasonCrud.EnableFailed'),
                $e
            );
        }
    }

    /**
     * @param BlockReason $blockReason
     *
     * @throws ProblemException
     */
    public function disableBlockReason(BlockReason $blockReason)
    {
        if (!$blockReason->isActive()) {
            throw new ProblemException(__('head::blockReasonCrud.DisableForbidden'));
        }

        try {
            $this->blockReasonRepository->disable($blockReason);
        } catch (\Exception $e) {
            throw new HeadException(
                __('head::blockReasonCrud.DisableFailed'),
                $e
            );
        }
    }
}
