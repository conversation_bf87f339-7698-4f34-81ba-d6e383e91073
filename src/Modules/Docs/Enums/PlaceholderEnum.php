<?php

namespace Modules\Docs\Enums;

use Modules\Docs\Domain\DataSource\Client;
use Modules\Docs\Domain\DataSource\Custom;
use Modules\Docs\Domain\DataSource\Environment;
use Modules\Docs\Domain\DataSource\Loan;
use Modules\Docs\Domain\DataSource\Office;

enum PlaceholderEnum: string
{
    case CLIENT_FIRST_NAME = 'client_first_name';
    case CLIENT_MIDDLE_NAME = 'client_middle_name';
    case CLIENT_SURNAME = 'client_surname';
    case CLIENT_PIN = 'client_pin';
    case CLIENT_ID_NUMBER = 'client_id_number';
    case CLIENT_PRIMARY_PHONE = 'client_primary_phone';
    case CLIENT_EMAIL_ADDRESS = 'client_email_address';
    case CLIENT_ID_ISSUE_DATE = 'client_id_issue_date';
    case CLIENT_ID_EXPIRATION_DATE = 'client_id_expiration_date';
    case CLIENT_ID_ISSUER_NAME = 'client_id_issuer_name';
    case CLIENT_PERMANENT_ADDRESS_SETTLEMENT = 'client_permanent_address_settlement';
    case CLIENT_PERMANENT_ADDRESS_MUNICIPALITY = 'client_permanent_address_municipality';
    case CLIENT_PERMANENT_ADDRESS_DISTRICT = 'client_permanent_address_district';
    case CLIENT_PERMANENT_ADDRESS_LOCATION_NAME = 'client_permanent_address_location_name';
    case CLIENT_PERMANENT_ADDRESS_BUILDING_NUMBER = 'client_permanent_address_building_number';
    case CLIENT_PERMANENT_ADDRESS_BUILDING_ENTRANCE = 'client_permanent_address_building_entrance';
    case CLIENT_PERMANENT_ADDRESS_BUILDING_FLOOR = 'client_permanent_address_building_floor';
    case CLIENT_PERMANENT_ADDRESS_BUILDING_APARTMENT = 'client_permanent_address_building_apartment';
    case CLIENT_PERMANENT_ADDRESS_FULL = 'client_permanent_address_full';
    case CLIENT_TEMPORARY_ADDRESS_SETTLEMENT = 'client_temporary_address_settlement';
    case CLIENT_TEMPORARY_ADDRESS_MUNICIPALITY = 'client_temporary_address_municipality';
    case CLIENT_TEMPORARY_ADDRESS_DISTRICT = 'client_temporary_address_district';
    case CLIENT_TEMPORARY_ADDRESS_LOCATION_NAME = 'client_temporary_address_location_name';
    case CLIENT_TEMPORARY_ADDRESS_BUILDING_NUMBER = 'client_temporary_address_building_number';
    case CLIENT_TEMPORARY_ADDRESS_BUILDING_ENTRANCE = 'client_temporary_address_building_entrance';
    case CLIENT_TEMPORARY_ADDRESS_BUILDING_FLOOR = 'client_temporary_address_building_floor';
    case CLIENT_TEMPORARY_ADDRESS_BUILDING_APARTMENT = 'client_temporary_address_building_apartment';
    case CLIENT_TEMPORARY_ADDRESS_FULL = 'client_temporary_address_full';
    case CLIENT_DISCOUNT = 'client_discount';
    case CLIENT_CREDIT_LIMIT = 'client_credit_limit';
    case CLIENT_IDCARD_2SIDES = 'client_idcard_2sides';
    case GUARANTOR_1_PHONE = 'guarant_1_phone';
    case GUARANTOR_1_EMAIL = 'guarant_1_email';
    case GUARANTOR_1_PIN = 'guarant_1_pin';
    case GUARANTOR_1_ID_NUMBER = 'guarant_1_id_number';
    case GUARANTOR_1_FIRST_NAME = 'guarant_1_first_name';
    case GUARANTOR_1_MIDDLE_NAME = 'guarant_1_middle_name';
    case GUARANTOR_1_SURNAME = 'guarant_1_surname';
    case GUARANTOR_1_ID_ISSUE_DATE = 'guarant_1_id_issue_date';
    case GUARANTOR_1_ID_EXPIRATION_DATE = 'guarant_1_id_expiration_date';
    case GUARANTOR_1_ID_ISSUER_NAME = 'guarant_1_id_issuer_name';
    case GUARANTOR_1_ADDRESS_CURRENT = 'guarant_1_address_current';
    case GUARANTOR_1_ADDRESS_PERMANENT = 'guarant_1_address_permanent';
    case GUARANTOR_2_PHONE = 'guarant_2_phone';
    case GUARANTOR_2_EMAIL = 'guarant_2_email';
    case GUARANTOR_2_PIN = 'guarant_2_pin';
    case GUARANTOR_2_ID_NUMBER = 'guarant_2_id_number';
    case GUARANTOR_2_FIRST_NAME = 'guarant_2_first_name';
    case GUARANTOR_2_MIDDLE_NAME = 'guarant_2_middle_name';
    case GUARANTOR_2_SURNAME = 'guarant_2_surname';
    case GUARANTOR_2_ID_ISSUE_DATE = 'guarant_2_id_issue_date';
    case GUARANTOR_2_ID_EXPIRATION_DATE = 'guarant_2_id_expiration_date';
    case GUARANTOR_2_ID_ISSUER_NAME = 'guarant_2_id_issuer_name';
    case GUARANTOR_2_ADDRESS_CURRENT = 'guarant_2_address_current';
    case GUARANTOR_2_ADDRESS_PERMANENT = 'guarant_2_address_permanent';
    case LOAN_HAS_GUARANT = 'loan_has_guarant';
    case LOAN_EPAY_MONEY_TOOKED_AT = 'loan_easypay_money_tooked_at';
    case APPLICATION_ID = 'application_id';
    case APPLICATION_DATE = 'application_date';
    case APPLICATION_DATE_2 = 'application_date_2';
    case APPLICATION_TIME = 'application_time';
    case LOAN_AMOUNT = 'loan_amount';
    case LOAN_AMOUNT_IN_WRITING = 'loan_amount_in_writing';
    case LOAN_AMOUNT_IN_WRITING_SIMPLE = 'loan_amount_in_writing_simple';
    case LOAN_INTEREST_RATE = 'loan_interest_rate';
    case LOAN_INTEREST_RATE_PER_DAY = 'loan_interest_rate_per_day';
    case LOAN_INTEREST_RATE_IN_WRITING_PER_DAY = 'loan_interest_rate_in_writing_per_day';
    case LOAN_INTEREST_RATE_IN_WRITING = 'loan_interest_rate_in_writing';
    case LOAN_OUTSTANDING_AMOUNT_TOTAL = 'loan_outstanding_amount_total';
    case LOAN_GPR = 'loan_gpr';
    case LOAN_GPR_IN_WRITING = 'loan_gpr_in_writing';
    case TOTAL_DUE_PRINCIPAL_INTEREST = 'total_due_principal_interest';
    case TOTAL_DUE_PRINCIPAL_INTEREST_IN_WRITING = 'total_due_principal_interest_in_writing';
    case TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY = 'total_due_principal_interest_penalty';
    case TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY_IN_WRITING = 'total_due_principal_interest_penalty_in_writing';
    case TOTAL_DUE_INTEREST = 'total_due_interest';
    case TOTAL_DUE_INTEREST_IN_WRITING = 'total_due_interest_in_writing';
    case TOTAL_DUE_PENALTY = 'total_due_penalty';
    case LOAN_PENALTY_SUM = 'loan_penalty_sum';
    case LOAN_PENALTY_SUM_IN_WRITING = 'loan_penalty_sum_in_writing';
    case LOAN_TERM = 'loan_term';
    case LOAN_TERM_IN_WRITING = 'loan_term_in_writing';
    case LOAN_OVERDUE_INTEREST_RATE = 'loan_overdue_interest_rate';
    case LOAN_PAYMENT_METHOD = 'loan_payment_method';
    case LOAN_LAST_INSTALLMENT_DATE = 'loan_last_instalment_date';
    case LOAN_FIRST_INSTALLMENT_DATE = 'loan_first_instalment_date';
    case LOAN_INSTALLMENT_SUM = 'loan_instalment_sum';
    case LOAN_INSTALLMENT_LIST_PRINCIPAL_INTEREST = 'loan_instalment_list_principal_interest';
    case LOAN_PAYMENT_PERIOD = 'loan_payment_period';
    case LOAN_INSTALLMENT_COUNT = 'loan_instalment_count';
    case LOAN_INSTALLMENT_COUNT_IN_WRITING = 'loan_instalment_count_in_writing';
    case LOAN_NEXT_UNPAID_INSTALLMENT_DATE = 'loan_next_unpaid_installment_date';
    case LOAN_LAST_UNPAID_INSTALLMENT_DATE = 'loan_last_unpaid_installment_date';
    case LOAN_EARLY_REPAYMENT_AMOUNT = 'loan_early_repayment_amount';
    case LOAN_REPAYMENT_SCHEDULE = 'loan_repayment_schedule';
    case LOAN_REPAYMENT_SCHEDULE_NO_PENALTY = 'loan_repayment_schedule_no_penalty';
    case LOAN_EMAIL_HISTORY = 'loan_email_history';
    case LOAN_EMAIL_HISTORY_FOR_LEGAL = 'loan_email_history_for_legal';
    case CREDIT_LIMIT = 'credit_limit';
    case REFINANCE_RECEIVE_AMOUNT = 'refinance_receive_amount';
    case REFINANCE_DUE_AMOUNT = 'refinance_due_amount';
    case LOAN_EXTEND_AMOUNT = 'loan_extend_amount';
    case LOAN_EXTEND_AMOUNT_15_DAYS = 'loan_extend_amount_15_days';
    case ACTIVE_LOAN_TABLE = 'active_loan_table';
    case OFFICE_NAME = 'office_name';
    case OFFICE_PHONE = 'office_phone';
    case OFFICE_ADDRESS = 'office_address';
    case OFFICE_BANK_DETAILS = 'office_bank_details';
    case TODAY_DATE = 'today_date';
    case TODAY_DATE_2 = 'today_date_2';
    case TEXT_MESSAGE_HISTORY_LEGAL = 'text_message_history_legal';
    case IP_APPLICATION_SUBMIT = 'ip_application_submit';
    case APPLICATION_SIGN_DATE = 'application_sign_date';
    case APPLICATION_SIGN_TIME = 'application_sign_time';
    case IP_APPLICATION_SIGN = 'ip_application_sign';
    case CLIENT_NAME_PREFIX = 'client_name_prefix';
    case COMPANY_PIN = 'company_pin';
    case COMPANY_NAME = 'company_name';
    case COMPANY_NAME_LATIN = 'company_name_latin';
    case COMPANY_PHONE = 'company_phone';
    case COMPANY_EMAIL = 'company_email';
    case COMPANY_WEB_PAGE = 'company_web_page';
    case COMPANY_SMS_LOGIN_URL = 'company_sms_login_url';
    case COMPANY_REGISTERED_ADDRESS = 'company_registered_address';
    case COMPANY_OFFICE_ADDRESS_SETTLEMENT = 'company_office_address_settlement';
    case COMPANY_OFFICE_IBAN = 'company_office_iban';
    case COMPANY_EASY_PAY_PIN = 'company_easypay_pin';
    case COMPANY_OFFICE_ADDRESS = 'company_office_address';
    case EMPLOYEE_NAME = 'employee_name';
    case DOCUMENT_ID = 'document_id';
    case DOCUMENT_CREATION_DATE = 'document_creation_date';
    case CLIENT_DEBIT_CARD_NUMBER = 'client_debit_card_number';
    case CLIENT_DEBIT_CARD_ISSUING_BANK = 'client_debit_card_issuing_bank';
    case PROFILE_LOGIN_CODE = 'profile_login_code';
    // case POLL = 'poll';
    // case EARLY_REPAYMENT_DATE = 'early_repayment_date';
    // case EARLY_REPAYMENT_AMOUNT = 'early_repayment_amount';
    case DUE_AMOUNT = 'due_amount';
    case HASH = 'hash';
    case OFFICE_WORKING_HOURS = 'office_working_hours';
    case OFFICE_WORKING_HOURS_LAT = 'office_working_hours_latin';
    case OFFICE_PHONE_NUM = 'office_phone_number';
    case MANAGER_SIGNATURE_IMAGE = 'manager_signature_image';

    // cashdesk vars
    case CD_ORDER_NUMBER = 'order_number';
    case CD_TRANSAC_DATE = 'transaction_date';
    case CD_REPRESENTOR_OR_FIRM = 'representator_or_firm';
    case CD_ATTORNEY_NUMBER = 'attorney_number';
    case CD_ATTORNEY_DATE = 'attorney_date';
    case CD_BASIS = 'basis';
    case CD_PAYMENT_AMOUNT = 'payment_amount';
    case CD_PAYMENT_AMOUNT_IN_WRITING = 'payment_amount_in_writing';
    case CD_PAID_PRINCIPLE = 'paid_principal';
    case CD_PAID_INTEREST = 'paid_interest';
    case CD_PAID_PENALTY = 'paid_penalty';
    case CD_PAID_LATE_INTEREST = 'paid_late_interest';
    case CD_PAID_TAXES = 'paid_taxes';

    // case DAYS = 'days';
    // case DISCOUNT_AMOUNT = 'discount_amount';
    case UNDEFINED_PLACEHOLDER = 'undefined_placeholder';

    case SIGN_DOCUMENTS_LINK = 'sign_documents_link';
    case OFFICES_INFO_LINK = 'offices_info_link';
    case HOW_TO_PAY_LINK = 'how_to_pay_link';
    case BLOG_LINK = 'blog_link';
    case FREQUENTLY_QUESTIONS_LINK = 'frequently_questions_link';

    public function required(): bool
    {
        return match ($this) {
            self::CLIENT_TEMPORARY_ADDRESS_SETTLEMENT,
                //self::CLIENT_TEMPORARY_ADDRESS_DISTRICT,
            self::CLIENT_PERMANENT_ADDRESS_SETTLEMENT,
            self::COMPANY_PHONE => true,
            default => false
        };
    }

    public function type(): string
    {
        return match ($this) {
            self::CLIENT_FIRST_NAME,
            self::CLIENT_MIDDLE_NAME,
            self::CLIENT_SURNAME => 'PROPER_NAME',
            self::APPLICATION_DATE,
            self::LOAN_FIRST_INSTALLMENT_DATE,
            self::LOAN_NEXT_UNPAID_INSTALLMENT_DATE,
            self::LOAN_LAST_UNPAID_INSTALLMENT_DATE,
            self::CLIENT_ID_ISSUE_DATE,
            self::APPLICATION_SIGN_DATE,
            self::GUARANTOR_1_ID_ISSUE_DATE,
            self::GUARANTOR_2_ID_ISSUE_DATE,
            self::GUARANTOR_1_ID_EXPIRATION_DATE,
            self::GUARANTOR_2_ID_EXPIRATION_DATE,
            self::TODAY_DATE => 'DATE_INTL',
            self::APPLICATION_DATE_2,
            self::LOAN_LAST_INSTALLMENT_DATE,
            self::TODAY_DATE_2 => 'DATE_BG',
            self::APPLICATION_TIME,
            self::APPLICATION_SIGN_TIME => 'TIME',

            self::LOAN_AMOUNT => 'AMOUNT',
            self::TOTAL_DUE_PRINCIPAL_INTEREST,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY,
            self::TOTAL_DUE_INTEREST,
            self::LOAN_PENALTY_SUM,
            self::DUE_AMOUNT,
            self::LOAN_INSTALLMENT_SUM => 'READY_AMOUNT',

            self::LOAN_AMOUNT_IN_WRITING,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_IN_WRITING,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY_IN_WRITING,
            self::TOTAL_DUE_INTEREST_IN_WRITING,
            self::LOAN_PENALTY_SUM_IN_WRITING,
            self::LOAN_EARLY_REPAYMENT_AMOUNT => 'NUMBER_OF_LEV',

            self::LOAN_AMOUNT_IN_WRITING_SIMPLE => 'NUMBER_WRITING',

            self::LOAN_INTEREST_RATE_IN_WRITING_PER_DAY,
            self::LOAN_INTEREST_RATE_IN_WRITING,
            self::LOAN_GPR_IN_WRITING => 'WORDED_PERCENT',
            self::LOAN_REPAYMENT_SCHEDULE => 'REPAYMENT_HTML',
            self::LOAN_REPAYMENT_SCHEDULE_NO_PENALTY => 'REPAYMENT_NO_PENALTY_HTML',
            self::ACTIVE_LOAN_TABLE => 'ACTIVE_LOANS_HTML',
            self::MANAGER_SIGNATURE_IMAGE => 'IMAGE',
            self::LOAN_INTEREST_RATE_PER_DAY,
            self::LOAN_INTEREST_RATE => 'READY_AMOUNT',

            self::CLIENT_IDCARD_2SIDES => 'ID_CARD_HTML',

            self::LOAN_EMAIL_HISTORY => 'EMAIL_HISTORY_HTML',
            self::LOAN_EMAIL_HISTORY_FOR_LEGAL => 'EMAIL_HISTORY_FOR_LEGAL_HTML',

            default => $this->value,
        };
    }

    public static function getAllDocVarsGrouped(): array
    {
        return [
            'client' => array_fill_keys([
                'client_first_name',
                'client_middle_name',
                'client_surname',
                'client_pin',
                'client_id_number',
                'client_id_issue_date',
                'client_id_expiration_date',
                'client_id_issuer_name',
                'client_permanent_address_settlement',
                'client_permanent_address_municipality',
                'client_permanent_address_district',
                'client_permanent_address_location_name',
                'client_permanent_address_building_number',
                'client_permanent_address_building_entrance',
                'client_permanent_address_building_floor',
                'client_permanent_address_building_apartment',
                'client_permanent_address_full',
                'client_primary_phone',
                'client_email_address',
                'client_temporary_address_settlement',
                'client_temporary_address_municipality',
                'client_temporary_address_district',
                'client_temporary_address_location_name',
                'client_temporary_address_building_number',
                'client_temporary_address_building_entrance',
                'client_temporary_address_building_floor',
                'client_temporary_address_building_apartment',
                'client_temporary_address_building_full',
                'client_debit_card_number',
                'client_debit_card_issuing_bank',
                'client_discount',
                'client_credit_limit',
                'client_idcard_2sides',
            ], 'client'),
            'loan' => array_fill_keys([
                'application_id',
                'application_date',
                'application_date_2',
                'loan_amount',
                'loan_amount_in_writing',
                'loan_amount_in_writing_simple',
                'loan_interest_rate',
                'loan_interest_rate_in_writing',
                'loan_interest_rate_per_day',
                'loan_interest_rate_in_writing_per_day',
                'loan_gpr',
                'loan_gpr_in_writing',
                'total_due_principal_interest',
                'total_due_principal_interest_in_writing',
                'total_due_principal_interest_penalty',
                'total_due_principal_interest_penalty_in_writing',
                'total_due_interest',
                'total_due_interest_in_writing',
                'total_due_penalty',
                'loan_term',
                'loan_term_in_writing',
                'loan_overdue_interest_rate',
                'loan_payment_method',
                'loan_last_instalment_date',
                'loan_first_instalment_date',
                'loan_instalment_sum',
                'loan_instalment_list_principal_interest',
                'loan_penalty_sum',
                'loan_penalty_sum_in_writing',
                'loan_payment_period',
                'loan_instalment_count',
                'loan_instalment_count_in_writing',
                'loan_repayment_schedule',
                'loan_repayment_schedule_no_penalty',
                'loan_next_unpaid_installment_date',
                'loan_last_unpaid_installment_date',
                'loan_early_repayment_amount',
                'loan_company_office_address',
                'company_office_address_settlement',
                'due_amount' => 'due_amount',
                'early_repayment_date' => 'early_repayment_date',
                'early_repayment_amount' => 'early_repayment_amount',
                'credit_limit',
                'refinance_receive_amount',
                'refinance_due_amount',
                'loan_extend_amount',
            ], 'loan'),
            'guarant' => array_fill_keys([
                'guarant_1_phone',
                'guarant_1_email',
                'guarant_1_pin',
                'guarant_1_id_number',
                'guarant_1_first_name',
                'guarant_1_middle_name',
                'guarant_1_surname',
                'guarant_1_id_issue_date',
                'guarant_1_id_expiration_date',
                'guarant_1_id_issuer_name',
                'guarant_1_address_current',
                'guarant_1_address_permanent',

                'guarant_2_phone',
                'guarant_2_email',
                'guarant_2_pin',
                'guarant_2_id_number',
                'guarant_2_first_name',
                'guarant_2_middle_name',
                'guarant_2_surname',
                'guarant_2_id_issue_date',
                'guarant_2_id_expiration_date',
                'guarant_2_id_issuer_name',
                'guarant_2_address_current',
                'guarant_2_address_permanent',

                'guarant_1_footer_content', // smart var used in Docs\Domain\Template
                'guarant_2_footer_content', // smart var used in Docs\Domain\Template

            ], 'guarant'),
            'document' => array_fill_keys([
                'document_id',
                'document_creation_date',
            ], 'document'),
            'office' => array_fill_keys([
                'office_name',
                'office_phone',
                'office_address',
                'employee_name',
                'office_bank_details',
            ], 'office'),
            'company' => array_fill_keys([
                'company_pin',
                'company_name',
                'company_name_latin',
                'company_phone',
                'company_email',
                'company_web_page',
                'company_registered_address',
                'company_office_iban',
                'company_easypay_pin',
                'manager_signature_image',
            ], 'company'),
            'other' => array_fill_keys([
                'today_date',
                'today_date_2',
                'poll' => 'poll',
                'hash' => 'hash',
                self::BLOG_LINK->value,
                self::SIGN_DOCUMENTS_LINK->value,
                self::HOW_TO_PAY_LINK->value,
                self::FREQUENTLY_QUESTIONS_LINK->value,
                self::OFFICES_INFO_LINK->value,
            ], 'other'),
            'smart_vars' => array_fill_keys([
                'guarant_1_footer_content',
                'guarant_2_footer_content',
                'guarant_1_contract_content',
                'guarant_2_contract_content',
                'guarant_1_order_content',
                'guarant_2_order_content',
            ], 'smart_vars')
        ];
    }

    public function getResponsibleClass(): string
    {
        return match ($this) {
            /**************************CLIENT************************/
            self::CLIENT_FIRST_NAME,
            self::CLIENT_MIDDLE_NAME,
            self::CLIENT_SURNAME,
            self::CLIENT_PIN,
            self::CLIENT_ID_NUMBER,
            self::CLIENT_PRIMARY_PHONE,
            self::CLIENT_EMAIL_ADDRESS,
            self::CLIENT_ID_ISSUE_DATE,
            self::CLIENT_ID_EXPIRATION_DATE,
            self::CLIENT_ID_ISSUER_NAME,
            self::CLIENT_PERMANENT_ADDRESS_SETTLEMENT,
            self::CLIENT_PERMANENT_ADDRESS_MUNICIPALITY,
            self::CLIENT_PERMANENT_ADDRESS_DISTRICT,
            self::CLIENT_PERMANENT_ADDRESS_LOCATION_NAME,
            self::CLIENT_PERMANENT_ADDRESS_BUILDING_NUMBER,
            self::CLIENT_PERMANENT_ADDRESS_BUILDING_ENTRANCE,
            self::CLIENT_PERMANENT_ADDRESS_BUILDING_FLOOR,
            self::CLIENT_PERMANENT_ADDRESS_BUILDING_APARTMENT,
            self::CLIENT_PERMANENT_ADDRESS_FULL,
            self::CLIENT_TEMPORARY_ADDRESS_SETTLEMENT,
            self::CLIENT_TEMPORARY_ADDRESS_MUNICIPALITY,
            self::CLIENT_TEMPORARY_ADDRESS_DISTRICT,
            self::CLIENT_TEMPORARY_ADDRESS_LOCATION_NAME,
            self::CLIENT_TEMPORARY_ADDRESS_BUILDING_NUMBER,
            self::CLIENT_TEMPORARY_ADDRESS_BUILDING_ENTRANCE,
            self::CLIENT_TEMPORARY_ADDRESS_BUILDING_FLOOR,
            self::CLIENT_TEMPORARY_ADDRESS_BUILDING_APARTMENT,
            self::CLIENT_TEMPORARY_ADDRESS_FULL,
            self::CLIENT_DISCOUNT,
            self::CLIENT_CREDIT_LIMIT,
            self::CLIENT_NAME_PREFIX,
            self::CLIENT_IDCARD_2SIDES,
            self::PROFILE_LOGIN_CODE => Client::class,

            /***********************LOAN**************************/
            self::GUARANTOR_1_PHONE,
            self::GUARANTOR_1_EMAIL,
            self::GUARANTOR_1_PIN,
            self::GUARANTOR_1_ID_NUMBER,
            self::GUARANTOR_1_FIRST_NAME,
            self::GUARANTOR_1_MIDDLE_NAME,
            self::GUARANTOR_1_SURNAME,
            self::GUARANTOR_1_ID_ISSUE_DATE,
            self::GUARANTOR_1_ID_EXPIRATION_DATE,
            self::GUARANTOR_1_ID_ISSUER_NAME,
            self::GUARANTOR_1_ADDRESS_CURRENT,
            self::GUARANTOR_1_ADDRESS_PERMANENT,
            self::GUARANTOR_2_PHONE,
            self::GUARANTOR_2_EMAIL,
            self::GUARANTOR_2_PIN,
            self::GUARANTOR_2_ID_NUMBER,
            self::GUARANTOR_2_FIRST_NAME,
            self::GUARANTOR_2_MIDDLE_NAME,
            self::GUARANTOR_2_SURNAME,
            self::GUARANTOR_2_ID_ISSUE_DATE,
            self::GUARANTOR_2_ID_EXPIRATION_DATE,
            self::GUARANTOR_2_ID_ISSUER_NAME,
            self::GUARANTOR_2_ADDRESS_CURRENT,
            self::GUARANTOR_2_ADDRESS_PERMANENT,
            self::APPLICATION_ID,
            self::APPLICATION_DATE,
            self::APPLICATION_DATE_2,
            self::APPLICATION_TIME,
            self::LOAN_AMOUNT,
            self::LOAN_AMOUNT_IN_WRITING,
            self::LOAN_AMOUNT_IN_WRITING_SIMPLE,
            self::LOAN_INTEREST_RATE,
            self::LOAN_INTEREST_RATE_IN_WRITING,
            self::LOAN_INTEREST_RATE_PER_DAY,
            self::LOAN_INTEREST_RATE_IN_WRITING_PER_DAY,
            self::LOAN_OUTSTANDING_AMOUNT_TOTAL,
            self::LOAN_GPR,
            self::LOAN_GPR_IN_WRITING,
            self::TOTAL_DUE_PRINCIPAL_INTEREST,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_IN_WRITING,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY,
            self::TOTAL_DUE_PRINCIPAL_INTEREST_PENALTY_IN_WRITING,
            self::TOTAL_DUE_INTEREST,
            self::TOTAL_DUE_INTEREST_IN_WRITING,
            self::TOTAL_DUE_PENALTY,
            self::LOAN_PENALTY_SUM,
            self::LOAN_PENALTY_SUM_IN_WRITING,
            self::LOAN_TERM,
            self::LOAN_TERM_IN_WRITING,
            self::LOAN_OVERDUE_INTEREST_RATE,
            self::LOAN_PAYMENT_METHOD,
            self::LOAN_LAST_INSTALLMENT_DATE,
            self::LOAN_FIRST_INSTALLMENT_DATE,
            self::LOAN_INSTALLMENT_SUM,
            self::LOAN_INSTALLMENT_LIST_PRINCIPAL_INTEREST,
            self::LOAN_PAYMENT_PERIOD,
            self::LOAN_INSTALLMENT_COUNT,
            self::LOAN_INSTALLMENT_COUNT_IN_WRITING,
            self::LOAN_NEXT_UNPAID_INSTALLMENT_DATE,
            self::LOAN_LAST_UNPAID_INSTALLMENT_DATE,
            self::LOAN_EARLY_REPAYMENT_AMOUNT,
            self::LOAN_REPAYMENT_SCHEDULE,
            self::LOAN_REPAYMENT_SCHEDULE_NO_PENALTY,
            self::LOAN_HAS_GUARANT,
            self::LOAN_EPAY_MONEY_TOOKED_AT,
            self::ACTIVE_LOAN_TABLE,
            self::OFFICE_NAME,
            self::OFFICE_PHONE,
            self::OFFICE_ADDRESS,
            self::OFFICE_BANK_DETAILS,
            self::COMPANY_OFFICE_IBAN,
            self::TEXT_MESSAGE_HISTORY_LEGAL,
            self::IP_APPLICATION_SUBMIT,
            self::APPLICATION_SIGN_DATE,
            self::APPLICATION_SIGN_TIME,
            self::COMPANY_OFFICE_ADDRESS_SETTLEMENT,
                // self::EARLY_REPAYMENT_DATE,
                // self::EARLY_REPAYMENT_AMOUNT,
            self::DUE_AMOUNT,
            self::LOAN_EMAIL_HISTORY,
            self::LOAN_EMAIL_HISTORY_FOR_LEGAL,
            self::IP_APPLICATION_SIGN,
            self::CREDIT_LIMIT,
            self::REFINANCE_RECEIVE_AMOUNT,
            self::REFINANCE_DUE_AMOUNT,
            self::LOAN_EXTEND_AMOUNT,
            self::LOAN_EXTEND_AMOUNT_15_DAYS,
            self::SIGN_DOCUMENTS_LINK => Loan::class,

            /******************ENVIRONMENT***************************/
            self::HASH,
            self::TODAY_DATE,
            self::TODAY_DATE_2,
            self::COMPANY_PIN,
            self::COMPANY_NAME,
            self::COMPANY_NAME_LATIN,
            self::COMPANY_PHONE,
            self::COMPANY_EMAIL,
            self::COMPANY_WEB_PAGE,
            self::COMPANY_SMS_LOGIN_URL,
            self::COMPANY_REGISTERED_ADDRESS,
            self::COMPANY_EASY_PAY_PIN,
            self::EMPLOYEE_NAME,
            self::MANAGER_SIGNATURE_IMAGE,
                /// links
            self::BLOG_LINK,
            self::HOW_TO_PAY_LINK,
            self::FREQUENTLY_QUESTIONS_LINK,
            self::OFFICES_INFO_LINK => Environment::class,

            /*************************CUSTOM***************************/
            self::CD_ORDER_NUMBER,
            self::CD_TRANSAC_DATE,
            self::CD_REPRESENTOR_OR_FIRM,
            self::CD_ATTORNEY_NUMBER,
            self::CD_ATTORNEY_DATE,
            self::CD_BASIS,
            self::CD_PAYMENT_AMOUNT,
            self::CD_PAYMENT_AMOUNT_IN_WRITING,
            self::CD_PAID_PRINCIPLE,
            self::CD_PAID_INTEREST,
            self::CD_PAID_PENALTY,
            self::CD_PAID_LATE_INTEREST,
            self::CD_PAID_TAXES,

            self::DOCUMENT_ID,
            self::DOCUMENT_CREATION_DATE,
            self::CLIENT_DEBIT_CARD_NUMBER,
            self::CLIENT_DEBIT_CARD_ISSUING_BANK => Custom::class,

            /*************************MISSING***************************/
            // self::POLL,
            // self::DAYS,
            // self::DISCOUNT_AMOUNT => Environment::class, // TODO: here just use Environment, to return EMPTY STRING, so template could continue working with unfilled VARS. //throw new ResponsibleClassNotFound($this),

            /******************OFFICES***************************/
            self::OFFICE_WORKING_HOURS,
            self::COMPANY_OFFICE_ADDRESS,
            self::OFFICE_WORKING_HOURS_LAT,
            self::OFFICE_PHONE_NUM => Office::class, // TODO: use office ang get proper data for these keys
        };
    }
}
