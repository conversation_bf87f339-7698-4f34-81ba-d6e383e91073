@extends('layouts.app')
@section('style')
    <link rel="stylesheet" href="{{ asset('css/document-templates.css') }}">
@endsection

@section('content')
    <div class="row" style="padding-left: 15px;">
        <form method="POST"
              action="{{ !empty($documentTemplate) ? route('docs.documentTemplate.update', $documentTemplate->document_template_id) : route('docs.documentTemplate.store') }}"
              accept-charset="UTF-8" class="col-12">
            @csrf
            <div class="row">
                <div class="col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="form-group">
                                <label for="name" class="control-label required">{{ __('table.Name') }}</label>
                                <input class="form-control" required="required" minlength="2" maxlength="100"
                                       name="name" type="text"
                                       value="{{ old('name') ?? ($documentTemplate->name ?? '')}}"
                                       id="name">
                            </div>
                            <div class="form-group">
                                <label for="description"
                                       class="control-label required">{{ __('table.Description') }}</label>
                                <input class="form-control" required="required" minlength="2" maxlength="300"
                                       name="description" type="text"
                                       value="{{ old('description') ?? ($documentTemplate->description ?? '')}}"
                                       id="description">
                            </div>
                            <div class="form-group">
                                <label for="type"
                                       class="control-label">{{ __('docs::document.DocumentType') }}</label>
                                <select name="type" id="type" class="form-control w-100 mb-3">
                                    @foreach($docTempTypes as $type)
                                        <option
                                                @if(
                                                    !empty($type)
                                                    && !empty($documentTemplate->document_template_id)
                                                    && $type == $documentTemplate->type
                                                )
                                                    selected
                                                @endif
                                                value="{{ $type }}">
                                            {{ __('product::product.' . $type) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="type" class="control-label">{{ __('product::product.active') }}</label>
                                <select name="active" id="active" class="form-control w-100 mb-3">
                                    @if (isset($documentTemplate->active))
                                        <option
                                                value="1" {{ $documentTemplate->active == 1 ? 'selected' : '' }}>{{ __('product::product.Yes') }}</option>
                                        <option
                                                value="0" {{ $documentTemplate->active == 0 ? 'selected' : '' }}>{{ __('product::product.No') }}</option>
                                    @else
                                        <option value="1">{{ __('product::product.Yes') }}</option>
                                        <option value="0">{{ __('product::product.No') }}</option>
                                    @endif
                                </select>
                            </div>
                            @if(empty($documentTemplate))
                            <x-btn-update-simple/>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-9">
                    <div class="card">
                        <div class="card-body">
                            <div class="form-group">
                                <label for="content" class="control-label required">{{ __('table.Content') }}</label>
                                <textarea class="form-control" name="content" id="content">
                                    {{old('content') ?? ($documentTemplate->content ?? '')}}
                                </textarea>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </form>
        @if(!empty($documentTemplate))
            <div class="col-lg-3 mt-4" id="test-generate-doc-dev-env-only">
                <div class="card">
                    <div class="card-body">
                        <form method="POST"
                              action="{{route('docs.document.generateDocument')}}"
                              accept-charset="UTF-8" class="col-12" target="_blank">
                            @csrf
                            <input class="form-control" required="required" name="document_template_id" type="hidden" value="{{$documentTemplate->document_template_id}}">
                            <div class="form-group">
                                <label for="loanId" class="control-label required">{{ __('docs::document.LoanId') }}</label>
                                <input class="form-control" required="required" minlength="1" maxlength="30" name="loan_id" type="text" id="loanId">
                            </div>
                            <button type="submit" value="true" name="download" class="btn btn-success default-btn-last">{{ __('docs::document.Download') }}</button>
                        </form>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('scripts')
    <script src="{{ asset('dist/tinymce/tinymce.js') }}"></script>
    <script>
        let height = '280px';
        let templateId = '{{$documentTemplate->document_template_id ?? ''}}';
        if (templateId) {
            height = '700px';
        } else {
            height = '470px';
        }
        $('.live-search-city').selectpicker();
    </script>
    <x-docs::tiny-mce-variables :variables="$variables" :width="779"/>
@endpush
