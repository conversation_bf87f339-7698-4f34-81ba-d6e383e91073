<?php

namespace Modules\Docs\Domain\DataSource;

use Modules\Admin\Services\OfficeService;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Office as DbModel;
use Modules\Docs\Enums\PlaceholderEnum as PE;

class Office extends DomainModel implements DataSource
{
    private DbModel $dbModel;

    public function __construct()
    {
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function setDbModel(DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;

        return $this;
    }

    public function isSet(): bool
    {
        return isset($this->dbModel) ? $this->dbModel->exists : false;
    }

    public function getValueByPlaceholder(PE $placeholder): mixed
    {
        return match ($placeholder) {
            PE::OFFICE_WORKING_HOURS => OfficeService::getWorkingTimeForSms($this->dbModel),
            PE::OFFICE_WORKING_HOURS_LAT => OfficeService::getWorkingTimeForSms($this->dbModel),
            PE::OFFICE_PHONE_NUM => $this->dbModel->phone,
            PE::COMPANY_OFFICE_ADDRESS => $this->dbModel?->address ?? '',
        };
    }
}
