<?php

namespace Modules\Docs\Domain;

use Illuminate\Support\Arr;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\DocumentTemplate;
use Modules\Docs\Domain\DataSource\Client;
use Modules\Docs\Domain\DataSource\DataSource;
use Modules\Docs\Domain\DataSource\Environment;
use Modules\Docs\Domain\DataSource\Custom;
use Modules\Docs\Domain\DataSource\Loan;
use Modules\Docs\Domain\DataSource\Office;
use Modules\Docs\Domain\Exceptions\RequiredPlaceholderValueIsMissing;
use Modules\Docs\Enums\PlaceholderEnum as PE;
use Modules\Docs\Services\FormatService;

final class Template
{
    const SMART_VARS = [
        'guarant_1_footer_content',
        'guarant_2_footer_content',
        'guarant_1_contract_content',
        'guarant_2_contract_content',
        'guarant_1_order_content',
        'guarant_2_order_content',
    ];

    const GUARANT1_FOOTER_CONTENT = '
<table style="border: none !important;">
        <tr class="empty-row">
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td style="width: 55%; text-align: left; padding: 5px;">Поръчител 1:</td>
        </tr>
        <tr>
            <td></td>
            <td style="width: 55%; text-align: left; padding-left: 5px;">{guarant_1_first_name} {guarant_1_surname}</td>
        </tr>
</table>
    ';

    const GUARANT2_FOOTER_CONTENT = '
<table style="border: none !important;">
        <tr class="empty-row">
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td style="width: 55%; text-align: left; padding-left: 5px;">Поръчител 2:</td>
        </tr>
        <tr>
            <td></td>
            <td style="width: 55%; text-align: left; padding-left: 5px;">{guarant_2_first_name} {guarant_2_surname}</td>
        </tr>
</table>
    ';

    const GUARANT1_CONTRACT_CONTENT = '
        <div>
        И ПОРЪЧИТЕЛ 1: {guarant_1_first_name} {guarant_1_middle_name} {guarant_1_surname}, с ЕГН: {guarant_1_pin}, Л.К. No. {guarant_1_id_number}, издадена на
        {guarant_1_id_issue_date} г., от {guarant_1_id_issuer_name}, валидна до: {guarant_1_id_expiration_date}, с постоянен адрес: {guarant_1_address_current},
        настоящ адрес: {guarant_1_address_permanent}, домашен телефон: , служ. телефон: , моб. телефон: {guarant_1_phone}
        </div>
    ';

    const GUARANT2_CONTRACT_CONTENT = '
        <br/><div>
        И ПОРЪЧИТЕЛ 2: {guarant_2_first_name} {guarant_2_middle_name} {guarant_2_surname}, с ЕГН: {guarant_2_pin}, Л.К. No. {guarant_2_id_number}, издадена на
        {guarant_2_id_issue_date} г., от {guarant_2_id_issuer_name}, валидна до: {guarant_2_id_expiration_date}, с постоянен адрес: {guarant_2_address_current},
        настоящ адрес: {guarant_2_address_permanent}, домашен телефон: , служ. телефон: , моб. телефон: {guarant_2_phone}
        </div>
    ';

    const GUARANT1_ORDER_CONTENT = '
        <p>&nbsp;</p>
        <p>Долуподписаният/та {guarant_1_first_name} {guarant_1_middle_name} {guarant_1_surname}, с ЕГН: {guarant_1_pin},
        притежаващ л.к. № {guarant_1_id_number}, изд. на {guarant_1_id_issue_date}, от {guarant_1_id_issuer_name}, в качеството ми на ПОРЪЧИТЕЛ, 
        АВАЛИРАМ ТОЗИ ЗАПИС НА ЗАПОВЕД и гарантирам с подписа си неговото изплащане.</p>
        <p>&nbsp;</p>
        <p style="text-align: center;"><span ><strong>Поръчител 1:.........................................</strong></span></p>
        <p style="text-align: center;">/.................................................................................................................</span></p>
        <p style="text-align: center;">.............................../</span></p>
        <p style="text-align: center;">три имена</span></p>
    ';

    const GUARANT2_ORDER_CONTENT = '
        <p>&nbsp;</p>
        <p>Долуподписаният/та {guarant_2_first_name} {guarant_2_middle_name} {guarant_2_surname}, с ЕГН: {guarant_2_pin},
        притежаващ л.к. № {guarant_2_id_number}, изд. на {guarant_2_id_issue_date}, от {guarant_2_id_issuer_name}, в качеството ми на ПОРЪЧИТЕЛ,
        АВАЛИРАМ ТОЗИ ЗАПИС НА ЗАПОВЕД и гарантирам с подписа си неговото изплащане.</p>
        <p>&nbsp;</p>
        <p style="text-align: center;"><span ><strong>Поръчител 2:.........................................</strong></span></p>
        <p style="text-align: center;">/.................................................................................................................</span></p>
        <p style="text-align: center;">.............................../</span></p>
        <p style="text-align: center;">три имена</span></p>
    ';

    const TPL_TYPES_WITH_FOOTER_GUARANTS = [
        'guarant_1_contract_content' => self::GUARANT1_CONTRACT_CONTENT,
        'guarant_2_contract_content' => self::GUARANT2_CONTRACT_CONTENT,
        'guarant_1_footer_content' => self::GUARANT1_FOOTER_CONTENT,
        'guarant_2_footer_content' => self::GUARANT2_FOOTER_CONTENT,
        'guarant_1_order_content' => self::GUARANT1_ORDER_CONTENT,
        'guarant_2_order_content' => self::GUARANT2_ORDER_CONTENT,
    ];

    private TemplateInterface $dbModel;
    private array $placeholders = [];
    private array $placeholderValues = [];

    public function __construct(
        private readonly Client      $client,
        private readonly Custom      $custom,
        private readonly Loan        $loan,
        private readonly Office      $office,
        private readonly Environment $environment
    ) {}

    public function getFilledContentFromData(
        TemplateInterface $dbModel,
        ?DbClient         $dbClient = null,
        ?DbLoan           $dbLoan = null,
        array             $placeholderValues = [],
        string            $filler = '',
    ): string {

        return $this->buildFromExisting($dbModel,$dbLoan)
            ->setClient($dbClient)
            ->setLoan($dbLoan)
            ->setOffice($placeholderValues['office_id'] ?? null)
            ->setPredefinedValues($placeholderValues)
            ->getContent($filler);
    }

    public function getFilledTitle(): string
    {
        return $this->setPlaceholderValues()->replacePlaceholdersWithValues($this->dbModel->title);
    }

    public function buildFromExisting(TemplateInterface $dbModel, ?DbLoan $dbLoan = null): self
    {
        return $this->setDbModel($dbModel)
            ->setOffice()
            ->updateContentWithSmartVars($dbLoan)
            ->setPlaceholders();
    }

    public function buildForAllVars(): self
    {
        $vars = [];
        foreach (PE::cases() as $name => $value) {
            if (in_array($value->value, self::SMART_VARS)) {
                continue;
            }

            $vars[] = $value->value;
        }

        $dbModel = new DocumentTemplate();
        $dbModel->variables = json_encode($vars);
        $dbModel->content = '{' . implode('} , {', $vars) . '}';

        return $this
            ->setDbModel($dbModel)
            ->setOffice()
            ->setPlaceholders();
    }

    public function setClient(?DbClient $dbClient): self
    {
        if ($dbClient && !$this->client->isSet()) {
            $this->client->buildFromExisting($dbClient);
        }

        return $this;
    }

    public function setOffice(?int $officeId = null): self
    {
        if (!$officeId && ($this->loan->isSet() && $this->loan->dbModel()?->office_id)) {
            $officeId = $this->loan->dbModel()->office_id;
        }

        if (!$officeId) {
            return $this;
        }

        $office = \Modules\Common\Models\Office::whereOfficeId($officeId)->first();
        $this->office->setDbModel($office);

        return $this;
    }

    public function setLoan(?DbLoan $dbLoan): self
    {
        if (!$dbLoan) {
            return $this;
        }
        $this->loan->buildFromExisting($dbLoan);

        return $this->client->isSet() ? $this : $this->setClient($dbLoan->client);
    }

    public function setPredefinedValues(array $placeholderValues): self
    {
        foreach ($placeholderValues as $key => $val) {
            $placeholderEnum = PE::tryFrom(trim($key));
            if (!$placeholderEnum) {
                $this->placeholderValues[$key] = $val;
            } else {
                $this->placeholderValues[$placeholderEnum->value] = $val;
            }
        }

        return $this;
    }

    public function getContent(string $filler = ''): string
    {
        return $this
            ->setPlaceholderValues()
            ->replacePlaceholdersWithValues($this->dbModel->text(), $filler);
    }


    private function setDbModel(TemplateInterface $dbModel): self
    {
        $this->dbModel = $dbModel;

        return $this;
    }

    private function setPlaceholders(): self
    {
        foreach ($this->dbModel->variables() as $var) {
            $value = trim($var);
            $placeholderEnum = PE::tryFrom($value) ?: PE::UNDEFINED_PLACEHOLDER;
            $this->placeholders[$value] = $placeholderEnum;
        }

        return $this;
    }

    // Имаме променливи, които не са променливи, а направо цели секции в документа, които вече съдържат променливи
    // Ще ги наречем smart var, и в случая проверяваме по типа на темплейт дали може да има такава променлива или не.
    // ако НЕ, не правиме нищо, ако ДА - подменяме я в контента с необходима секция
    // и после подменяме и контента на самия темплейт
    //
    // Пример:
    // Има темплейти в които трябва да се подписват и гаранти, обаче ако няма гаранти, секция за тях изобщо не тр да фигурира в документа.
    private function updateContentWithSmartVars(?DbLoan $dbLoan = null)
    {
        $smartVars = null;
        $content = $this->dbModel->text();

        /// check for smart vars in content
        foreach (self::SMART_VARS as $smartVar) {
            if (str_contains($content, $smartVar)) {
                $smartVars[$smartVar] = self::TPL_TYPES_WITH_FOOTER_GUARANTS[$smartVar] ?? null;
            }
        }

        if (empty($smartVars)) {
            return $this;
        }


        if (!$this->loan->isSet() && $dbLoan) {
            $this->setLoan($dbLoan);
        }

        $loan = $this->loan;
        $tplVars = $this->dbModel->variables();
        $varPattern = '/\{(.*?)\}/';

        $guarant1 = $loan->guarantor()->dbModel()->first_name ?? '';
        $guarant2 = $loan->guarantor(true)->dbModel()->first_name ?? '';

        foreach ($smartVars as $smartVar => $replaceContent) {

            // първо изтриваме smart var от списъка на променливи в темплейта
            $key = array_search($smartVar, $tplVars);
            if ($key !== false) {
                unset($tplVars[$key]);
            }

            $replaceString = '';
            // подмяна на smart var в content
            if (
                !empty($guarant1)
                && preg_match('/(guarant_1)/', $smartVar)
            ) {
                $replaceString = $replaceContent;
            }
            if (
                !empty($guarant2)
                && preg_match('/(guarant_2)/', $smartVar)
            ) {
                $replaceString = $replaceContent;
            }

            // добавяме промнливи от smartVar в общия масив на темплейта
            if (!empty($replaceString)) {
                preg_match_all($varPattern, $replaceString, $matches);
                if (!empty($matches[1])) {
                    $tplVars = array_merge($tplVars, $matches[1]);
                }
            }

            $content = str_replace("{" . $smartVar . "}", $replaceString, $content);

        }

        // подменяме контента и променливите в темплейт
        $this->dbModel->content = $content;
        $this->dbModel->variables = $tplVars;

        return $this;
    }

    private function getResponsibleClass(PE $placeholder): DataSource
    {
        return match ($placeholder->getResponsibleClass()) {
            Client::class => $this->client,
            Custom::class => $this->custom,
            Loan::class => $this->loan,
            Office::class => $this->office,
            Environment::class => $this->environment
        };
    }

    private function setPlaceholderValues(): self
    {
        // check if all values already set (comes from customVars)
        $allSet = true;
        foreach ($this->placeholders as $key => $PEvalue) {
            if (!isset($this->placeholderValues[$key])) {
                $allSet = false;
                break;
            }
        }
        if ($allSet) {
            return $this;
        }

        foreach ($this->placeholders as $key => $placeholder) {
            if (PE::UNDEFINED_PLACEHOLDER === $placeholder) {
                // $this->placeholderValues[$key] = '{' . $key . '}';
                continue;
            }

            //this value already set
            if (isset($this->placeholderValues[$placeholder->value])) {
                continue;
            }

            $dataSource = $this->getResponsibleClass($placeholder);
            $value = $dataSource->isSet() ? $dataSource->getValueByPlaceholder($placeholder) : null;

            if ($placeholder->required() && is_null($value)) {
                throw new RequiredPlaceholderValueIsMissing($placeholder->value);
            }
            $this->placeholderValues[$placeholder->value] = !is_null($value) && $value !== ''
                ? FormatService::byType($value, $placeholder->type())
                : '';
        }

        return $this;
    }

    public function placeholderValues(string $filler = ''): array
    {
        // Second call after call in self::getFilledTitle and self::getContent. Why?
        $this->setPlaceholderValues();

        // For cases when the field is not filled specifically
        $fillerExceptions = [
            'client_middle_name'
        ];

        $pv = [];
        foreach ($this->placeholderValues as $key => $val) {
            if (in_array($key, $fillerExceptions, true)) {
                $pv[$key] = $val ?: '';
                continue;
            }
            $pv[$key] = $val ?: $filler;
        }

        return $pv;
    }

    /**     $variables = ['key1' => ['key2' => ['key3' => 'val']]];
     *      convert to
     *      ['{key1.key2.key3}' => 'val']
     */
    private function replacePlaceholdersWithValues(string $content, string $filler = ''): string
    {
        $data = collect(Arr::dot($this->placeholderValues($filler)))->mapWithKeys(static function ($item, $key) {
            return ['{' . $key . '}' => $item];
        });

        return strtr($content, $data->toArray());
    }
}
