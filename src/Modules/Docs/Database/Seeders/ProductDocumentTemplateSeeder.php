<?php

namespace Modules\Docs\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Product\Repository\ProductRepository;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Product;

class ProductDocumentTemplateSeeder extends Seeder
{
    public function __construct(
        private readonly ProductRepository $productRepository = new ProductRepository,
    ) {
    }

    public function run(): void
    {
        $products = Product::all();
        $documents = DocumentTemplate::whereIn('type', [
            'application',
            'sef',
            'personal_data_declaration',
            'general_terms',
            'contract',
            'repayment_plan',
            'order_record',
        ])->distinct('type')->get();
        $products->each(function (Product $product) use ($documents) {
            $documents->each(function (DocumentTemplate $documentTemplate) use ($product) {
                $this->productRepository->updateOrCreateDocumentTemplate(
                    $product,
                    $documentTemplate->type,
                    $documentTemplate->document_template_id
                );
            });
        });
    }
}