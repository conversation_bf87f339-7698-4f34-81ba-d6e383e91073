<?php

namespace Modules\Docs\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

// php artisan db:seed --class=\\Modules\\Docs\\Database\\Seeders\\DocumentTemplateSeeder
class DocumentTemplateSeeder extends Seeder
{
    public function run()
    {
        //DB::table('document_template')->truncate();
        // DB::table('product_document_template')->truncate();
        // DB::table('document')->truncate();

        $sql = file_get_contents(__DIR__ . '/all_doc_templates.sql');
        DB::unprepared($sql);

        $this->call(DocumentTemplateAutoIncrementSeeder::class);

        $this->command->call('document:fix-variables');
    }
}
