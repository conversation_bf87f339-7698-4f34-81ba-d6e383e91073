<?php

namespace Modules\Docs\Jobs;

use Modules\Common\Jobs\CommonJob;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan;
use Modules\Docs\Services\DocumentService;
use Modules\Sales\Domain\Events\NewLoanDocsGenerated;

class DocsPackGeneratingJob extends CommonJob
{
    protected Loan $loan;
    protected bool $notify;
    protected string $dockPack;

    protected $logChannel = 'docsError';
    protected $queueName = 'docs';

    public function __construct(
        Loan   $loan,
        string $dockPack,
        bool   $notify = true,
    ) {
        $this->loan = $loan;
        $this->notify = $notify;
        $this->dockPack = $dockPack;
    }

    public function handle(): void
    {
        try {
            $documentService = app(DocumentService::class);
            $res = $documentService->generatePdfDocumentsPackForLoan(
                $this->loan,
                $this->dockPack
            );

        } catch (\Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();
            $this->log($msg);
        }

        if ($this->notify) {
            try {
                if ($this->dockPack == DocumentTemplate::DOC_PACK_NEW_LOAN) {
                    NewLoanDocsGenerated::dispatch($this->loan);
                }
            } catch (\Throwable $e) {
                $msg = 'Error: ' . $e->getMessage()
                    . ', file: ' . $e->getFile()
                    . ', line: ' . $e->getLine();

                $this->log($msg);
            }
        }
    }
}
