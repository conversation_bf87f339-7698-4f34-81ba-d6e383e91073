<?php

namespace Modules\Common\Domain;

use Illuminate\Support\Carbon;

class CurrentDate
{
    public Carbon $now;
    public function __construct(?string $datetime = null){
        $this->fromString($datetime);
    }

    public function now(): Carbon
    {
        return clone($this->now);
    }

    public function nowString(): string
    {
        return (string)$this->now();
    }

    public function fromString(?string $datetime): self
    {
        $this->now = $datetime ? Carbon::parse($datetime) : now();
        return clone $this;
    }

    public function format(string $format): string
    {
        return $this->now()->format($format);
    }

    public function today(): Carbon
    {
        return $this->now()->startOfDay();
    }

    public function yesterday(): Carbon
    {
        return $this->now()->subDay()->startOfDay();
    }

    public function todayString(): string
    {
        return $this->today()->format('Y-m-d');
    }

    public function secondsSinceDate(Carbon $date): int
    {
        return $this->now()->diffInSeconds($date);
    }

    public function secondsAgo(int $seconds): Carbon
    {
        return $this->now()->subSeconds($seconds);
    }

    public function afterMinutes(int $minutes): Carbon
    {
        return $this->now()->addMinutes($minutes);
    }

    public function minutesTillEndOfTomorrow(): int
    {
        return $this->endOfTomorrow()->diffInMinutes($this->now());
    }

    public function minutesUntilDate(Carbon $date): int
    {
        return $date->diffInMinutes($this->now());
    }

    public function startOfTomorrow(): Carbon
    {
        return $this->now()->addDay()->startOfDay();
    }

    public function endOfTomorrow(): Carbon
    {
        return $this->now()->addDay()->endOfDay();
    }

    public function dateIsAfterTomorrow(Carbon $date): bool
    {
        return $date->gt($this->endOfTomorrow());
    }

    public function dateIsInThePast(Carbon $date): bool
    {
        return $this->now()->gt($date);
    }

    public function parse(string $date): Carbon
    {
        return Carbon::parse($date);
    }

    public function hourLater(): Carbon
    {
        return $this->now()->addHour();
    }

    public function hourAgo(): Carbon
    {
        return $this->now()->subHour();
    }

    public function firstSecondOfMonth(): Carbon
    {
        return $this->now()->startOfMonth();
    }

    public function secondsFromMonthStart(): int
    {
        return $this->secondsSinceDate($this->now()->startOfMonth());
    }

    public function isToday(Carbon $date): bool
    {
        return $date->gte($this->now()->startOfDay()) && $date->lt($this->startOfTomorrow());
    }

    public function startOfDayWeekAgo(): Carbon
    {
        return $this->now()->subDays(7)->startOfDay();
    }

    public function daysAgo(int $days): Carbon
    {
        return $this->now()->subDays($days);
    }

    public function isWeekBeforeToday(Carbon $date): bool
    {
        return $date->gte($this->startOfDayWeekAgo()) && $date->lt($this->startOfDayWeekAgo()->addDay());
    }

    public static function valid(string $date): bool
    {
        return preg_match('/([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})/', $date);
    }

    public function sqlDate(string $iDate = null, $format = null): string|Carbon
    {
        if (is_null($iDate)) {
            $iDate = date('Y-m-d H:i:s', strtotime($this->nowString()));
        }
        $dateTime = date('Y-m-d H:i:s', strtotime($iDate));

        if (!is_null($format)) {
            return Carbon::parse($dateTime)->format($format);
        }

        return Carbon::parse($dateTime);
    }

    public function modify(string $expression): CurrentDate
    {
        return new CurrentDate($this->now()->modify($expression));
    }

    public function addDays(int $days): CurrentDate
    {
        return new CurrentDate($this->now()->addDays($days));
    }

    public function nextYearStart(): Carbon
    {
        return $this->now->copy()->addYear()->startOfYear();
    }
}