<?php

namespace Modules\Common\Traits;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\ModelFilters\FilterBuilder;
use Modules\Common\ModelFilters\FilterWithListBuilder;
use Modules\Common\ModelFilters\FilterWithMapBuilder;

/**
 * @method getDefaultFilters()
 * @method getCustomFilters()
 * @method Builder|static filterBy(array $filters)
 * @method Builder|static filterByWithList(array $filters, array $list)
 * @method Builder|static filterByWithMap(array $filters, array $map)
 */
trait FilterModelTrait
{
    public function scopeFilterBy(Builder $query, array $filters): void
    {
        $className = class_basename(get_class($this));
        $namespace = $this->filtersNamespace;
        if (!$namespace) {
            $namespace = 'Modules\\Common\\ModelFilters\\' . $className;
        }
        if (empty($filters) && method_exists($this, 'getDefaultFilters')) {
            $filters = $this->getDefaultFilters();
        }

        if (method_exists($this, 'getCustomFilters')) {
            $customFilters = $this->getCustomFilters();
            if (!empty($filters)) {
                $filters = array_merge($customFilters, $filters);
            }
        }

        (new FilterBuilder($query, $filters, $namespace))->apply();
    }

    public function scopeFilterByWithList(Builder $query, array $filters, array $list): void
    {
        (new FilterWithListBuilder($query, $filters, $list))->apply();
    }

    public function scopeFilterByWithMap(Builder $query, array $filters, array $map): void
    {
        (new FilterWithMapBuilder($query, $filters, $map))->apply();
    }
}
