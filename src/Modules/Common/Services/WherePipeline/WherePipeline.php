<?php

namespace Modules\Common\Services\WherePipeline;

use Exception;
use Illuminate\Container\Container;
use Illuminate\Pipeline\Pipeline;
use Str;

class WherePipeline
{
    protected DataWrapper $dataWrapper;
    protected string $className;

    public function __construct(DataWrapper $dataWrapper, string $className)
    {
        $this->dataWrapper = $dataWrapper;
        $this->className = $className;

        return $this;
    }

    /**
     * @throws Exception
     */
    public function run()
    {
        $pipeline = new Pipeline(new Container());

        $this->setPrefix();

        return $pipeline
            ->send($this->dataWrapper)
            ->through((new PipelineResolver($this->className))->getPipes())
            ->thenReturn();
    }

    /**
     * @throws \ReflectionException
     */
    public function setPrefix()
    {
        if (empty($this->dataWrapper->getPrefix())) {
            $this->dataWrapper->setPrefix(Str::snake((new \ReflectionClass($this->className))->getShortName()));
        }
    }
}
