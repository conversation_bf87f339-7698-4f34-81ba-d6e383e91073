<?php

namespace Modules\Common\Services;

use Illuminate\Support\Facades\Cache;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Affiliate;
use Modules\Common\Models\AffiliateAttempt;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Product;
use Modules\Product\Repository\ProductRepository;

class AffiliateApplicationService extends BaseService
{
    public const DEFAULT_DAYS_INSTALLMENT_LOAN = 12;
    public const DEFAULT_DAYS_PAYDAY_LOAN = 30;

    public function registerAffiliateApplication(int $affiliateId, $requestData): ?AffiliateAttempt
    {
        return AffiliateAttempt::create([
            'affiliate_id' => $affiliateId,
            'request_json' => $requestData
        ])->fresh();
    }

    public function detectProductAndPeriod(array $data): array
    {
        $productRepo = app(ProductRepository::class);
        $products = Cache::remember('affiliate_products', now()->addHour(), function () use ($productRepo) {
            return $productRepo
                ->getByOffice(Office::OFFICE_ID_WEB, true)
                /// sort all payday products will be first
                /// after all installment products
                ->sortBy(function (Product $product) {
                    return !$product->isPayday();
                })
                ->keyBy('product_id');
        });

        $requestedAmount = (floatToInt($data['amount']) / 100);

        $out = [
            'product_id' => null,
            'period' => null,
            'payday' => null,
        ];
        /** @var Product $product ** */
        foreach ($products as $product) {
            $maxProductAmount = $productRepo->getProductsSettingsForAffiliates($product->product_id, ['max_amount']);
            $maxProductAmount = (floatToInt($maxProductAmount[0]->value) / 100);

            $period = self::DEFAULT_DAYS_INSTALLMENT_LOAN;
            if ($product->isPayday()) {
                $period = $productRepo->getProductsSettingsForAffiliates(
                    $product->product_id,
                    ['max_period']
                )[0]?->value ?? self::DEFAULT_DAYS_PAYDAY_LOAN;
            }

            if ($requestedAmount <= $maxProductAmount) {
                $out['product_id'] = $product->product_id;
                $out['period'] = $period;
                $out['payday'] = $product->isPayday();

                break;
            }
        }

        return $out;
    }

    public function getCachedAffiliates(): array
    {
        return Cache::remember('affiliates', now()->addHour(), function () {
            return Affiliate::where('active', 1)
                ->where('deleted', 0)
                ->get()
                ->keyBy('name')
                ->toArray();
        });
    }
}