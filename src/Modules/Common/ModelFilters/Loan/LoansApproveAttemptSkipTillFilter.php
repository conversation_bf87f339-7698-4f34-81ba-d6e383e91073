<?php

namespace Modules\Common\ModelFilters\Loan;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class LoansApproveAttemptSkipTillFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (!$filterValue) {
            return;
        }

        $date = Carbon::parse($filterValue)->format('Y-m-d H:i:s');

        $this->query->where(function ($query) use ($date) {
            $query->whereHas('approveAttempts', function (Builder $builder) use ($date) {
                $builder
                    ->where('last', 1)
                    ->where('skip_till', '<=', $date);
            })->orWhereDoesntHave('approveAttempts');
        });
    }
}
