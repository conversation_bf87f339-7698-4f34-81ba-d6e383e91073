<?php

namespace Modules\Common\ModelFilters\Loan;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class OuterCollectorFromDateFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $dates = getDatesForFilter($filterValue);
        $dateFrom = $dates['from'];
        $dateTo = $dates['to'];

        $this->query->whereBetween('outer_collector_from_date', [$dateFrom, $dateTo]);
    }
}