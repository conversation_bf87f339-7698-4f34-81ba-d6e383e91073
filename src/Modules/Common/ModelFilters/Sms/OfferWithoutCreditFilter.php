<?php

namespace Modules\Common\ModelFilters\Sms;

use Modules\Common\ModelFilters\ModelFilterAbstract;
use Modules\Communication\Models\SmsTemplate;

class OfferWithoutCreditFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $offerWithoutCreditIds = SmsTemplate::query()
            ->select(['sms_template_id', 'key'])
            ->where('key', 'LIKE', '%_days_first')
            ->pluck('sms_template_id')->toArray();

        if (intval($filterValue) === 1) {
            $this->query->whereIn('sms_template_id', $offerWithoutCreditIds);
        } else {
            $this->query->whereNotIn('sms_template_id', $offerWithoutCreditIds);
        }
    }
}