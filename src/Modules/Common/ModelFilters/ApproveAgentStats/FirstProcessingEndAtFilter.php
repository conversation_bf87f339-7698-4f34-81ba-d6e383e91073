<?php

namespace Modules\Common\ModelFilters\ApproveAgentStats;

use Illuminate\Support\Carbon;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class FirstProcessingEndAtFilter extends ModelFilterAbstract
{
    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);
        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);

        } else  if (preg_match("/([1-2][0-9]{3})/i", $filterValue)) {
            $dateFrom = new Carbon($filterValue);
            $dateTo = new Carbon($filterValue);
        }
        if (!empty($dateFrom) && !empty($dateTo)) {
            $this->query->whereBetween('first_processing_end_at', [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}