<?php

namespace Modules\Common\ModelFilters\ApproveAgentStats;

use Modules\Common\ModelFilters\ModelFilterAbstract;

final class FirstProcessingStartAtFilter extends ModelFilterAbstract
{
    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        $dates = getDatesForFilter($filterValue);
        $this->query->whereBetween('first_processing_start_at', [$dates['from'], $dates['to']]);
    }
}
