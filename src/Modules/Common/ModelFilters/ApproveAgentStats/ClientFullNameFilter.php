<?php

namespace Modules\Common\ModelFilters\ApproveAgentStats;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class ClientFullNameFilter extends ModelFilterAbstract
{
    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        $filterValue = str_replace(' ', '%', $filterValue);
        $this->query->where('client_full_name', 'LIKE', "%{$filterValue}%");
    }
}
