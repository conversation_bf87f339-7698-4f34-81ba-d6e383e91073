<?php

namespace Modules\Common\ModelFilters\Payment;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\ModelFilters\ModelFilterAbstract;

final class DateFromToFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);
        $dateFrom = new Carbon($dates[0]);
        if (count($dates) === 2) {
            $dateTo = new Carbon($dates[1]);
        } else {
            $dateTo = new Carbon($dates[0]);
        }

        if (isset($dateFrom, $dateTo)) {
            $this->query->whereBetween(DB::raw('COALESCE(payment.deleted_at, payment.created_at)'), [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}
