<?php

namespace Modules\Common\ModelFilters\Consultant;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class ConsultantOfficeIdFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (!is_array($filterValue)) {
            $filterValue = [$filterValue];
        }
        
        $this->query->whereHas('officesRelation', function (Builder $query) use ($filterValue) {
            $query->whereIn('consultant_office.office_id', $filterValue);
        });
    }
}
