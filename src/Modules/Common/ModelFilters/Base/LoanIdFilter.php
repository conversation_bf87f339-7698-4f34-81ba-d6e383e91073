<?php

namespace Modules\Common\ModelFilters\Base;

use Modules\Common\ModelFilters\ModelFilterAbstract;

class LoanIdFilter extends ModelFilterAbstract
{

    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        if (!is_array($filterValue)) {
            $filterValue = [$filterValue];
        }
        $this->query->whereIn('loan_id', $filterValue);
    }
}
