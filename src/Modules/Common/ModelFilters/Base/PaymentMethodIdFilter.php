<?php

namespace Modules\Common\ModelFilters\Base;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Schema;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class PaymentMethodIdFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $tableName = $this->query->getModel()->getTable();
        $filterColumn = 'payment_method_id';

        if (!is_array($filterValue)) {
            $filterValue = [$filterValue];
        }

        if (Schema::hasColumn($tableName, $filterColumn)) {
            $this->query->whereIn('payment_method_id', $filterValue);
        } else {
            $this->query->whereHas('paymentMethod', function (Builder $builder) use ($filterValue) {
                $builder->whereIn('payment_method_id', $filterValue);
            });
        }
    }
}
