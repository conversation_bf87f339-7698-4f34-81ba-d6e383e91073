<?php

namespace Modules\Common\ModelFilters\ClientWithoutActiveLoan;

use Modules\Common\ModelFilters\ModelFilterAbstract;

final class ClientNameFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $filterValue = str_replace(' ', "%", $filterValue);
        $this->query->whereRaw("CONCAT_WS(' ',first_name,middle_name,last_name) ILIKE '%{$filterValue}%'")
            ->orWhereRaw("CONCAT_WS(' ',first_name,last_name,middle_name) ILIKE '%{$filterValue}%'")
            ->orWhereRaw("CONCAT_WS(' ',first_name,last_name) ILIKE '%{$filterValue}%'")
            ->orWhereRaw("CONCAT_WS(' ',first_name,middle_name) ILIKE '%{$filterValue}%'")
            ->orWhere('first_name', 'ILIKE', "%{$filterValue}%")
            ->orWhere('middle_name', 'ILIKE', "%{$filterValue}%")
            ->orWhere('last_name', 'ILIKE', "%{$filterValue}%");
    }
}
