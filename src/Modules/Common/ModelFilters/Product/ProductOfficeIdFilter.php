<?php

namespace Modules\Common\ModelFilters\Product;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class ProductOfficeIdFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (!is_array($filterValue)) {
            $filterValue = [$filterValue];
        }

        $this->query->whereHas('offices', function (Builder $builder) use ($filterValue) {
            $builder->whereIn('office_product.office_id', $filterValue);
        });
    }
}
