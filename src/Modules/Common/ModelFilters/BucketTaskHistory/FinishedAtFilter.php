<?php

declare(strict_types=1);

namespace Modules\Common\ModelFilters\BucketTaskHistory;

use Modules\Common\ModelFilters\ModelFilterAbstract;

final class FinishedAtFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $dates = getDatesForFilter($filterValue);
        $dateFrom = $dates['from'];
        $dateTo = $dates['to'];

        if (!empty($dateFrom) && !empty($dateTo)) {
            $this->query->whereBetween('finished_at', [
                $dateFrom,
                $dateTo,
            ]);
        }
    }
}
