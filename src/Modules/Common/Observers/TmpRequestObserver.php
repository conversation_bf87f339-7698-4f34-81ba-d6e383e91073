<?php

declare(strict_types=1);

namespace Modules\Common\Observers;

use Modules\Api\Services\TmpRequestHistoryService;
use Modules\Common\Models\TmpRequest;

final readonly class TmpRequestObserver
{
    public function __construct(private TmpRequestHistoryService $historyService)
    {
    }

    public function deleting(TmpRequest $model): void
    {
        // There may be a model that is partially empty of data
        $this->historyService->insertFromArray($model->getAttributes());
    }
}
