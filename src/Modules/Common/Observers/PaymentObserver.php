<?php

declare(strict_types=1);

namespace Modules\Common\Observers;

use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Payment;

final class PaymentObserver
{
    public function updating(Payment $payment): void
    {
        if (
            $payment->status === PaymentStatusEnum::DELIVERED
            && $payment->isDirty('status')
        ) {
            $originalStatus = $payment->getOriginal('status');
            if (!$originalStatus) {
                $originalStatus = Payment::where('payment_id', $payment->getKey())->first('status')->status;
            }

            if ($originalStatus !== PaymentStatusEnum::EASY_PAY_SENT) {
                $payment->created_at = now();
            }
        }
    }
}
