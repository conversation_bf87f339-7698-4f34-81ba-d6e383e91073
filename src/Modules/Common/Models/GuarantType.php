<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperGuarantType
 */
class GuarantType extends BaseModel
{

    public const DEFAULT_GUARANT_TYPE_ID = 6;

    /**
     * @var string
     */
    protected $table = 'guarant_type';

    /**
     * @var string
     */
    protected $primaryKey = 'guarant_type_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'guarant_type_id',
        'active',
        'deleted',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    protected $fillable = [
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];
}
