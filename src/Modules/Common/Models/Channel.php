<?php

namespace Modules\Common\Models;

class Channel
{
    const PHONE_ID = 1;
    const WEB_ID = 2;
    const CPAY_ID = 3;
    const OFFICE_ID = 4;
    const MOBILE_ID = 5;
    const IFRAME_ID = 6;
    const IZBIRAM_BG_ID = 7;
    const IFRAME_CREATE_ID = 8;
    const CONSULT_QUICK_ID = 9;

    public static function getChannel(int $channelId): ?string
    {
        return match ($channelId) {
            self::PHONE_ID => 'phone',
            self::WEB_ID => 'web',
            self::CPAY_ID => 'cpay',
            self::OFFICE_ID => 'office',
            self::MOBILE_ID => 'mobile',
            self::IFRAME_ID => 'iframe',
            self::IFRAME_CREATE_ID => 'iframe_create',
            self::IZBIRAM_BG_ID => 'izbiram_bg_api_post',
            self::CONSULT_QUICK_ID => 'consultant-quick',
            default => null,
        };
    }

    public static function getChannelId(string $channelName): ?int
    {
        return match ($channelName) {
            'phone' => self::PHONE_ID,
            'web' => self::WEB_ID,
            'cpay' => self::CPAY_ID,
            'office' => self::OFFICE_ID,
            'mobile' => self::MOBILE_ID,
            'iframe' => self::IFRAME_ID,
            'iframe_create' => self::IFRAME_CREATE_ID,
            'izbiram_bg_api_post' => self::IZBIRAM_BG_ID,
            'consultant-quick' => self::CONSULT_QUICK_ID,
            default => null,
        };
    }
}
