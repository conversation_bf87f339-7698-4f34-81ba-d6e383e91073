<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperBranch
 */
class Branch extends BaseModel
{
    const BRANCH_ID_NOVI_PAZAR = 1;
    const BRANCH_ID_SOFIA = 2;
    const BRANCH_ID_VARNA = 3;
    const BRANCH_ID_SHUMEN = 4;
    const BRANCH_ID_CREDIT_HELP = 5;

    protected $table = 'branch';
    protected $primaryKey = 'branch_id';

    protected $fillable = [
        'name',
        'description',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function offices()
    {
        return $this->hasMany(
            Office::class,
            'branch_id',
            'branch_id',
        );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function bankAccount()
    {
        return $this->belongsToMany(
            BankAccount::class,
            'bank_clone',
            'bank_account_id',
            'branch_id'
        );
    }
}
