<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;
use Modules\Common\Enums\InviteUrlActionEnum;

/**
 * @property string $hash
 * @property InviteUrlActionEnum $action
 * @property Carbon $used_at
 * @property Carbon $expires_till
 * @property array $payload
 * @mixin IdeHelperInviteUrl
 */
class InviteUrl extends BaseModel
{
    protected $table = 'invite_urls';
    protected $primaryKey = 'invite_url_id';

    protected $fillable = [
        'hash', 'action', 'used_at', 'expires_till', 'payload', 'enabled_by', 'loan_id', 'deleted', 'active',
        'created_by', 'disabled_by', 'updated_by', 'enabled_at', 'client_id', 'disabled_at', 'deleted_by'
    ];
    protected $casts = [
        'action' => InviteUrlActionEnum::class,
        'used_at' => 'datetime:d-m-Y H:i:s',
        'expires_till' => 'datetime:d-m-Y H:i:s',
        'payload' => 'array',
    ];

    public function getClient(): ?Client
    {
        if (empty($this->client_id)) {
            return null;
        }

        return Client::where('client_id', $this->client_id)->first();
    }
}
