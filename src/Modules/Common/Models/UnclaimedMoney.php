<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Events\UnclaimedMoney\UnclaimedMoneyWasCreated;
use Modules\Common\Traits\FilterModelTrait;

/**
 * @property int $payment_id
 * @property int $amount
 * @property int|null $return_id
 * @property int|null $accounting_payment_id
 * @property Payment|null $payment
 * @property UnclaimedMoney|null $unclaimedMoney
 * @mixin IdeHelperUnclaimedMoney
 */
final class UnclaimedMoney extends Model
{
    use FilterModelTrait;

    protected $table = 'unclaimed_money';

    protected $fillable = [
        'payment_id',
        'direction',
        'amount',
        'details',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'return_id',
        'accounting_payment_id',
    ];

    protected $dispatchesEvents = [
        'created' => UnclaimedMoneyWasCreated::class
    ];

    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class, 'payment_id', 'payment_id');
    }

    public function unclaimedMoney(): BelongsTo
    {
        return $this->belongsTo(UnclaimedMoney::class, 'return_id', 'id');
    }

    public function getParent(): ?UnclaimedMoney
    {
        if ($this->direction != 'out') {
            return null;
        }

        if (empty($this->return_id)) {
            return null;
        }

        return UnclaimedMoney::find($this->return_id);
    }

    public function hasReturnId(): bool
    {
        return intval($this->return_id) > 0;
    }

    public function hasReturnRow(): bool
    {
        $row = UnclaimedMoney::where('return_id', $this->id)->first();
        return (!empty($row->id));
    }

    public function canBeReturned(): bool
    {
        return ($this->direction == 'in' && !$this->hasReturnRow());
    }
}
