<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $client_agreement_id
 * @property int $agreement_id
 * @property int|null $client_id
 * @property int|null $tmp_request_id
 * @property int|null $loan_id
 * @property int $value
 * @property-read Agreement $agreement
 * @property-read Client|null $client
 * @property-read Loan|null $loan
 * @property-read Administrator|null $processedBy
 * @property-read TmpRequest|null $tmpRequest
 * @mixin IdeHelperClientAgreement
 */
class ClientAgreement extends Model
{
    /**
     * @var string
     */
    protected $table = 'client_agreement';

    protected $primaryKey = 'client_agreement_id';

    public $timestamps = false;

    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'agreement_id',
        'tmp_request_id',
        'loan_id',
        'value',
        'created_at',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function agreement(): BelongsTo
    {
        return $this->belongsTo(
            Agreement::class,
            'agreement_id',
            'agreement_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function tmpRequest(): BelongsTo
    {
        return $this->belongsTo(
            TmpRequest::class,
            'tmp_request_id',
            'tmp_request_id'
        );
    }
}
