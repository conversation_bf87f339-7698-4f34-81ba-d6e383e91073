<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Modules\Common\Traits\FilterModelTrait;

/**
 * @property int $bucket_task_history_id
 * @property int $bucket_task_id
 * @property int $bucket_id
 * @property int $loan_id
 * @property int $client_id
 * @property int $product_id
 * @property string $amount
 * @property string|null $promised_amount
 * @property string|null $details
 * @property int $period
 * @property int $repaid_credits_count
 * @property string $client_full_name
 * @property string $pin
 * @property string $phone
 * @property string $email
 * @property int $overdue_amount
 * @property int $overdue_days_count
 * @property string|null $show_after
 * @property string|null $started_at
 * @property string|null $finished_at
 * @property int|null $processed_by
 * @property Carbon|null $created_at
 * @property string $status
 * @property int|null $collector_decision_id
 * @property int|null $parent_bucket_task_history_id
 * @property Payment[]|Collection $payments
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Loan $loan
 * @property-read Administrator|null $processedBy
 * @property-read Product $product
 * @property-read BucketTask $bucket_task
 * @property-read Administrator|null $updater
 * @mixin IdeHelperBucketTaskHistory
 */
final class BucketTaskHistory extends Model
{
    use FilterModelTrait;

    const UPDATED_AT = null;
    /**
     * @var string
     */
    protected $table = 'bucket_task_history';

    /**
     * @var string
     */
    protected $primaryKey = 'bucket_task_history_id';

    /**
     * @var array
     */
    protected $fillable = [
        'bucket_task_id',
        'bucket_id',
        'loan_id',
        'client_id',
        'product_id',
        'amount',
        'promised_amount',
        'promised_date',
        'details',
        'period',
        'repaid_credits_count',
        'client_full_name',
        'pin',
        'phone',
        'email',
        'overdue_amount',
        'overdue_days_count',
        'show_after',
        'started_at',
        'finished_at',
        'duration',
        'created_at',
        'created_by',
        'processed_by',
        'status',
        'collector_decision_id',
        'prev_decision_id',
        'prev_promised_date',
        'prev_promised_amount',
        'prev_agent_id',
        'parent_id',
    ];

    public function bucket_task(): BelongsTo
    {
        return $this->belongsTo(
            BucketTask::class,
            'bucket_task_id',
            'bucket_task_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(
            Product::class,
            'product_id',
            'product_id'
        );
    }

    public function payments(): HasMany
    {
        return $this->hasMany(
            Payment::class,
            'loan_id',
            'loan_id'
        );
    }

    public function collector_decision(): BelongsTo
    {
        return $this->belongsTo(
            CollectorDecision::class,
            'collector_decision_id',
            'collector_decision_id'
        );
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'processed_by', 'administrator_id');
    }
}
