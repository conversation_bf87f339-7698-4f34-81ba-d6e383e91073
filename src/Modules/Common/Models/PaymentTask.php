<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Enums\TaskStatusEnum;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Enums\PaymentProblemEnum;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $payment_task_id
 * @property int|null $payment_id
 * @property int|null $client_id
 * @property int|null $loan_id
 * @property string|null $iban
 * @property string|null $bic
 * @property int|null $payment_method_id
 * @property string|null $amount
 * @property string|null $basis
 * @property string|null $handled_at
 * @property int|null $handled_by
 * @property TaskStatusEnum $status
 * @property PaymentTaskNameEnum $name
 * @property string $direction
 * @property PaymentProblemEnum $type
 * @property int|null $success
 * @property string|null $details
 * @property string|null $start_at
 * @property string|null $end_at
 * @property int|null $duration
 * @property string $show_after
 * @property string|null $last_status_update_date
 * @property int|null $parent_task_id
 * @property int|null $imported_payment_id
 * @property int|null $easypay_rid
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Client|null $client
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read ImportedPayment|null $importedPayment
 * @property-read Loan|null $loan
 * @property-read PaymentTask|null $parent
 * @property-read Payment|null $payment
 * @property-read PaymentMethod|null $paymentMethod
 * @property-read CustomEloquentCollection<int, PaymentTaskStat> $paymentTaskStats
 * @property-read int|null $payment_task_stats_count
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperPaymentTask
 */
class PaymentTask extends AbstractTask
{
    public const DIRECTION_IN = 'in';
    public const DIRECTION_OUT = 'out';

    /**
     * @var string
     */
    protected $table = 'payment_task';

    /**
     * @var string
     */
    protected $primaryKey = 'payment_task_id';

    /**
     * @var string[]
     */
    public $casts = [
        'status' => TaskStatusEnum::class,
        'name' => PaymentTaskNameEnum::class,
        'type' => PaymentProblemEnum::class
    ];

    protected $fillable = [
        'payment_id', 'client_id', 'loan_id', 'iban', 'bic', 'payment_method_id', 'amount', 'basis', 'handled_at',
        'handled_by', 'status', 'name', 'direction', 'type', 'success', 'details', 'start_at', 'end_at', 'duration',
        'show_after', 'last_status_update_date', 'parent_task_id', 'imported_payment_id', 'easypay_rid', 'active',
        'deleted', 'created_by', 'updated_by', 'deleted_by', 'enabled_at', 'enabled_by', 'disabled_at', 'disabled_by'
    ];

    public function isTaskForRefund(): bool
    {
        return $this->name === PaymentTaskNameEnum::REQUEST_EASY_PAY_REFUND;
    }

    public function isTaskForRefundState(): bool
    {
        return $this->name === PaymentTaskNameEnum::WAIT_REFUND_STATE;
    }

    public function isTaskForManualSending(): bool
    {
        return (
            $this->name === PaymentTaskNameEnum::SEND_EASY_PAY
            || $this->name === PaymentTaskNameEnum::SEND_EASY_PAY_MANUALLY
        );
    }

    public function getProcessingRoute(): string
    {
        // in case of cash on handling we just redirect to client card and ask agent
        // to aquire money or cancel loan
        if (PaymentTaskNameEnum::GIVE_OUT_CASH == $this->name) {
            return route('head.loan-task.index', [
                'clientId' => $this->client_id,
                'loanId' => $this->loan_id,
                'task' => ClientCardRouteParamEnum::APPROVE->value
            ]);
        }

        return route('payment.paymentsTasks.processing', $this->getKey());
    }

    public static function getDirection(): array
    {
        return [
            self::DIRECTION_IN,
            self::DIRECTION_OUT,
        ];
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id',
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id',
        );
    }

    public function paymentTaskStats(): HasMany
    {
        return $this->hasMany(
            PaymentTaskStat::class,
            'payment_task_id',
            'payment_task_id',
        );
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(
            PaymentMethod::class,
            'payment_method_id',
            'payment_method_id'
        );
    }

    public function importedPayment(): HasOne
    {
        return $this->hasOne(
            ImportedPayment::class,
            'imported_payment_id',
            'imported_payment_id'
        );
    }

    public function taskAttempt(): BelongsTo
    {
        return $this
            ->belongsTo(
                PaymentTaskAttempt::class,
                'payment_task_id',
                'payment_task_id'
            )
            ->orderBy('created_at', 'DESC');
    }

    public function payment(): BelongsTo
    {
        return $this->belongsTo(
            Payment::class,
            'payment_id',
            'payment_id'
        );
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(
            PaymentTask::class,
            'parent_task_id',
            'payment_task_id'
        );
    }

    public function getEasypayRequest(): ?EasypayRequest
    {
        if (!empty($this->easypay_rid)) {
            return EasypayRequest::where(
                'easypay_request_id',
                $this->easypay_rid
            )->first();
        }

        return null;
    }

    public function getStatusLabel(string $status = 'status'): string
    {
        if ($this->status === TaskStatusEnum::PROCESSING) {
            $adminName = $this->handler->getFullNames();

            return __('collect::bucketTask.processedBy', ['name' => $adminName]);
        }


        $str = '';
        $lastAttempt = $this->getLastAttemptDecision(['administrator']);
        if (!empty($lastAttempt->payment_task_attempt_id)) {

            $admin = $lastAttempt->administrator;
            if (!empty($admin->administrator_id)) {
                $str = $admin->getName() . ', ';
            }

            $decisionStr = PaymentTaskDecision::getPaymentTaskDecision($lastAttempt->payment_task_decision_id);
            if ($decisionStr) {
                $str .= ' Изход: <b>' . __("payments::paymentTaskDecision." . $decisionStr) . '</b>';
            }
        }


        return trim($str);
    }

    public function timer(): string
    {
        return date_diff($this->created_at, $this->sqlDate())->format('%H:%I:%S');
    }

    public function isProblemOrdering(): bool
    {
        return $this->type->is(PaymentProblemEnum::TYPE_PROBLEM_ORDERING);
    }

    public function isStatusProcessing(): bool
    {
        return $this->status === TaskStatusEnum::PROCESSING;
    }

    public function isStatusDone(): bool
    {
        return $this->status === TaskStatusEnum::DONE;
    }

    public function isEasypay(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_EASYPAY;
    }

    public function isBank(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_BANK;
    }

    public function isCash(): bool
    {
        return $this->payment_method_id === PaymentMethod::PAYMENT_METHOD_CASH;
    }

    public function scopeOfMyOffices(Builder $query)
    {
        // $officeIds = getAdmin()->offices->pluck('office_id');
        $query->whereRaw("payment_id IN (
            SELECT pm.payment_id
            FROM payment pm
            WHERE
                pm.status != 'delivered'
                AND pm.office_id IN (" . implode(',', getAdminOfficeIds()) . ")
        )");
    }

    public function getLastAttemptDecisionLabel(): string
    {
        $attempt = $this->getLastAttemptDecision();
        if (empty($attempt->payment_task_decision_id)) {
            return '';
        }

        $decisionStr = PaymentTaskDecision::getPaymentTaskDecision($attempt->payment_task_decision_id);
        if (empty($decisionStr)) {
            return '';
        }

        return __("payments::paymentTaskDecision." . $decisionStr);
    }

    public function getLastAttemptDecision(array $with = []): ?PaymentTaskAttempt
    {
        $builder = PaymentTaskAttempt::where('payment_task_id', $this->payment_task_id);

        if (!empty($with)) {
            $builder->with($with);
        }

        return $builder->where('last', 1)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function getRefundSaleTask(): ?SaleTask
    {
        return SaleTask::where('client_id', $this->client_id)
            ->where('loan_id', $this->loan_id)
            ->where('parent_task_id', $this->payment_task_id)
            ->where('sale_task_type_id', SaleTaskType::SALE_TASK_TYPE_ID_UNRECEIVED_MONEY)
            ->orderBy('sale_task_id', 'DESC')
            ->first();
    }
}
