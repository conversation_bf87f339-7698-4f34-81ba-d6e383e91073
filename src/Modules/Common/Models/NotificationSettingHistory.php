<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperNotificationSettingHistory
 */
class NotificationSettingHistory extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'notification_setting_history';

    /**
     * @var string
     */
    protected $primaryKey = 'notification_setting_history_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'notification_setting_id',
        'client_id',
        'type',
        'channel',
        'value',
        'created_at',
        'created_by',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

}
