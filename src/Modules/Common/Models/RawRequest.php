<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;


/**
 * @property int $raw_request_id
 * @property string $type
 * @property string|null $url
 * @property string|null $ip
 * @property string|null $browser
 * @property string $request
 * @property string|null $response
 * @property bool $success
 * @property string|null $token
 * @property string|null $token2
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @mixin IdeHelperRawRequest
 */
class RawRequest extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'raw_request';

    /**
     * @var string
     */
    protected $primaryKey = 'raw_request_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'type',
        'url',
        'ip',
        'browser',
        'request',
        'response',
        'created_at',
        'success',
        'token',
        'token2',
        'country',
        'city',
        'latitude',
        'longitude',
        'coords_updated_at',
        'client_id',
    ];
}
