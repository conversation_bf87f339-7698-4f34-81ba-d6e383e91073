<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\ProductTypeEnum;
use Modules\Common\Traits\FilterModelTrait;


/**
 * \Modules\Common\Models\ApproveAgentStats
 *
 * @property int $approve_agent_stats_id
 * @property int $loan_id
 * @property int $client_id
 * @property string $pin
 * @property string $client_full_name
 * @property string $client_phone
 * @property string|null $client_email
 * @property int $office_id
 * @property bool $is_online
 * @property ProductTypeEnum|null $product_type
 * @property PaymentMethodEnum|null $payment_method
 * @property int $amount_requested
 * @property int $amount_approved
 * @property int|null $credit_limit
 * @property int|null $period_requested
 * @property int|null $period_approved
 * @property string|null $discount_percent
 * @property string $interest_rate
 * @property string $penalty_rate
 * @property Carbon|null $request_created_at
 * @property Carbon $loan_created_at
 * @property Carbon $loan_signed_at
 * @property int $first_processing_agent_id
 * @property string $first_processing_agent_name
 * @property Carbon $first_processing_start_at
 * @property Carbon $first_processing_end_at
 * @property int $first_decision_id
 * @property int|null $first_decision_reason_id
 * @property int|null $first_waiting_time
 * @property int|null $first_processing_time
 * @property string|null $referer
 * @property int $final_processing_agent_id
 * @property string $final_processing_agent_name
 * @property Carbon $final_processing_start_at
 * @property Carbon $final_processing_end_at
 * @property int $final_decision_id
 * @property int|null $final_decision_reason_id
 * @property int|null $final_waiting_time
 * @property int|null $final_processing_time
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read ApproveDecision|null $finalDecision
 * @property-read ApproveDecisionReason|null $finalDecisionReason
 * @property-read Administrator|null $finalProcessingAgent
 * @property-read ApproveDecision|null $firstDecision
 * @property-read ApproveDecisionReason|null $firstDecisionReason
 * @property-read Administrator|null $firstProcessingAgent
 * @property-read Loan|null $loan
 * @mixin IdeHelperApproveAgentStats
 */
class ApproveAgentStats extends Model
{
    use FilterModelTrait;
    /**
     * @var string
     */
    protected $table = 'approve_agent_stats';

    protected $casts = [
        'product_type' => ProductTypeEnum::class,
        'payment_method' => PaymentMethodEnum::class,
        'request_created_at' => 'datetime',
        'loan_created_at' => 'datetime',
        'loan_signed_at' => 'datetime',
        'first_processing_start_at' => 'datetime',
        'first_processing_end_at' => 'datetime',
        'final_processing_start_at' => 'datetime',
        'final_processing_end_at' => 'datetime'
    ];

    /**
     * @var string[]
     */
    protected $fillable = [
        'loan_id',
        'client_id',
        'pin',
        'client_full_name',
        'client_phone',
        'client_email',
        'office_id',
        'is_online',
        'product_type',
        'payment_method',
        'amount_requested',
        'amount_approved',
        'credit_limit',
        'period_requested',
        'period_approved',
        'discount_percent',
        'interest_rate',
        'penalty_rate',
        'request_created_at',
        'loan_created_at',
        'loan_signed_at',
        'first_processing_agent_id',
        'first_processing_agent_name',
        'first_processing_start_at',
        'first_processing_end_at',
        'first_decision_id',
        'first_decision_reason_id',
        'first_waiting_time',
        'first_processing_time',
        'referer',
        'final_processing_agent_id',
        'final_processing_agent_name',
        'final_processing_start_at',
        'final_processing_end_at',
        'final_decision_id',
        'final_decision_reason_id',
        'final_waiting_time',
        'final_processing_time'
    ];

    /**
     * @var string
     */
    protected $primaryKey = 'approve_agent_stats_id';

    public function firstDecision(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecision::class,
            'first_decision_id',
            'approve_decision_id'
        );
    }

    public function firstDecisionReason(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecisionReason::class,
            'first_decision_reason_id',
            'approve_decision_reason_id'
        );
    }

    public function finalDecision(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecision::class,
            'final_decision_id',
            'approve_decision_id'
        );
    }

    public function finalDecisionReason(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecisionReason::class,
            'final_decision_reason_id',
            'approve_decision_reason_id'
        );
    }

    public function firstProcessingAgent(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'first_processing_agent_id',
            'administrator_id'
        );
    }

    public function finalProcessingAgent(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'final_processing_agent_id',
            'administrator_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function getDecisionName(): string
    {
        return ApproveDecision::getDecisionById($this->approve_decision_id);
    }

    public function getDecisionReasonName(): string
    {
        return ApproveDecisionReason::getReasonById($this->approve_decision_reason_id);
    }
}
