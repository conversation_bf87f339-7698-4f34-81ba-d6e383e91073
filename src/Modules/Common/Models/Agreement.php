<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperAgreement
 */
class Agreement extends BaseModel
{
    const AGR_ID_PERSONAL_DATA = 1;

    const LIST = [
        'personal_data' => self::AGR_ID_PERSONAL_DATA,
    ];

    protected $table = 'agreement';
    protected $primaryKey = 'agreement_id';

    protected $fillable = [
        'name',
        'description',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function tmpRequests()
    {
        return $this->belongsToMany(
            TmpRequest::class,
            'client_agreement',
            'agreement_id',
            'tmp_request_id',
        )->withPivot(['loan_id', 'value']);
    }
}
