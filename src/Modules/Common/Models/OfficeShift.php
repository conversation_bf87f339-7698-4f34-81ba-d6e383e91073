<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\Common\Traits\FilterModelTrait;

/**
 * @property int $office_shift_id
 * @property int $office_id
 * @property string $shift_date
 * @property string|null $starts_at
 * @property string|null $ends_at
 * @property string $day_of_week
 * @property bool $is_holiday
 * @property string $comment
 * @property int|null $updated_by
 * @property Carbon|null $updated_at
 * @property-read Office $office
 * @mixin IdeHelperOfficeShift
 */
class OfficeShift extends Model
{
    use FilterModelTrait;
    /**
     * @var string
     */
    protected $table = 'office_shift';

    /**
     * @var string[]
     */
    protected $fillable = [
        'office_shift_id',
        'office_id',
        'date',
        'starts_at',
        'ends_at',
        'week_day',
        'is_holiday',
        'comment',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    /**
     * @var string
     */
    protected $primaryKey = 'office_shift_id';

    public static array $daysOfWeek =
        ['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];

    public function office(): BelongsTo
    {
        return $this->belongsTo(
            Office::class,
            'office_id',
            'office_id'
        );
    }
}
