<?php

namespace Modules\Common\Models;

abstract class AbstractDecision extends BaseModel
{
    abstract public function inSkipCounterCondition();

    public const TASK_DECISION_NO_ANSWER = 'the_call_is_unanswered';
    public const TASK_DECISION_BUSY = 'busy';
    public const TASK_DECISION_RECALL = 'call_later';
    public const TASK_DECISION_WRONG_PHONE = 'wrong_phone';
    public const TASK_DECISION_SAVE_PAYMENT = 'save_payment';
    public const TASK_DECISION_OTHER = 'something_else';
    public const TASK_DECISION_DECLINE_PAYMENT = 'decline_payment';
    public const TASK_DECISION_NOT_FROM_CLIENT = 'not_from_client';
    public const TASK_DECISION_UNIDENTIFIED = 'unidentified';

    public const TASK_DECISION_TASK_MODAL_CALL_LATER = 'call_later';
    public const TASK_DECISION_TASK_MODAL_OTHER = 'something_else';

    public const TASK_DECISION_TYPE_FINAL = 'final';
    public const TASK_DECISION_TYPE_WAITING = 'waiting';

}
