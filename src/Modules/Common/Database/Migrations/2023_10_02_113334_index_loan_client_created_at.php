<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\OfficeShift;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client', function (Blueprint $table) {
            $table->index('created_at');
        });
        Schema::table('loan', function (Blueprint $table) {
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client', function ($table) {
            $table->dropIndex(['client_created_at_index']);
        });
        Schema::table('loan', function ($table) {
            $table->dropIndex(['loan_created_at_index']);
        });
    }
};
