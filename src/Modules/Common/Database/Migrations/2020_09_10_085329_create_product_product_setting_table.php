<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'product_product_setting',
            function ($table) {
                $table->bigIncrements('product_product_setting_id');
                $table->bigInteger('product_id')->unsigned();
                $table->bigInteger('product_setting_id')->unsigned();
                $table->string('value');
                $table->foreign('product_id')->references('product_id')->on('product');
                $table->foreign('product_setting_id')->references('product_setting_id')->on('product_setting');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
         Schema::table(
            'product_product_setting',
            function (Blueprint $table) {
                $table->dropForeign('product_product_setting_product_id_foreign');
                $table->dropForeign('product_product_setting_product_setting_id_foreign');
            }
        );
        Schema::dropIfExists('product_product_setting');
    }
};
