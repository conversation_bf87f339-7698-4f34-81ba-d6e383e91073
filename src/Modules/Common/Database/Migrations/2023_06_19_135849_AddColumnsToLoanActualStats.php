<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_actual_stats', function (Blueprint $table) {

            $table->decimal('lifetime_principal_withdrawn', 11, 2)->default(0)->unsigned();
            $table->decimal('lifetime_principal_repaid', 11, 2)->default(0)->unsigned();
            $table->decimal('lifetime_income_repaid', 11, 2)->default(0)->unsigned();
            $table->date('max_overdue_date')->nullable();

            $table->integer('days_by_contract')->nullable();
            $table->integer('days_amended')->nullable();
            $table->integer('days_in_use')->default(0);
            $table->integer('loan_extension_count')->default(0);

            $table->date('real_repayment_date')->nullable();
            $table->integer('repayment_days_value')->nullable();
            $table->date('first_payment_received_at')->nullable();

            $table->string('creation_browser', 2048)->nullable();
            $table->string('creation_ip', 2048)->nullable();
            $table->jsonb('creation_location')->nullable();

            $table->string('sign_browser', 2048)->nullable();
            $table->string('sign_ip', 2048)->nullable();
            $table->jsonb('sign_location')->nullable();

            $table->string('a4e_expected_pd', 512)->nullable();
            $table->string('a4e_scorecard_version', 512)->nullable();
        });

        Schema::table('loan_stats_history', function (Blueprint $table) {

            $table->decimal('lifetime_principal_withdrawn', 11, 2)->default(0)->unsigned();
            $table->decimal('lifetime_principal_repaid', 11, 2)->default(0)->unsigned();
            $table->decimal('lifetime_income_repaid', 11, 2)->default(0)->unsigned();
            $table->date('max_overdue_date')->nullable();

            $table->integer('days_by_contract')->nullable();
            $table->integer('days_amended')->nullable();
            $table->integer('days_in_use')->default(0);
            $table->integer('loan_extension_count')->default(0);

            $table->date('real_repayment_date')->nullable();
            $table->integer('repayment_days_value')->nullable();
            $table->date('first_payment_received_at')->nullable();

            $table->string('creation_browser', 2048)->nullable();
            $table->string('creation_ip', 2048)->nullable();
            $table->jsonb('creation_location')->nullable();

            $table->string('sign_browser', 2048)->nullable();
            $table->string('sign_ip', 2048)->nullable();
            $table->jsonb('sign_location')->nullable();

            $table->string('a4e_expected_pd', 512)->nullable();
            $table->string('a4e_scorecard_version', 512)->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
