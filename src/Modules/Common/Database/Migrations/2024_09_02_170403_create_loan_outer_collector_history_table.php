<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('loan_outer_collector_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('loan_id');
            $table->foreignId('consultant_id')->nullable();
            $table->timestamp('outer_collector_from_date')->nullable();
            $table->foreignId('marked_as_outer_collector_by')->nullable();
            $table->timestamp('removed_at')->useCurrent();
            $table->foreignId('removed_by');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('loan_outer_collector_history');
    }
};
