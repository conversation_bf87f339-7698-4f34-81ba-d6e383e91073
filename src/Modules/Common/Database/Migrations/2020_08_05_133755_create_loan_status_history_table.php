<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'loan_status_history',
            function ($table) {
                $table->bigIncrements('loan_status_history_id');
                $table->integer('loan_id')->unsigned();
                $table->integer('loan_status_id')->unsigned();
                $table->timestamp('date');
                $table->integer('administrator_id')->unsigned();
                $table->foreign('loan_id')
                    ->references('loan_id')
                    ->on('loan');
                $table->foreign('loan_status_id')
                    ->references('loan_status_id')
                    ->on('loan_status');
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'loan_status_history',
            function (Blueprint $table) {
                $table->dropForeign('loan_status_history_loan_id_foreign');
                $table->dropForeign('loan_status_history_loan_status_id_foreign');
            }
        );
        Schema::dropIfExists('loan_status_history');
    }
};
