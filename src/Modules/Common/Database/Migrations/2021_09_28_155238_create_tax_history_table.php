<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'tax_history',
            function ($table) {
                $table->bigIncrements('tax_history_id');
                $table->integer('tax_id')->unsigned();
                $table->string('field');
                $table->string('value_from')->nullable();
                $table->string('value_to')->nullable();
                $table->integer('administrator_id')->unsigned();

                $table->foreign('tax_id')
                    ->references('tax_id')
                    ->on('tax');
                $table->foreign('administrator_id')
                    ->references('administrator_id')
                    ->on('administrator');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'tax_history',
            function (Blueprint $table) {
                $table->dropForeign('tax_history_tax_id_foreign');
                $table->dropForeign('tax_history_administrator_id_foreign');
            }
        );

        Schema::dropIfExists('tax_history');
    }
};
