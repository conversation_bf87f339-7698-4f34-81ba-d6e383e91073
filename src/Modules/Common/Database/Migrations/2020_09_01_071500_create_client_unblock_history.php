<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_unblock_history',
            function ($table) {
                $table->bigIncrements('client_unblock_history_id');
                $table->integer('client_id')->unsigned()->index();
                $table->string('comment')->nullable();
                $table->foreign('client_id')->references('client_id')->on('client');
                $table->tableCrudFields(true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'client_unblock_history',
            function (Blueprint $table) {
                $table->dropForeign('client_unblock_history_client_id_foreign');
            }
        );

        Schema::dropIfExists('client_unblock_history');
    }
};
