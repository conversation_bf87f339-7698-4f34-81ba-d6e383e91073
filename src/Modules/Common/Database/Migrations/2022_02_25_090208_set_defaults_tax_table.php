<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'tax',
            function ($table) {
                $table->decimal('rest_amount', 11, 2)->default(0)->change();
                $table->decimal('removed_amount', 11, 2)->default(0)->change();
                $table->decimal('paid_amount', 11, 2)->default(0)->change();
            });
    }
};
