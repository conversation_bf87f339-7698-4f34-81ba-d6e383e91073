<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->smallInteger('to_be_quit_ccr')->default(0);
            $table->timestamp('to_be_quit_ccr_at')->nullable();
            $table->integer('to_be_quit_ccr_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dropColumn(['to_be_quit_ccr', 'to_be_quit_ccr_at', 'to_be_quit_ccr_by']);
        });
    }
};
