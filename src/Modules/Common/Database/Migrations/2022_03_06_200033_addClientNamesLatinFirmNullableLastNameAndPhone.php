<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\Client;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client', function(Blueprint $table) {

            $table->string('latin_names')->nullable();

            $table->enum('gender', Client::getGenders())->nullable()->index();
            $table->enum('legal_status', Client::getLegalStatuses())->default(Client::LS_INDV)->index();
            $table->enum('citizenship_type', Client::getCitizenshipTypes())->default(Client::CT_LOCAL)->index();

            // ccr report properties
            $table->enum('legal_status_code', Client::getLegalStatusCodes())->nullable()->index();
            $table->enum('economy_sector_code', Client::getEconomySectorCodes())->nullable()->index();
            $table->enum('industry_code', Client::getIndustryCodes())->nullable()->index();

            // ccr report flags on client level: BORR
            $table->tinyInteger('registered_in_ccr')->default('0')->index();
            $table->tinyInteger('need_ccr_sync')->default('0')->index();
            $table->timestamp('registered_in_ccr_at', 0)->nullable();
            $table->timestamp('set_need_ccr_sync_at', 0)->nullable();
            $table->timestamp('unset_need_ccr_sync_at', 0)->nullable();
        });
    }
};
