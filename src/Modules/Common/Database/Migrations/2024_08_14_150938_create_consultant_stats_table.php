<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {

    public function up(): void
    {
        Schema::create('consultant_stats', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();
            $table->tinyInteger('active')->default('1')->index();
            $table->tinyInteger('deleted')->default('0');
            $table->foreignId('created_by')
                ->references('administrator_id')
                ->on('administrator');

            $table->dateTime('for_month')->index();
            $table->foreignIdFor(\Modules\Common\Models\Office::class, 'office_id')
                ->constrained('office', 'office_id');

            $table->foreignIdFor(\Modules\Common\Models\Consultant::class, 'consultant_id')
                ->constrained('consultant', 'consultant_id');

            $table->integer('rest_amount')->default(0);
            $table->integer('collected_amount')->default(0);
            $table->string('ratio')->default(0);
            $table->integer('new_clients')->default(0);
            $table->integer('installments_less_than_400')->default(0);
            $table->integer('installments_more_than_400')->default(0);
            $table->integer('repaid_loans_less_than_3_overdue_days')->default(0);
            $table->integer('repaid_loans_between_4_30_overdue_days')->default(0);
            $table->integer('repaid_loans_between_31_60_overdue_days')->default(0);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('consultant_stats');
    }
};
