<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())
            ->create(
                'penalty_term_history',
                function ($table) {
                    $table->bigIncrements('penalty_term_history_id');
                    $table->integer('penalty_term_id')->unsigned();
                    $table->integer('product_id')->unsigned();
                    $table->decimal('period_from', 11, 2);
                    $table->decimal('period_to', 11, 2)->nullable();
                    $table->decimal('amount_from', 11, 2);
                    $table->decimal('amount_to', 11, 2)->nullable();
                    $table->decimal('penalty_rate', 11, 2);
                    $table->dateTime('archived_at');
                    $table->integer('archived_by')->unsigned();


                    $table->tinyInteger('file_id');
                    $table->tinyInteger('gpr')->default(1);

                    $table->foreign('file_id')->references('file_id')->on('file');

                    $table->foreign('product_id')
                        ->references('product_id')
                        ->on('product');
                    $table->foreign('archived_by')
                        ->references('administrator_id')
                        ->on('administrator');

                    $table->tableCrudFields();
                }
            );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'penalty_term_history',
            function (Blueprint $table) {
                $table->dropForeign('penalty_term_history_product_id_foreign');
                $table->dropForeign('penalty_term_history_archived_by_foreign');
                $table->dropForeign('penalty_term_history_file_id_foreign');
            }
        );

        Schema::dropIfExists('penalty_term_history');
    }
};
