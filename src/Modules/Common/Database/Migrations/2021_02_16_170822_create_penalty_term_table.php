<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'penalty_term',
            function ($table) {
                $table->bigIncrements('penalty_term_id');
                $table->integer('product_id')->unsigned()->index();
                $table->decimal('period_from', 11, 2);
                $table->decimal('period_to', 11, 2)->nullable();
                $table->decimal('amount_from', 11, 2);
                $table->decimal('amount_to', 11, 2)->nullable();
                $table->decimal('penalty_rate', 11, 2);

                $table->tinyInteger('file_id');
                $table->tinyInteger('active')->default(1);
                $table->tinyInteger('gpr')->default(1);

                $table->foreign('product_id')->references('product_id')->on('product');
                $table->foreign('file_id')->references('file_id')->on('file');

                $table->tableCreateFields(false, true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'penalty_term',
            function (Blueprint $table) {
                $table->dropForeign('penalty_term_product_id_foreign');
                $table->dropForeign('penalty_term_file_id_foreign');
            }
        );

        Schema::dropIfExists('penalty_term');
    }
};
