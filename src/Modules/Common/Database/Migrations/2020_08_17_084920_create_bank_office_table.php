<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'bank_office',
            function ($table) {
                $table->integer('bank_account_id')->unsigned()->index();
                $table->foreign('bank_account_id')->references('bank_account_id')->on('bank_account');
                $table->integer('office_id')->unsigned()->index();
                $table->foreign('office_id')->references('office_id')->on('office');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'bank_office',
            function (Blueprint $table) {
                $table->dropForeign('bank_office_bank_account_id_foreign');
                $table->dropForeign('bank_office_office_id_foreign');
            }
        );

        Schema::dropIfExists('bank_office');
    }
};

