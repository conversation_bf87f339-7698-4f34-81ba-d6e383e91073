<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_stats_history',
            function ($table) {
                $table->bigIncrements('client_stats_history_id');
                $table->integer('client_actual_stats_id')->unsigned();
                $table->integer('client_id')->unsigned()->index();
                $table->date('date')->nullable();
                $table->integer('credit_limit');
                $table->integer('applications_count');
                $table->integer('approved_loans_count');
                $table->integer('disapproved_loans_count');
                $table->integer('repaid_loans_count');
                $table->integer('days_without_loan');
                $table->integer('current_overdue');
                $table->integer('max_overdue');

                $table->foreign('client_id')->references('client_id')->on('client');

                $table->tableCrudFieldsHistory(false);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'client_stats_history',
            function (Blueprint $table) {
                $table->dropForeign('client_stats_history_client_id_foreign');
            }
        );

        Schema::dropIfExists('client_stats_history');
    }
};
