<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_address',
            function ($table) {
                $table->bigIncrements('client_address_id');
                $table->integer('client_id')->unsigned()->index();
                $table->enum('type', ClientAddress::getTypes())->index();
                $table->integer('city_id')->unsigned()->nullable()->index();
                $table->string('post_code')->nullable();
                $table->string('address')->nullable();
                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('city_id')->references('city_id')->on('city');
                $table->tableCrudFields(true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'client_address',
            function (Blueprint $table) {
                $table->dropForeign('client_address_client_id_foreign');
                $table->dropForeign('client_address_city_id_foreign');
            }
        );

        Schema::dropIfExists('client_address');
    }
};
