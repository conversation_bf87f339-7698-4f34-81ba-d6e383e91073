<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'loan_meta',
            function ($table) {
                $table->bigIncrements('meta_id');
                $table->integer('loan_id')->unsigned();
                $table->string('key')->index();
                $table->text('value');
                $table->foreign('loan_id')
                    ->references('loan_id')
                    ->on('loan');
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'loan_meta',
            function (Blueprint $table) {
                $table->dropForeign('loan_meta_loan_id_foreign');
            }
        );
        Schema::dropIfExists('loan_meta');
    }
};
