<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'loan_email',
            function ($table) {
                $table->integer('loan_id')->unsigned();
                $table->integer('client_email_id')->unsigned();
                $table->foreign('loan_id')
                    ->references('loan_id')
                    ->on('loan');
                $table->tableCrudFields(true, true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'loan_email',
            function (Blueprint $table) {
                $table->dropForeign('loan_email_loan_id_foreign');
            }
        );
        Schema::dropIfExists('loan_email');
    }
};
