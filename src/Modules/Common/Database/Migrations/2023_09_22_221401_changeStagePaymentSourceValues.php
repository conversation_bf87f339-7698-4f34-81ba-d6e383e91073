<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        DB::statement("UPDATE payment SET source = 'file_import' WHERE source='bank';");
        DB::statement("UPDATE payment SET source = 'easy_pay_api' WHERE source='easypay';");
        DB::statement("UPDATE payment SET source = 'loan_refinance' WHERE source='refinance';");
        DB::statement("UPDATE payment SET source = 'cash_desk' WHERE source='cash';");
    }
};
