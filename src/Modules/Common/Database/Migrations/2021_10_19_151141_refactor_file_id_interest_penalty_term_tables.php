<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\InterestTerm;
use Modules\Common\Models\InterestTermHistory;
use Modules\Common\Models\PenaltyTerm;
use Modules\Common\Models\PenaltyTermHistory;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(
            InterestTerm::getTableName(),
            function (Blueprint $table) {
                $table->bigInteger('file_id')->nullable()->change();
                $table->dropForeign('interest_term_file_id_foreign');
                $table->foreign('file_id')->references('file_id')->on('file')->onDelete('set null');
            }
        );
        Schema::table(
            InterestTermHistory::getTableName(),
            function (Blueprint $table) {
                $table->bigInteger('file_id')->nullable()->change();
                $table->dropForeign('interest_term_history_file_id_foreign');
                $table->foreign('file_id')->references('file_id')->on('file')->onDelete('set null');
            }
        );

        Schema::table(
            PenaltyTerm::getTableName(),
            function (Blueprint $table) {
                $table->bigInteger('file_id')->nullable()->change();
                $table->dropForeign('penalty_term_file_id_foreign');
                $table->foreign('file_id')->references('file_id')->on('file')->onDelete('set null');
            }
        );
        Schema::table(
            PenaltyTermHistory::getTableName(),
            function (Blueprint $table) {
                $table->bigInteger('file_id')->nullable()->change();
                $table->dropForeign('penalty_term_history_file_id_foreign');
                $table->foreign('file_id')->references('file_id')->on('file')->onDelete('set null');
            }
        );
    }
};
