<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_alert_actual',
            function ($table) {
                $table->bigIncrements('client_alert_actual_id');
                $table->integer('client_id')->unsigned()->index();
                $table->integer('client_alert_type_id')->unsigned()->index();
                $table->string('details');
                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('client_alert_type_id')->references('client_alert_type_id')->on('client_alert_type');
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_alert_actual');
    }
};
