<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up()
    {
        Schema::create('migration_stats', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_id')->nullable();
            $table->unsignedBigInteger('loan_id')->nullable();
            $table->unsignedBigInteger('payment_id')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('client_id');
            $table->index('loan_id');
            $table->index('payment_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('migration_stats');
    }
};
