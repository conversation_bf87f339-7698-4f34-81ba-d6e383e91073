<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\ClientPicture;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE client_picture DROP CONSTRAINT IF EXISTS client_picture_type_check;');
        DB::statement(
            "
                ALTER TABLE
                    client_picture
                ADD CONSTRAINT client_picture_type_check
                    CHECK (((type)::text = ANY ((ARRAY[
                            'mvr'::character varying,
                            'id_card_1'::character varying,
                            'id_card_2'::character varying,
                            'passport'::character varying,
                            'viber'::character varying,
                            'whatsapp'::character varying,
                            'telegram'::character varying
                            ])::text[])))
            "
        );
    }
};
