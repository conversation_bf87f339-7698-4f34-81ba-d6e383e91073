<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\ClientPicture;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_picture',
            function ($table) {
                $table->bigIncrements('client_picture_id');

                $table->bigInteger('client_id')->unsigned()->index();
                $table->enum('type', ClientPicture::getTypes());
                $table->text('base64');
                $table->string('source');

                $table->foreign('client_id')->references('client_id')->on('client');

                $table->tableCrudFields(true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'client_picture',
            function (Blueprint $table) {
                $table->dropForeign('client_picture_client_id_foreign');
            }
        );

        Schema::dropIfExists('client_picture');
    }
};
