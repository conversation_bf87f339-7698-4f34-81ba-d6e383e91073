<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_visit_log', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('client_id');
            $table->date('date');
            $table->integer('logins_count')->nullable();
            $table->jsonb('logins_raw_requests')->nullable();
            $table->integer('pages_visited_count')->nullable();
            $table->jsonb('pages_visited_raw_requests')->nullable();
            $table->jsonb('ips')->nullable();
            $table->integer('timespent_total')->nullable();
            $table->integer('timespent_avg')->nullable();
            $table->timestamp('created_at')->default(\DB::raw('NOW()'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_visit_log');
    }
};
