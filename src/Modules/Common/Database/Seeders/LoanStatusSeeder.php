<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class LoanStatusSeeder extends Seeder
{
    public function run()
    {
        $loanStatus = [
            [
                'loan_status_id' => LoanStatus::NEW_STATUS_ID,
                'name' => LoanStatus::STATUS_NEW,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::PROCESSING_STATUS_ID,
                'name' => LoanStatus::STATUS_PROCESSING,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::APPROVED_STATUS_ID,
                'name' => LoanStatus::STATUS_APPROVED,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::SIGNED_STATUS_ID,
                'name' => LoanStatus::STATUS_SIGNED,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
                'name' => LoanStatus::STATUS_ACTIVE,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::REPAID_STATUS_ID,
                'name' => LoanStatus::STATUS_REPAID,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::CANCELLED_STATUS_ID,
                'name' => LoanStatus::STATUS_CANCELLED,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'loan_status_id' => LoanStatus::WRITTEN_OF_STATUS_ID,
                'name' => LoanStatus::STATUS_WRITTEN_OFF,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
        ];

        DB::table('loan_status')->insert($loanStatus);
    }
}
