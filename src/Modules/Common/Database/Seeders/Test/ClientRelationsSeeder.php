<?php

namespace Modules\Common\Database\Seeders\Test;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;

class ClientRelationsSeeder extends Seeder
{
    use WithoutModelEvents;

    public function run(): void
    {
        if(Client::find(ClientSeeder::CLIENT_ID)) {
            return;
        }
        $this->call([ClientSeeder::class]);
        DB::table('client_email')->insert([
            ['client_email_id'=>1,'client_id'=>1,'email'=>'<EMAIL>','last'=>1, 'active'=>1],
        ]);
        DB::table('client_address')->insert([
            ['client_address_id'=>1,'client_id'=>1,'type'=>'id_card', 'city_id'=>1, 'post_code'=>'1111', 'address'=>'alalal 27','last'=>1, 'active'=>1],
            ['client_address_id'=>2,'client_id'=>1,'type'=>'current', 'city_id'=>1, 'post_code'=>'1111', 'address'=>'alalal 28','last'=>1, 'active'=>1],
        ]);
        DB::table('client_name')->insert([
            ['client_name_id'=>1,'client_id'=>1,'first_name'=>'Калоян', 'middle_name'=>'Патлеев', 'last_name'=>'Илиев','last'=>1, 'active'=>1],
        ]);
        DB::table('client_bank_account')->insert([
            ['client_bank_account_id'=>1,'client_id'=>1, 'bank_id'=>1, 'iban'=>'**********************'],
        ]);
        DB::table('client_phone')->insert([
            ['client_phone_id'=>1,'client_id'=>1,'number'=>'**********', 'seq_num'=>1, 'last'=>1, 'active'=>1],
            ['client_phone_id'=>2,'client_id'=>1,'number'=>'**********', 'seq_num'=>2, 'last'=>1, 'active'=>1],
        ]);
        DB::table('client_employer')->insert([
            [
                'client_employer_id'=>1,
                'client_id'=>1,
                'name'=>'yagni ik',
                'bulstat'=>'1234567',
                'city_id'=>1,
                'address'=>'Satiksmes 5',
                'details'=>'coolest company ever',
                'position'=>'CEO',
                'salary'=>100500,
                'experience'=>120,
                'last'=>1,
                'active'=>1
            ],
        ]);
        DB::table('client_idcard')->insert([
            [
                'client_idcard_id'=>1,
                'client_id'=>1,
                'pin'=>'9104188750',
                'idcard_number'=>'9104188750',
                'idcard_issued_id'=>1,
                'issue_date'=>'2011-03-09',
                'valid_date'=>'2024-03-09',
                'city_id'=>1,
                'address'=>'Soedinenie 5',
                'last'=>1,
                'active'=>1
            ],
        ]);

        DB::table('client_picture')->insert([
            [
                'client_picture_id'=>1,
                'client_id'=>1,
                'type'=>'mvr',
                'base64'=> 'SomeLongBase64String',
                'source'=>'funky',
                'last'=>1,
                'active'=>1
            ],
        ]);

//        DB::table('client_bank_account')->insert([
//            ['client_bank_account_id'=>1,'client_id'=>1,'bank_id'=>1, 'bic'=>'BUINBGSF', 'iban'=>'**********************', 'last'=>1, 'active'=>1],
//        ]);

        DB::table('client_actual_stats')->insert([
            'client_actual_stats_id' => 1,
            'client_id' => 1,
            'date' => '2022-10-10 11:11:11',
            'credit_limit' => 0,
            'applications_count' => 0,
            'approved_loans_count' => 0,
            'disapproved_loans_count' => 0,
            'repaid_loans_count' => 0,
            'days_without_loan' => 0,
            'current_overdue_days' => 0,
            'max_overdue_days' => 0,
            'active' => 1,
            'credit_limit_updated_at' => '2022-10-10 11:11:11',
            'current_overdue_amount' => 0,
            'max_overdue_amount' => 0
        ]);

        DB::table('notification_setting')->insert([
            ['client_id'=>1, "type" => "marketing", "channel" => "call", "value" => 1],
            ['client_id'=>1, "type" => "marketing","channel" => "sms","value" => 1],
            ['client_id'=>1, "type" => "marketing", "channel" => "email", "value" => 1],
            ['client_id'=>1, "type" => "marketing", "channel" => "viber", "value" => 1],
            ['client_id'=>1, "type" => "collect", "channel" => "call", "value" => 1],
            ['client_id'=>1, "type" => "collect", "channel" => "sms", "value" => 1],
            ['client_id'=>1, "type" => "collect", "channel" => "email", "value" => 1],
            ['client_id'=>1, "type" => "collect", "channel" => "viber", "value" => 1],
            ['client_id'=>1, "type" => "collect", "channel" => "mail", "value" => 1]
        ]);
    }
}