<?php

namespace Modules\Common\Database\Seeders\Test;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Loan;
use Modules\Common\Models\ProductSetting;
use Modules\Head\Services\LoanService;

class Payday30LoanExistingInstallmentSeeder extends Seeder
{
    use WithoutModelEvents;

    public function run(): void
    {
        $this->call([
            Payday30LoanSeeder::class,
        ]);

        DB::table('installment')->insert([
            'installment_id' => 1,
            'client_id' => 1,
            'loan_id' => Payday30LoanSeeder::LOAN_ID,
            'seq_num' => 1,
            'due_date'=> '2022-11-09 00:00:00',
            'accrued_total_amount' => 1000.00,
            'total_amount' => 1016.60,
            'rest_principal' => 1000.00,
            'principal' => 1000.00,
            'paid_principal' => 0.00,
            'accrued_interest' => 0.00,
            'interest' => 60,
            'late_interest' => 0.00,
            'paid_accrued_interest' => 0.00,
            'paid_interest' => 0.00,
            'rest_interest' => 60,
            'paid_late_interest' => 0.00,
            'accrued_penalty' => 0.00,
            'penalty' => 90,
            'late_penalty' => 0.00,
            'paid_accrued_penalty' => 0.00,
            'paid_penalty' => 0.00,
            'rest_penalty' => 90,
            'paid_late_penalty' => 0.00,
            'overdue_days' => 0,
            'overdue_amount' => 0.00,
            'max_overdue_days' => 0,
            'max_overdue_amount' => 0.00,
            'status' => 'scheduled',
            'paid' => 0,
            'active' => 1,
            'created_at' => '2022-10-10 11:24:26'
        ]);

    }
}
