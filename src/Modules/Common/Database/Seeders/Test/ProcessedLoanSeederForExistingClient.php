<?php

namespace Modules\Common\Database\Seeders\Test;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Enums\ProductTypeEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Channel;
use Modules\Common\Models\Currency;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\ProductProductSetting;
use Modules\Common\Models\ProductSetting;

class ProcessedLoanSeederForExistingClient extends Seeder
{
    use WithoutModelEvents;

    const LOAN_ID = 4;
    const CURRENT_DATE = '2022-10-10 11:11:11';
    const PRODUCT_ID = 1;

    public function run(): void
    {
        $currentDate = new CurrentDate(self::CURRENT_DATE);

        DB::table('loan')->insert([
            'loan_id' => self::LOAN_ID,
            'client_id' => 1,
            'product_id' => 1,
            'product_type_id' => ProductTypeEnum::PAYDAY->id(),
            'loan_type_id' => LoanType::LOAN_TYPE_ID_NORMAL,
            'interest_percent' => 10,
            'penalty_percent' => 10,
            'installment_modifier' => '+7 days',
            'discount_percent' => 10,
            'amount_requested' => 1000,
            'amount_approved' => 1000,
            'installments_requested' => 30,
            'installments_approved' => 30,
            'currency_id' => Currency::BGN_CURRENCY_ID,
            'period_requested' => 30,
            'period_approved' => 30,
            'period_grace' => null,
            'loan_status_id' => LoanStatus::PROCESSING_STATUS_ID,
            'client_discount_actual_id' => null,
            'last_status_update_administrator_id' => 1,
            'administrator_id' => Administrator::DEFAULT_ADMINISTRATOR_ID,
            'last_status_update_date' => '2022-10-10 11:11:11',
            'payment_method_id' => PaymentMethod::PAYMENT_METHOD_BANK,
            'source' => LoanSourceEnum::CRM,
            'channel_id' => Channel::PHONE_ID,
            'office_id' => Office::OFFICE_ID_WEB,
            'active' => 0,
            'amount_rest' => 1000,
            'comment' => 'first default loan',
            'hash' => md5(time() + rand(0, 9999)),
            'created_at' => '2022-10-10 11:11:11',
        ]);
        DB::table('loan_email')->insert([
            ['client_email_id' => 1, 'loan_id' => self::LOAN_ID, 'last' => 1],
        ]);
        DB::table('loan_status_history')->insert([[
            'loan_id' => self::LOAN_ID,
            'loan_status_id' => 2,
            'date'=>'2022-10-10 11:11:11',
            'administrator_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID],
        ]);
        DB::table('loan_actual_stats')->insert([
            'loan_id' => self::LOAN_ID,
            'total_installments_count' => 1,
            'first_installment_date' => $currentDate->nowString(),
            'last_installment_date' => $currentDate->nowString(),
            'current_overdue_days' => 0,
            'current_overdue_amount' => 0
        ]);
        $productSettings = ProductProductSetting::where('product_id', self::PRODUCT_ID)->get();
        $currentProductSettings = $productSettings->pluck('value', 'name')->toArray();
        DB::table('loan_product_setting')->insert([
            'loan_id'=>self::LOAN_ID,
            'product_id'=>self::PRODUCT_ID,
            'period'=>$currentProductSettings[ProductSetting::PERIOD_KEY] ?? null,
            'settings'=>json_encode($currentProductSettings)
        ]);
    }
}
