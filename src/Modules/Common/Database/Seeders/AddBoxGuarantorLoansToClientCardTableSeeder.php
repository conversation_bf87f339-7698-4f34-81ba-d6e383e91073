<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\Common\Enums\ClientCardBoxGroupEnum;
use Modules\Common\Models\ClientCardBox;

//php artisan db:seed --class=Modules\\Common\\Database\\Seeders\\AddBoxGuarantorLoansToClientCardTableSeeder
class AddBoxGuarantorLoansToClientCardTableSeeder extends Seeder
{
    public function run(): void
    {
        $clientCardBox = ClientCardBox::create(['name' => 'boxGuarantorLoans']);

        $clientCardBox->clientCardBoxToGroup()->create([
            'group_name' => ClientCardBoxGroupEnum::TASK_FOR_APPROVE->value,
            'ord' => 1
        ]);

        $clientCardBox->clientCardBoxToGroup()->create([
            'group_name' => ClientCardBoxGroupEnum::TASK_FOR_SALE->value,
            'ord' => 1
        ]);

        $clientCardBox->clientCardBoxToGroup()->create([
            'group_name' => ClientCardBoxGroupEnum::TASK_FOR_COLLECT->value,
            'ord' => 1
        ]);

        $clientCardBox->clientCardBoxToGroup()->create([
            'group_name' => ClientCardBoxGroupEnum::CLIENT_WITH_LOAN->value,
            'ord' => 1
        ]);

        $clientCardBox->clientCardBoxToGroup()->create([
            'group_name' => ClientCardBoxGroupEnum::CLIENT_WITHOUT_LOAN->value,
            'ord' => 1
        ]);
    }
}
