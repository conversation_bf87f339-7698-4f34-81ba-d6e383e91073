<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;

class IdCardIssuedSeeder extends Seeder
{
    public function run()
    {
        $records = [

            [
                'idcard_issued_id' => 1,
                'name' => 'МВР',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 2,
                'name' => 'МВР - Благоевград',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ], [
                'idcard_issued_id' => 3,
                'name' => 'МВР - Бургас',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 4,
                'name' => 'МВР - Варна',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 5,
                'name' => 'МВР - Велико Търново',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 6,
                'name' => 'МВР - Видин',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 7,
                'name' => 'МВР - Враца',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 8,
                'name' => 'МВР - Габрово',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 9,
                'name' => 'МВР - Добрич',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 10,
                'name' => 'МВР - Кърджали',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 11,
                'name' => 'МВР - Кюстендил',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 12,
                'name' => 'МВР - Ловеч',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 13,
                'name' => 'МВР - Монтана',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 14,
                'name' => 'МВР - Пазарджик',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 15,
                'name' => 'МВР - Перник',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 16,
                'name' => 'МВР - Плевен',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 17,
                'name' => 'МВР - Пловдив',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 18,
                'name' => 'МВР - Разград',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 19,
                'name' => 'МВР - Русе',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 20,
                'name' => 'МВР - Силистра',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 21,
                'name' => 'МВР - Сливен',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 22,
                'name' => 'МВР - Смолян',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 23,
                'name' => 'МВР - Софийска',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 24,
                'name' => 'МВР - София',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 25,
                'name' => 'МВР - Стара Загора',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 26,
                'name' => 'МВР - Търговище',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 27,
                'name' => 'МВР - Хасково',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            [
                'idcard_issued_id' => 28,
                'name' => 'МВР - Шумен',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
            ['idcard_issued_id' => 29,
                'name' => 'МВР - Ямбол',
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                'created_at' => now(),
            ],
        ];

        DB::table('idcard_issued')->insert($records);
    }

}
