<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Enums\ThirdPartyReportServiceEnum;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeCheck;

class OfficeCheckFactory extends Factory
{
    protected $model = OfficeCheck::class;

    public function definition(): array
    {
        return [
            'office_id' => Office::query()->inRandomOrder()->firstOrFail()->getKey(),
            'service_key' => collect(ThirdPartyReportServiceEnum::cases())->random()->first()->value,
        ];
    }
}