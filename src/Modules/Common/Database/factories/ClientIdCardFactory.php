<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\Common\Models\City;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\IdCardIssued;

class ClientIdCardFactory extends Factory
{
    protected $model = ClientIdCard::class;

    public const RANDOM_IMAGE = '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';

    public const REAL_PIN_LIST = [
        '9101180967',
        '9103075929',
        '9103173930',
        '9104179281',
        '9104273317',
        '9105057591',
        '9105181220',
        '9105185648',
        '9107047231',
        '9107230730',
        '9108012899',
        '9108016662',
        '9108238464',
        '9108296305',
        '9109038199',
        '9109136810',
        '9110140777',
        '9111021200',
        '9111027169',
        '9111040941',
        '9111050364',
        '9111179686',
        '9111296384',
        '9112171680',
        '9112238377',
        '9101049941',
        '9102067699',
        '9102096845',
        '9102185670',
        '9102186147',
        '9103215960',
        '9104048110',
        '9104287157',
        '9104302689',
        '9105086394',
        '9105123414',
        '9105168152',
        '9106135657',
        '9106157403',
        '9106304248',
        '9107041681',
        '9107064080',
        '9107076060',
        '9108311694',
        '9110233669',
        '9110259215',
        '9110310580',
        '9111075300',
        '9112018270',
        '9112117396',
    ];

    public function definition(): array
    {
        return [
            'pin' => $this->faker->unique()->randomElement(self::REAL_PIN_LIST),
            'idcard_number' => $this->faker->numerify('########'),
            'idcard_issued_id' => IdCardIssued::query()->inRandomOrder()->first()?->getKey(),
            'issue_date' => $this->faker->dateTimeBetween('-10 years', 'now')->format('Y-m-d'),
            'valid_date' => $this->faker->dateTimeBetween('now', '+10 years')->format('Y-m-d'),
            'post_code' => 1000,
            'address' => str_replace('\n', '', $this->faker->address()),
            'sex' => $this->faker->randomElement(['male', 'female']),
            'last' => 1,
            'active' => 1,
            'deleted' => 0,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'city_id' => City::query()->inRandomOrder()->first()?->getKey(),
            'image' => self::RANDOM_IMAGE,
        ];
    }
}
