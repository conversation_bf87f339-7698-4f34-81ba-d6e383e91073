<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\City;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\IdCardIssued;

class GuarantFactory extends Factory
{
    protected $model = Guarant::class;

    public function definition(): array
    {
        return [
            'pin' => $this->faker->unique()->randomElement(ClientIdCardFactory::REAL_PIN_LIST),
            'first_name' => $this->faker->firstName,
            'middle_name' => $this->faker->lastName,
            'last_name' => $this->faker->lastName,
            'phone' => $this->faker->numerify('0999######'),
            'guarant_type_id' => 1,
            'idcard_issue_date' => $this->faker->date($format = 'Y-m-d', $max = 'now'),
            'idcard_valid_date' => $this->faker->date($format = 'Y-m-d', $max = 'now'),
            'idcard_issued_id' => rand(1, 25),
            'idcard_number' => $this->faker->numerify('########'),
            'address' => $this->faker->address,
            'currentAddress' => [
                'city_id' => $this->faker->numberBetween(1, 300),
                'address' => str_replace("\n", ' ', $this->faker->address),
                'post_code' => $this->faker->numberBetween(1111, 9999),
            ]
//            'pin' => $this->faker->word(),
//            'first_name' => $this->faker->firstName(),
//            'middle_name' => $this->faker->name(),
//            'last_name' => $this->faker->lastName(),
//            'phone' => $this->faker->phoneNumber(),
//            'email' => $this->faker->unique()->safeEmail(),
//            'idcard_number' => $this->faker->word(),
//            'idcard_issue_date' => $this->faker->word(),
//            'idcard_valid_date' => $this->faker->word(),
//            'address' => $this->faker->address(),
//            'active' => $this->faker->boolean(),
//            'deleted' => $this->faker->boolean(),
//            'created_at' => Carbon::now(),
//            'updated_at' => Carbon::now(),
//            'enabled_at' => Carbon::now(),
//            'enabled_by' => $this->faker->randomNumber(),
//            'disabled_at' => Carbon::now(),
//            'disabled_by' => $this->faker->randomNumber(),
//            'municipality' => $this->faker->word(),
//            'district' => $this->faker->word(),
//            'location' => $this->faker->word(),
//            'building_number' => $this->faker->word(),
//            'building_entrance' => $this->faker->word(),
//            'building_floor' => $this->faker->word(),
//            'building_apartment' => $this->faker->word(),
//
//            'city_id' => City::factory(),
//            'idcard_issued_id' => IdCardIssued::factory(),
//            'created_by' => Administrator::factory(),
//            'updated_by' => Administrator::factory(),
//            'deleted_by' => Administrator::factory(),
        ];
    }
}
