<?php

namespace Modules\Common\Forms;

use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Enums\ClientCardBoxGroupEnum;
use Modules\Common\Models\ClientCardBox;

class ClientCardBoxForm extends Form
{
    public function buildForm(): void
    {
        $this->add('name', 'text', [
            'label' => __('table.Name'),
            'attr' => [
                'readonly' => 'readonly'
            ]
        ]);

        $selectedGroups = [];
        $dbModel = $this->getModel();
        if ($dbModel instanceof ClientCardBox) {
            $selectedGroups = $dbModel->clientCardBoxToGroup->pluck('group_name', 'group_name');
        }

        $this->add('clientCardBoxToGroup', 'select', [
            'label' => __('table.ClientCardBoxToGroup'),
            'choices' => ClientCardBoxGroupEnum::selectOptions(),
            'selected' => $selectedGroups,
            'empty_value' => __('table.SelectOption'),
            'attr' => [
                'required' => 'required',
                'data-boostrap-selectpicker' => 'true',
                'multiple' => 'multiple'
            ]
        ]);
    }
}
