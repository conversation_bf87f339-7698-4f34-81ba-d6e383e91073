<?php

namespace Modules\Common\Statistics\Dashboard;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\StatsDaily;
use Modules\Common\Statistics\Dashboard\DTO\SaleTaskDTO;

class SaleTask
{
    const TTL_CACHE = 120; //sec

    public function getStatsByUser(Administrator $administrator, Carbon $date): SaleTaskDTO
    {
        // return Cache::remember(
        //     'stats_dashboard_sale_task' . $date->format('Y-m-d') . '_' . $administrator,
        //     self::TTL_CACHE,
        //     function () use ($administrator, $date) {
                $offices = $administrator->offices->pluck('office_id')->toArray();
                $day = $this->getByOfficeQuery($offices, [$date, $date])->first();
                $week = $this->getByOfficeQuery($offices, [(clone $date)->startOfWeek(), $date])->first();
                $month = $this->getByOfficeQuery($offices, [(clone $date)->startOfMonth(), $date])->first();

                return new SaleTaskDTO(
                    $day->total ?: 0,
                    $week->total ?: 0,
                    $month->total ?: 0,

                    $day->avg_time ?: 0,
                    $week->avg_time ?: 0,
                    $month->avg_time ?: 0,

                    $day->max_time ?: 0,
                    $week->max_time ?: 0,
                    $month->max_time ?: 0,

                    $day->min_time ?: 0,
                    $week->min_time ?: 0,
                    $month->min_time ?: 0,

                    now(),
                );
        //     }
        // );
    }

    private function getByOfficeQuery(array $offices, array $between): Builder
    {
        return $this->baseQuery($between)->whereIn('office_id', $offices);
    }

    private function baseQuery(array $between): Builder
    {
        return StatsDaily::query()->select([
            DB::raw('sum(processed_sale_tasks_count) as total'),
            DB::raw('sum(processing_sale_attempt_time_total)/sum(processed_sale_tasks_count) as avg_time_old'),
            DB::raw('avg(processing_sale_attempt_time_avg) as avg_time'), // since it could be for several offices, we take avg from averages
            DB::raw('min(processing_sale_attempt_time_min) as min_time'),
            DB::raw('max(processing_sale_attempt_time_max) as max_time'),
        ])->whereBetween('state_date', $between);
    }
}
