<?php

namespace Modules\Common\Http\Requests\Filters;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Modules\Common\Models\SaleTask;

class SaleTaskFilterRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'saleTaskId' => 'nullable|integer',
            'pin' => 'nullable|numeric',
            'saleTaskTypeId' => 'nullable|numeric|exists:sale_task_type,sale_task_type_id',
            'clientFullName' => 'nullable|string',
            'phone' => 'nullable|numeric',
            'loanTypeId' => 'nullable|numeric|exists:loan_type,loan_type_id',
            'amountRequestedFrom' => 'nullable|numeric',
            'amountRequestedTo' => 'nullable|numeric',
            'periodRequested' => 'nullable|integer',
            'status' => [
                'nullable',
                'string',
                Rule::in(SaleTask::AVAILABLE_STATUSES)
            ],
            'createdAt' => 'nullable|string',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function getFilters(): array
    {
        $filters = $this->validated();
        if (!isset($filters['status'])) {
            $filters['status'] = [SaleTask::SALE_TASK_STATUS_NEW, SaleTask::SALE_TASK_STATUS_PROCESSING];
        }
        $filters['showAfter'] = Carbon::now()->toDateTimeString();
        return $filters;
    }
}
