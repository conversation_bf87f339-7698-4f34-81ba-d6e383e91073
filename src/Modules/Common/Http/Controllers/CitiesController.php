<?php

namespace Modules\Common\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;
use Modules\Common\Models\City;
use Modules\Head\Http\Requests\CitySearchRequest;
use Modules\Head\Services\CityService;

class CitiesController extends BaseController
{
    public function __construct(
        private readonly CityService $cityService
    )
    {
        parent::__construct();
    }

    public function getCitiesByName(
        CitySearchRequest $request
    ): JsonResponse
    {
        $request = $request->validated();

        $searchString = '';
        if (isset($request['q']) && $request['q'] !== '') {
            $searchString = $request['q'];
        }

        if (isset($request['name']) && $request['name'] !== '') {
            $searchString = $request['name'];
        }

        $rows = $this->cityService->getCitiesByName($searchString);

        $cities = [];
        $rows->map(function (City $city) use (&$cities) {
            if (str_contains($city->name, 'с.')) {
                $municipality = $city?->municipality;
                $area = $municipality?->area;

                $cities[$city->getKey()] = "{$city->name}";
                if (!empty($municipality->name)) {
                    $cities[$city->getKey()] .= ", общ. {$municipality->name}";
                }
                if (!empty($area->name)) {
                    $cities[$city->getKey()] .= ", обл. {$area->name}";
                }
            } else {
                $cities[$city->getKey()] = $city->name;
            }
        });

        return response()->json($cities);
    }

    public function getCityById(City $city): JsonResponse
    {
        return Response::json($city);
    }

    public function getCityName(City $city): JsonResponse
    {
        return Response::json([
            $city->city_id => $city->name,
        ]);
    }
}
