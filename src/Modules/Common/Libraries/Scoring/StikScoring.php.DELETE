<?php

namespace Modules\Common\Libraries\Scoring;

// TODO: refactor and adopt

//use App\Modules\Credits\Models\Credit;
//use App\Modules\Credits\Models\CreditA4;
//use App\Modules\Credits\Models\CreditNoi;
//use App\Modules\Users\Models\UsersCcr;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;

use Modules\Common\Models\Loan;

/**
 * Used after A4E scoring is done
 *
 * Roman Slugin
 * 16:58
 *  Да, но оставь его пока, ето върваннъе куски из другой системъ, их оставил как пример, как бъло до етого
 */
class StikScoring
{
    /*
     * Recommendations
     */
    private const RECOMMENDATION_ACCEPT = 1;
    private const RECOMMENDATION_LOW_RISK = 2;
    private const RECOMMENDATION_MEDIUM_RISK = 3;
    private const RECOMMENDATION_HIGH_RISK = 4;
    private const RECOMMENDATION_REJECT = 5;

    /*
     * Zones
     */
    private const ZONE_ONE = 1;
    private const ZONE_TWO = 2;
    private const ZONE_THREE = 3;
    private const ZONE_FOUR = 4;

    /**
     * Колко кредита има усвоение
     */
    private $oldLoansCount;
    private $scoreResult;
    private $loan;

    /**
     * StikScoring constructor.
     * @param Credit $credit
     */
    public function __construct(Loan $loan)
    {
        $this->loan = $loan;
        $this->scoreResult = new ScoreResult($loan);
    }

    /**
     * Изчисляване на оценката
     * @throws Exception
     */
    public function score(): ScoreResult
    {
        $zoneIndex = $this->zone();

        $this->result->addDebugMessages('Zone index: ' . $zoneIndex);
        $this->result->addDebugMessages('Old credits count: ' . $this->getOldCreditsCount());
        $this->result->addDebugMessages('A4Score card suffix: ' . $this->credit->a4->last()->getScoreCardSuffix());

        /*
         * Set credit limits from matrix
         */
        $this->setLimitFromMatrix();

        if ($this->isNewClient()) {
            $this->result->addDebugMessages('New Client');
            /*
             *
             * new client
             */
            switch ($zoneIndex) {
                case self::ZONE_ONE:
                    $this->result->setAcceptAction();
                    break;
                case self::ZONE_TWO:

                    /*
                     * доход > 0
                     * и ост. просрочена главница по активни кредити (от ЦКР) <= 1000лв
                     * и ост. просрочена главница по активни кредити над 90 дни <= 50 лева
                     *  -  одобри, иначе откажи (ВНИМАНИЕ: < ПО МАЛКО)
                     */
                    if (
                        $this->getCcrOverduePrincipal() <= 1000 &&
                        $this->getCcrOverduePrincipalOver90Days() <= 50
                    ) {
                        $this->result->setAcceptAction();
                        break;
                    }

                    $this->result->setRejectAction();

                    break;
                default:
                    throw new Exception('Unknown zone index - new client (zone: ' . $zoneIndex . ')');
            }

            return $this->result;
        }

        /*
         * old client
         */
        $this->result->addDebugMessages('Old Client');
        switch ($zoneIndex) {
            case self::ZONE_ONE:
                $this->result->setAcceptAction();
                break;
            case self::ZONE_TWO:

                /*
                 * ако доход > 0
                 * и наш скор (ccr) >= 0
                 * и ост. просрочена главница по активни кредити (от ЦКР) <= 2000лв
                 * и ост. просрочена главница по активни кредити над 90 дни над 50 лева
                 * одобри, иначе откажи.
                 */
                if (
                    $this->hasIncome() &&
                    $this->getStikCcrScore() >= 0 &&
                    $this->getCcrOverduePrincipal() <= 2000 &&
                    $this->getCcrOverduePrincipalOver90Days() <= 50
                ) {
                    $this->result->setAcceptAction();
                    break;
                }

                $this->result->setRejectAction();

                break;
            default:
                throw new Exception('Unknown zone index - old client (zone: ' . $zoneIndex . ')');
        }

        return $this->result;
    }

    /**
     * get zone index
     * @return int
     * @throws Exception
     */
    private function zone(): int
    {
        $a4score = $this->getA4Score();

        $recommendationGB = $this->recommendationToInt($a4score->getRecommendation());
        $recommendationGBR = $this->recommendationToInt($a4score->getRecommendation('ScorecardGBR'));
        $this->result->addDebugMessages('ScorecardGB recommendation: ' . $a4score->getRecommendation());
        $this->result->addDebugMessages('ScorecardGBR recommendation: ' . $a4score->getRecommendation('ScorecardGBR'));

        if ($this->isNewClient()) {
            /*
             * NEW CLIENT
             */
            if (
                $recommendationGB <= self::RECOMMENDATION_LOW_RISK ||
                $recommendationGB == self::RECOMMENDATION_MEDIUM_RISK && $recommendationGBR <= self::RECOMMENDATION_LOW_RISK
            ) {
                return self::ZONE_ONE;
            } elseif (
                $recommendationGB <= self::RECOMMENDATION_LOW_RISK && $recommendationGBR >= self::RECOMMENDATION_HIGH_RISK ||
                $recommendationGB == self::RECOMMENDATION_MEDIUM_RISK && $recommendationGBR >= self::RECOMMENDATION_MEDIUM_RISK ||
                $recommendationGB >= self::RECOMMENDATION_HIGH_RISK
            ) {
                return self::ZONE_TWO;
            }

            throw new Exception(
                'Unknown zone - new client (GB: ' . $recommendationGB . ', GBR:' . $recommendationGBR . ')'
            );
        }

        /*
         * OLD CLIENT
         */
        if (
            $recommendationGB <= self::RECOMMENDATION_LOW_RISK ||
            $recommendationGB == self::RECOMMENDATION_MEDIUM_RISK && $recommendationGBR <= self::RECOMMENDATION_MEDIUM_RISK
        ) {
            return self::ZONE_ONE;
        } elseif (
            $recommendationGB == self::RECOMMENDATION_MEDIUM_RISK && $recommendationGBR >= self::RECOMMENDATION_HIGH_RISK ||
            $recommendationGB >= self::RECOMMENDATION_HIGH_RISK
        ) {
            return self::ZONE_TWO;
        }

        throw new Exception(
            'Unknown zone - old client (GB: ' . $recommendationGB . ', GBR:' . $recommendationGBR . ')'
        );
    }

    /**
     * @return CreditA4
     * @throws Exception
     */
    public function getA4Score(): CreditA4
    {
        /** @var CreditA4 $a4score */
        $a4score = $this->credit->a4->last();
        if (!$a4score) {
            throw new Exception('Score missing!');
        }
        return $a4score;
    }

    /**
     * @param string $rec
     * @return int
     * @throws Exception
     */
    private function recommendationToInt(string $rec): int
    {
        switch ($rec) {
            case 'Accept':
                return self::RECOMMENDATION_ACCEPT;
                break;
            case 'LowRisk':
                return self::RECOMMENDATION_LOW_RISK;
                break;
            case 'MidRisk':
                return self::RECOMMENDATION_MEDIUM_RISK;
                break;
            case 'HighRisk':
                return self::RECOMMENDATION_HIGH_RISK;
                break;
            case 'Reject':
                return self::RECOMMENDATION_REJECT;
                break;
        }
        throw new Exception('Unknown recommendation (' . $rec . ')');
    }

    /**
     * Нов клиент ли е?
     * @return bool
     */
    private function isNewClient(): bool
    {
        return $this->getOldCreditsCount() > 0 ? false : true;
    }

    /**
     * Брой стари кредити
     * @return int
     */
    private function getOldCreditsCount(): int
    {
        if (!is_null($this->oldCreditsCount)) {
            return $this->oldCreditsCount;
        }

        $oldCredits = $this->getOldCreditsQuery()
            ->count();

        $this->oldCreditsCount = $oldCredits;

        return $this->oldCreditsCount;
    }

    private function getOldCreditsQuery(): Builder
    {
        return Credit::where('egn', $this->credit->egn)
            ->whereNotIn('status', [
                'cancel',
                'cancel_client',
                'processing'
            ])
            ->orderBy('id', 'ASC')
            ->where('id', '!=', $this->credit->id);
    }

    /**
     * Credit limit
     * @return void
     * @throws Exception
     */
    private function setLimitFromMatrix(): void
    {
        if ($this->isNewClient()) {
            $matrix = [
                [800, 800, 600, 400, 400],
                [800, 600, 400, 200, 200],
                [400, 400, 200, 200, 200],
                [150, 150, 150, 150, 150],
                [150, 150, 150, 150, 100],
            ];
        } else {
            $matrix = [
                [
                    [1500, 2000, 2500, 3000],
                    [1500, 2000, 2500, 3000],
                    [1500, 2000, 2500, 3000],
                    [1000, 1500, 2000],
                    [1000, 1500, 2000]
                ],
                [[1500, 2000, 2500, 3000], [1000, 1250, 1500], [1000, 1250, 1500], [600, 800, 1000], [600, 800, 1000]],
                [[1000, 1500, 2000], [1000, 1250, 1500], [600, 800, 1000], [400, 600, 800], [200, 400, 600]],
                [[200, 400, 600], [200, 400, 600], [200, 400, 600], [200, 400, 600], [200, 400, 600]],
                [[200, 400, 600], [200, 400, 600], [200, 400, 600], [200, 400, 600], [200, 400, 600]],
            ];
        }

        $a4score = $this->getA4Score();
        $recommendationGB = $this->recommendationToInt($a4score->getRecommendation());
        $recommendationGBR = $this->recommendationToInt($a4score->getRecommendation('ScorecardGBR'));

        $this->result->addDebugMessages('Matrix indexes: ' . $recommendationGB . ' X ' . $recommendationGBR);

        $matrixXIndex = $recommendationGB - 1;
        $matrixYIndex = $recommendationGBR - 1;

        if (empty($matrix[$matrixXIndex][$matrixYIndex])) {
            throw new Exception('Matrix indexes error!');
        }
        $matrixValue = $matrix[$matrixXIndex][$matrixYIndex];

        $this->result->addDebugMessages('Matrix value: ' . print_r($matrixValue, true));

        if (!is_array($matrixValue)) {
            $this->result->setLimit($matrixValue);
            return;
        }

        /*
         * Credit limit based on old credits count in time
         */
        $oldCredits = $this->getOldCreditsQuery()->select(['id', 'created_at', 'nefin_credit_info'])->get();

        $this->result->addDebugMessages('Old credits count for matrix: ' . $oldCredits->count());

        $firstCredit = $oldCredits->first();

        $grouped = $oldCredits->keyBy(function (Credit $credit) use ($firstCredit) {
            return floor(Carbon::parse($firstCredit->created_at)->diffInDays(Carbon::parse($credit->created_at)) / 60);
        });

        $this->result->addDebugMessages('Old credits group count for matrix: ' . $grouped->count());

        if ($grouped->count() > count($matrixValue)) {
            $limit = array_last($matrixValue);
            $this->result->setLimit($limit);
        } else {
            $index = $grouped->count() - 1;
            if ($index < 0) {
                $index = 0;
            }
            $limit = $matrixValue[$index];
            $this->result->setLimit($limit);
        }
    }

    /**
     * дали има някакъв доход?
     * @return bool
     */
    private function hasIncome(): bool
    {
        /** @var CreditNoi $noi2 */
        $noi2 = $this->credit->noi->where('type', 'noi2')->last();
        if ($noi2) {
            $noi2Array = $noi2->getData();
            if (isset($noi2Array['PersonalInfo']) && is_array($noi2Array['PersonalInfo']) && count(
                    $noi2Array['PersonalInfo']
                ) > 0) {
                $this->result->addDebugMessages('noi2 hasIncome: true');
                return true;
            }
        }

        /** @var CreditNoi $noi51 */
        $noi51 = $this->credit->noi->where('type', 'noi51')->last();
        if ($noi51) {
            $noi51Array = $noi51->getData();
            if (isset($noi51Array['Pensioner']) && is_array($noi51Array['Pensioner']) && count(
                    $noi51Array['Pensioner']
                ) > 0) {
                $this->result->addDebugMessages('noi51 hasIncome: true');
                return true;
            }
        }

        $this->result->addDebugMessages('hasIncome: false');
        return false;
    }

    /**
     * Просрочена главница по активни кредити (общо за банкови и небанкови активни кредити)
     * @return float|bool
     */
    private function getCcrOverduePrincipal()
    {
        /** @var UsersCcr $ccr */
        $ccr = $this->credit->ccr;
        if (!$ccr) {
            $this->result->addDebugMessages(__FUNCTION__ . ': false');
            return false;
        }

        $response = $ccr->getProsrochenaGlavnica();
        $this->result->addDebugMessages(__FUNCTION__ . ': ' . $response);
        return $response;
    }

    /**
     * ост. просрочена главница по активни кредити над 90 дни
     * @return float|bool
     */
    private function getCcrOverduePrincipalOver90Days()
    {
        /** @var UsersCcr $ccr */
        $ccr = $this->credit->ccr;
        if (!$ccr) {
            $this->result->addDebugMessages(__FUNCTION__ . ': false');
            return false;
        }
        $response = $ccr->getOverduePrincipalOver90Days();
        $this->result->addDebugMessages(__FUNCTION__ . ': ' . $response);
        return $response;
    }

    /**
     * Score от стик кредит на ЦКР
     * @return bool|float
     */
    private function getStikCcrScore()
    {
        /** @var UsersCcr $ccr */
        $ccr = $this->credit->ccr;
        if (!$ccr) {
            return false;
        }
        return $ccr->total_percent;
    }

}
