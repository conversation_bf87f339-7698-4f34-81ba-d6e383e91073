<?php
/**
 * @var MenuItem $menu
 */

?>
<li class="sidebar-item">
    @if($menu->title)
        <a class="{{$menu->items?'sidebar-link has-arrow':'sidebar-link'}}" title="{{__($menu->title)}}"
           href="{{$menu->getUrl()}}" aria-expanded="false">
            @if($menu->icon)
                <i class="{{$menu->icon}}"></i>
            @endif
            <span class="hide-menu">{{ __($menu->title) }}</span>
        </a>
    @endif
    @if($menu->getItems()->count())
        <ul aria-expanded="false" class="collapse first-level base-level-line">
            @foreach($menu->getItems() as $item)
                <x-common::menu.items :menu="$item"/>
            @endforeach
        </ul>
    @endif
</li>
