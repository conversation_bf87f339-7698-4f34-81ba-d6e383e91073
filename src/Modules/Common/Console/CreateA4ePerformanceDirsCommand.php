<?php

namespace Modules\Common\Console;

use Illuminate\Support\Facades\Storage;
use Modules\Common\Services\StorageService;
use Symfony\Component\Console\Command\Command;

class CreateA4ePerformanceDirsCommand extends CommonCommand
{
    protected $name = 'script:create-a4e-performance-dirs';
    protected $signature = 'script:create-a4e-performance-dirs';
    protected $description = 'Creates A4e Directories';

    public function handle(): int
    {
        $this->startLog();
        if (!Storage::exists(StorageService::PATH_TO_A4E_PERFORMANCE_FINAL)) {
            Storage::disk('local')->makeDirectory(StorageService::PATH_TO_A4E_PERFORMANCE_FINAL, 0777, true, true);
        }

        if (!Storage::exists(StorageService::PATH_TO_A4E_PERFORMANCE_TMP)) {
            Storage::disk('local')->makeDirectory(StorageService::PATH_TO_A4E_PERFORMANCE_TMP, 0777, true, true);
        }
        $this->finishLog();

        return Command::SUCCESS;
    }
}
