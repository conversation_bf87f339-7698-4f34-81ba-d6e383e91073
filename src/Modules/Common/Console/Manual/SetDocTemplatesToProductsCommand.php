<?php

namespace Modules\Common\Console\Manual;

use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\ProductDocumentTemplate;
use Symfony\Component\Console\Command\Command;

class SetDocTemplatesToProductsCommand extends CommonCommand
{
    protected $name = 'script:set-doc-templates-to-products';
    protected $signature = 'script:set-doc-templates-to-products';
    protected $description = 'Set doc templates for products, based on product type OR office';

    public function handle()
    {
        $this->startLog($this->getClassName());


        $products = DB::select(DB::raw("
            select
                p.product_id,
                p.product_type_id,
                (select op.office_id from office_product op where op.product_id = p.product_id limit 1) as office_id
            from product p
            where p.active = 1
        "));
        $todo = count($products);
        $done = 0;


        $templates = [
            // related tpls
            'contract' => 1,
            'sef' => 1,
            'general_terms' => 1,

            // unique tpls
            'repayment_plan' => 0,
            'order_record' => 0,
            'personal_data_declaration' => 0,
            'application' => 0,
        ];


        $insert = [];
        foreach ($products as $product) {
            foreach ($templates as $templateKey => $hasLogic) {
                $docTplId = $this->getTemplateId($templateKey, $hasLogic, $product);
                if ($docTplId) {
                    $insert[] = [
                        'product_id' => $product->product_id,
                        'document_template_type' => $templateKey,
                        'document_template_id' => $docTplId,
                        'active' => 1,
                        'deleted' => 0,
                    ];
                }
            }
        }

        ProductDocumentTemplate::insert($insert);
        $done = count($insert);

        $msg = 'Processed: ' . $done . ', Total: ' . $todo;
        $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);


        return Command::SUCCESS;
    }

    private function getTemplateId(string $key, int $hasLogic, $productObj): int
    {
        if (!$hasLogic) {
            $docTpl = DB::selectOne(DB::raw("
                select dp.document_template_id, dp.name
                from document_template dp
                where
                    dp.type = '" . $key . "'
                    AND dp.active = 1
                    AND dp.deleted = 0
                limit 1
            "));

            return $docTpl?->document_template_id ?? 0;
        }

        $whereAdd = [];
        $whereAdd[] = " dp.type = '" . $key . "' ";
        $whereAdd[] = " dp.active = 1 ";
        $whereAdd[] = " dp.deleted = 0 ";
        if ('contract' == $key) {
            if ($productObj->product_type_id == 1) { // булит
                $whereAdd[] = " dp.name ILIKE '%булит%' ";
            }
            if ($productObj->product_type_id == 2) { // амортизиран
                $whereAdd[] = " dp.name ILIKE '%амортизиран%' ";
            }
            if ($productObj->office_id == 1) {
                $whereAdd[] = " dp.name ILIKE '%сайт%' ";
            } else {
                $whereAdd[] = " dp.name ILIKE '%офис%' ";
            }
        }
        if ('sef' == $key) {
            if ($productObj->product_type_id == 1) { // булит
                $whereAdd[] = " dp.name ILIKE '%булит%' ";
            }
            if ($productObj->product_type_id == 2) { // амортизиран
                $whereAdd[] = " dp.name ILIKE '%амортизиран%' ";
            }
        }
        if ('general_terms' == $key) {
            if ($productObj->office_id == 1) {
                $whereAdd[] = " dp.name ILIKE '%ОТ РАЗСТОЯНИЕ %' ";
            } else {
                $whereAdd[] = " dp.name NOT ILIKE '%ОТ РАЗСТОЯНИЕ %' ";
            }
        }

        $docTpl = DB::selectOne(DB::raw("
            select dp.document_template_id
            from document_template dp
            where " . implode(" AND ", $whereAdd) . "
            limit 1
        "));

        return $docTpl?->document_template_id ?? 0;
    }
}
