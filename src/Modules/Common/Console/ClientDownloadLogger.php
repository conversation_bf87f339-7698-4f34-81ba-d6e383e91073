<?php

namespace Modules\Common\Console;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\ClientDownloadDocLog;
use Modules\Common\Models\File;
use Symfony\Component\Console\Command\Command;

class ClientDownloadLogger extends CommonCommand
{
    protected $name = 'script:client-download-docs-logger';
    protected $signature = 'script:client-download-docs-logger';
    protected $description = 'Create stats for client who download his docs from profile';

    protected int $total;
    protected int $processed;

    public function handle(): bool
    {
        $this->startLog();

        $startTime = Carbon::now()->subHours(24);
        $rows = DB::table('raw_request')
            ->select('raw_request.*')
            ->leftJoin('client_download_doc_log', 'client_download_doc_log.raw_request_id', 'raw_request.raw_request_id')
            ->whereNull('client_download_doc_log.id')
            ->where('raw_request.type', 'api')
            ->where('raw_request.created_at', '>=', $startTime)
            ->where('raw_request.url', 'LIKE', '%/api/v1/get-client-file%')
            ->where('raw_request.request', 'LIKE', '%"client_id"%')
            ->get();

        $done = 0;
        $todo = $rows->count();
        if (!$todo) {
            $msg = 'Processed: ' . $done . ', Total: ' . $todo;
            $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

            return Command::FAILURE;
        }


        $inserts = [];
        foreach ($rows as $row) {

            $request  = $row->request;
            $response = $row->response;

            $clientId = null;
            if (preg_match('/"client_id":"(\d+)"/', $request, $matches)) {
                $clientId = (int) $matches[1];
            }
            if (empty($clientId)) {
                /** @phpstan-ignore-next-line  */
                dump('Failed to find client id in raw.req #' . $row->raw_request_id);
                continue;
            }

            $fileId = null;
            if (preg_match('/"file_id":"(\d+)"/', $request, $matches)) {
                $fileId = (int) $matches[1];
            }
            if (empty($fileId)) {
                /** @phpstan-ignore-next-line  */
                dump('Failed to find file id in raw.req #' . $row->raw_request_id);
                continue;
            }

            $success = false;
            if (preg_match('/"success":[\s+]?["]?(true)["]?/', $response, $matches)) {
                $success = true;
            }

            $fileType = null;
            $file = File::where('file_id', $fileId)->first();
            if (!empty($file->file_type)) {
                $fileType = $file->file_type;
            }


            $rowInsert = [
                'raw_request_id' => $row->raw_request_id,
                'client_id' => $clientId,
                'file_id' => $fileId,
                'file_type' => $fileType,
                'ip' => $row->ip,
                'browser' => $row->browser,
                'success' => (int) $success,
            ];

            $inserts[] = $rowInsert;
        }


        if (empty($inserts)) {
            $msg = 'Processed: ' . $done . ', Total: ' . $todo;
            $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

            return Command::FAILURE;
        }


        if (ClientDownloadDocLog::insert($inserts)) {
            $done = count($inserts);
        }


        $msg = 'Processed: ' . $done . ', Total: ' . $todo;
        $this->finishLog([$msg, $this->executionTimeString()], $todo, $done, $msg);

        return Command::SUCCESS;
    }
}
