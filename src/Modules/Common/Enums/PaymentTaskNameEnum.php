<?php

namespace Modules\Common\Enums;

use Modules\Common\Enums\Payment\PaymentMethodEnum;

enum PaymentTaskNameEnum: string
{
    case GIVE_OUT_CASH = 'give_out_cash';
    case TRANSFER_TO_BANK = 'transfer_to_bank';
    case SEND_EASY_PAY = 'send_easy_pay';
    case SEND_EASY_PAY_MANUALLY = 'send_easy_pay_manually';
    case REQUEST_EASY_PAY_REFUND = 'request_easy_pay_refund';
    case WAIT_REFUND_STATE = 'wait_refund_state';

    case DISTRIBUTE_PAYMENT = 'distribute_payment';
    case FIND_CLIENT = 'find_client';
    case CORRECT_THE_AMOUNT = 'correct_the_amount';

    case NONE = 'no_task';

    case REGISTER_EASY_PAY_PAYMENT_FAILED = 'register_easy_pay_payment_failed';

    public static function nameByPaymentMethod(PaymentMethodEnum $method): self
    {
        return match ($method) {
            PaymentMethodEnum::CASH => self::GIVE_OUT_CASH,
            PaymentMethodEnum::BANK => self::TRANSFER_TO_BANK,
            PaymentMethodEnum::EASY_PAY => self::SEND_EASY_PAY
        };
    }

    public static function nameByPaymentStatus(
        PaymentStatusEnum $fromStatus,
        ?PaymentMethodEnum $method = null
    ): self
    {
        return match ($fromStatus) {
            PaymentStatusEnum::NEW => self::nameByPaymentMethod($method),
            PaymentStatusEnum::EASY_PAY_SENDING_FAILED => self::SEND_EASY_PAY_MANUALLY,
            PaymentStatusEnum::EASY_PAY_SENT => self::REQUEST_EASY_PAY_REFUND,
            PaymentStatusEnum::CANCELED, PaymentStatusEnum::DELIVERED => self::NONE
        };
    }
}
