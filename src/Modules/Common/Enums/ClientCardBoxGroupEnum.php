<?php

namespace Modules\Common\Enums;

enum ClientCardBoxGroupEnum: string
{
    case TASK_FOR_APPROVE = 'task_for_approve';
    case TASK_FOR_SALE = 'task_for_sales';
    case TASK_FOR_COLLECT = 'task_for_collect';
    case CLIENT_WITH_LOAN = 'client_with_loan';
    case CLIENT_WITHOUT_LOAN = 'client_without_loan';

    public function boxes(): array
    {
        return match ($this) {
            self::TASK_FOR_APPROVE => [
                'boxOpenTasksGenerateDocuments',
                'boxCustomerInfo',
                'boxScoring',
                'boxCustomerPictures',
                'boxCommunication',
                'boxManualCommunication',
                'boxApplicationParams',
                'boxDocuments',
                'boxHistoryOfApplicationsAndLoans',
                'boxLoanApproveActions',
                'boxClientLoan',
                'boxLoanUpdate',
                'boxGuarantorLoans',
            ],
            self::TASK_FOR_SALE => [
                'boxOpenTasksGenerateDocuments',
                'boxCustomerInfo',
                'boxCustomerPictures',
                'boxCommunication',
                'boxManualCommunication',

                'boxApplicationParams',
                'boxSaleInformation',
                'boxSalesActions',
                'boxLoanUpdate',
                'boxGuarantorLoans',
            ],
            self::TASK_FOR_COLLECT => [
                'boxOpenTasksGenerateDocuments',
                'boxCustomerInfo',
                'boxCustomerPictures',
                'boxCommunication',
                'boxManualCommunication',
                'boxCollectActions',
                'boxApplicationParams',
                'boxLast5Payments',
                'boxAmountDue',
                'boxLastBucketTasks',
                'boxLastNoi7Report',
                'boxGuarantorLoans',
            ],
            self::CLIENT_WITH_LOAN => [
                'boxOpenTasksGenerateDocuments',
                'boxCustomerInfo',
                'boxCustomerPictures',
                'boxCommunication',
                'boxManualCommunication',

                /// current route cards
                'boxApplicationParams',
                'boxSaleInformation',
                'boxLast5Payments',
                'boxClientEarlyRepayment',

                /// add box loan slider at end
                'boxLoanUpdate',
                'boxSkipCreatingBucketTask',
                'boxGuarantorLoans',
            ],
            self::CLIENT_WITHOUT_LOAN => [
                'boxOpenTasksGenerateDocuments',
                'boxApplicationParams',
                'boxHistoryOfApplicationsAndLoans',
                'boxCustomerInfo',
                'boxCustomerPictures',
                'boxCommunication',
                'boxManualCommunication',
                'boxLoanUpdate',
                'boxSaleInformation',
                'boxGuarantorLoans',
            ],
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::TASK_FOR_APPROVE => __('Task for approve'),
            self::TASK_FOR_SALE => __('Task for sale'),
            self::TASK_FOR_COLLECT => __('Task for collect'),
            self::CLIENT_WITH_LOAN => __('Client with loan'),
            self::CLIENT_WITHOUT_LOAN => __('Client without loan'),
        };
    }

    public static function selectOptions(): array
    {
        return [
            self::TASK_FOR_APPROVE->value => self::TASK_FOR_APPROVE->label(),
            self::TASK_FOR_SALE->value => self::TASK_FOR_SALE->label(),
            self::TASK_FOR_COLLECT->value => self::TASK_FOR_COLLECT->label(),
            self::CLIENT_WITH_LOAN->value => self::CLIENT_WITH_LOAN->label(),
            self::CLIENT_WITHOUT_LOAN->value => self::CLIENT_WITHOUT_LOAN->label(),
        ];
    }

    public static function toArray(): array
    {
        return [
            self::TASK_FOR_APPROVE->value,
            self::TASK_FOR_SALE->value,
            self::TASK_FOR_COLLECT->value,
            self::CLIENT_WITH_LOAN->value,
            self::CLIENT_WITHOUT_LOAN->value,
        ];
    }
}
