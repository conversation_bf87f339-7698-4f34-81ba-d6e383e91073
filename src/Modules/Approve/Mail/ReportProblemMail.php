<?php

namespace Modules\Approve\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

final class ReportProblemMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Collection $mvrUnparsed,
        public Collection $ccrUnparsed,
        public Collection $a4eUnparsed,
        public \Illuminate\Database\Eloquent\Collection $noiUnparsed,
        public \Illuminate\Database\Eloquent\Collection $noiCallStatusFour,
        public \Illuminate\Database\Eloquent\Collection $noiCallStatusOne,
        public int $noiAllCount,
        public int $loansAllCount,
        public \Illuminate\Database\Eloquent\Collection $loansWithNoi,
        public int $noiWeekUnparsedCount = 0,
    ) {
    }

    public function envelope(): Envelope
    {
        $from = config('mail.reports_monitor')['sender'];

        return new Envelope(
            from: new Address($from['from'], $from['name']),
            subject: strtoupper(config('app.project')) . ' - ThirdParty Report ('
                . strtoupper(config('app.env')) . '): ' . now()->format('Y-m-d'),
        );
    }

    public function content(): Content
    {
        return new Content(view: 'approve::emails.report-problem');
    }

    public function attachments(): array
    {
        return [];
    }
}
