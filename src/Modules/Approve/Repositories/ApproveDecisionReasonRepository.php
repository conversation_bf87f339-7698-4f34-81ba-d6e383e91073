<?php


namespace Modules\Approve\Repositories;

use Exception;
use Illuminate\Support\Collection;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Repositories\BaseRepository;

class ApproveDecisionReasonRepository extends BaseRepository
{

    protected ApproveDecisionReason $approveDecisionReason;

    public function __construct(?ApproveDecisionReason $approveDecisionReason = null)
    {
        if(! $approveDecisionReason){
            $approveDecisionReason = new ApproveDecisionReason();
        }
        $this->approveDecisionReason = $approveDecisionReason;
    }

    /**
     * @param int|null $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        ?int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'approve_decision_reason_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = ApproveDecisionReason::orderByRaw(implode(', ', $this->prepareOrderStatement($order)));

        if (!empty($joins)) {
            foreach ($joins as $joinType => $joinVars) {
                foreach ($joinVars as $key => $joinArgs) {
                    $builder->{$joinType}(
                        $joinArgs[0], // reference table
                        $joinArgs[1], // reference column
                        $joinArgs[2], // sign
                        $joinArgs[3]  // reference condition
                    );
                }
            }
        }

        if (!empty($where)) {
            foreach ($where as $seqNum => $row) {
                if ($row[1] == 'in') {
                    $builder->whereIn($row[0], $row[2]);
                    unset($where[$seqNum]);
                }
            }

            $builder->where($where);
        }

        return $limit == null ? $builder->get() : $builder->paginate($limit);
    }

    /**
     * @param array $data
     *
     * @return ApproveDecisionReason $approveDecisionReason
     */
    public function create(array $data)
    {
        $appDecReason = new ApproveDecisionReason();
        $appDecReason->fill($data);
        $appDecReason->save();

        return $appDecReason;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getApproveDecisions()
    {
        return ApproveDecision::where(
            [
                'deleted' => 0,
                'active' => 1,
            ]
        )->get();
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     * @param array $data
     *
     * @return ApproveDecisionReason
     */
    public function update(ApproveDecisionReason $approveDecisionReason, array $data)
    {
        $approveDecisionReason->fill($data);
        $approveDecisionReason->save();

        return $approveDecisionReason;
    }

    /**
     * @param int $id
     *
     * @return ApproveDecisionReason|null
     */
    public function getById(int $id): ?ApproveDecisionReason
    {
        $appDecReasons = ApproveDecisionReason::where(
            [
                'approve_decision_reason_id' => $id,
                'deleted' => 0,
            ]
        )
            ->get();

        return $appDecReasons->first();
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     *
     * @throws Exception
     */
    public function delete(ApproveDecisionReason $approveDecisionReason)
    {
        $approveDecisionReason->delete();
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     */
    public function disable(ApproveDecisionReason $approveDecisionReason)
    {
        $approveDecisionReason->disable();
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     */
    public function enable(ApproveDecisionReason $approveDecisionReason)
    {
        $approveDecisionReason->enable();
    }

    /**
     * @param int $deleted
     * @param int $active
     *
     * @return Collection
     */
    public function getAllApproveDecisionReasons(
        int $deleted = 0,
        int $active = 1
    ): Collection {
        return ApproveDecisionReason::where(
            ['deleted' => $deleted, 'active' => $active]
        )
            ->orderBy('approve_decision_reason_id', 'ASC')
            ->get();
    }
}
