<?php

namespace Modules\Approve\Repositories;

use Exception;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Repositories\BaseRepository;

class ApproveDecisionRepository extends BaseRepository
{
    public function __construct(
        protected ApproveDecision $approveDecision = new ApproveDecision
    ) {
    }

    /**
     * @param int|null $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        ?int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'approve_decision_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = ApproveDecision::orderByRaw(implode(', ', $this->prepareOrderStatement($order)));

        if (!empty($joins)) {
            foreach ($joins as $joinType => $joinVars) {
                foreach ($joinVars as $key => $joinArgs) {
                    $builder->{$joinType}(
                        $joinArgs[0], // reference table
                        $joinArgs[1], // reference column
                        $joinArgs[2], // sign
                        $joinArgs[3]  // reference condition
                    );
                }
            }
        }

        if (!empty($where)) {
            foreach ($where as $seqNum => $row) {
                if ($row[1] == 'in') {
                    $builder->whereIn($row[0], $row[2]);
                    unset($where[$seqNum]);
                }
            }

            $builder->where($where);
        }

        return $limit == null ? $builder->get() : $builder->paginate($limit);
    }

    /**
     * @param array $data
     *
     * @return ApproveDecision $approveDecision
     */
    public function create(array $data)
    {
        $approveDecision = new ApproveDecision();
        $approveDecision->fill($data);
        $approveDecision->save();

        return $approveDecision;
    }

    /**
     * @param ApproveDecision $approveDecision
     * @param array $data
     *
     * @return ApproveDecision
     */
    public function update(ApproveDecision $approveDecision, array $data)
    {
        $approveDecision->fill($data);
        $approveDecision->save();

        return $approveDecision;
    }

    /**
     * @param int $id
     *
     * @return mixed
     */
    public function getById(int $id)
    {
        $approveDecision = ApproveDecision::where(
            'approve_decision_id',
            '=',
            $id
        )->get();

        return $approveDecision->first();
    }

    /**
     * @return array
     */
    public function getTypes()
    {
        return [
            ApproveDecision::APPROVE_DECISION_TYPE_FINAL,
            ApproveDecision::APPROVE_DECISION_TYPE_WAITING,
        ];
    }

    /**
     * @param ApproveDecision $approveDecision
     *
     * @throws Exception
     */
    public function delete(ApproveDecision $approveDecision)
    {
        $approveDecision->delete();
    }

    /**
     * @param ApproveDecision $approveDecision
     */
    public function disable(ApproveDecision $approveDecision)
    {
        $approveDecision->disable();
    }

    /**
     * @param ApproveDecision $approveDecision
     */
    public function enable(ApproveDecision $approveDecision)
    {
        $approveDecision->enable();
    }

    /**
     * @return mixed
     */
    public function getAllApproveDecisions()
    {
        return $this->approveDecision::orderBy('name', 'ASC')->get();
    }

    /**
     * @param string $type
     *
     * @return mixed
     */
    public function getAllByType(string $type)
    {
        return ApproveDecision::where(
            [
                'type' => $type,
                'active' => 1,
            ]
        )->get();
    }
}
