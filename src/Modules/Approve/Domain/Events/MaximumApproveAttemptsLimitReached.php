<?php

namespace Modules\Approve\Domain\Events;

use App\Providers\EventServiceProvider;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Approve\Application\Listeners\CancelLoanListener;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;

/**
 * @see LoanForDelay::save()
 * @see EventServiceProvider
 * @see CancelLoanListener::handle()
 */
class MaximumApproveAttemptsLimitReached
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public int $loanId){}

    public function dto(): DecisionDto
    {
        return DecisionDto::from([
            'loan_id' => $this->loanId,
            'admin_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
            'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            'description' => 'Loan is disapproved. Too many postponements.'
        ]);
    }
}
