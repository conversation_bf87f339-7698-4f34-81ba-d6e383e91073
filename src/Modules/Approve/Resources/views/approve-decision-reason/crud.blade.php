@extends('layouts.app')

@section('content')
    <div class="row">
        @php
            $action = !empty($approveDecisionReason) ?
                route('approve.approveDecisionReason.update', $approveDecisionReason->approve_decision_reason_id) :
                route('approve.approveDecisionReason.store');
                $description = old('approveDecisionReason.description') ?? $approveDecisionReason->description ?? '';
        @endphp
        <form method="POST"
              action="{{ $action }}"
              accept-charset="UTF-8" class="col-12">
            @csrf
            <div class="row">
                <div class="col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="form-group">
                                <label for="name" class="control-label required">{{ __('table.Name') }}</label>
                                <input class="form-control" required="required" minlength="2" maxlength="50" name="name"
                                       type="text" id="name"
                                       value="{{ old('approveDecisionReason.name') ?? $approveDecisionReason->name ?? ''}}">
                            </div>

                            <div class="form-group">
                                <label for="description" class="control-label required">
                                    {{ __('table.Description') }}
                                </label>
                                <textarea class="form-control" minlength="2" maxlength="255" name="description"
                                          cols="50" rows="10" id="description">{{ $description }}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="approve_decision_id"
                                       class="control-label required">{{ __('approve::approveDecision.approveDecision') }}</label>
                                <select class="form-control" required="required" id="approve_decision_id"
                                        name="approve_decision_id">
                                    @foreach($approveDecisions as $approveDecision)
                                        <option
                                            value="{{ $approveDecision->approve_decision_id }}"
                                            @if(
                                                !empty($approveDecisionReason) &&
                                                $approveDecisionReason->approve_decision_id === $approveDecision->approve_decision_id
                                            )
                                            selected
                                            @endif
                                        >
                                            {{ __('approve::approveDecision.' . $approveDecision->name) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-6 btns-form-panel">
                                    @if(Route::is('approve.approveDecisionReason.create'))
                                        <x-btn-create-simple/>
                                    @else
                                        <x-btn-update-simple/>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection
