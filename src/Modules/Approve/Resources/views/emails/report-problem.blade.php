@php
    /**
     * @var int $noiAllCount
     * @var int $noiWeekUnparsedCount
     * @var int $loansAllCount
     * @var \Modules\Common\Models\NoiReport[]|\Illuminate\Database\Eloquent\Collection $noiCallStatusFour
     * @var \Modules\Common\Models\NoiReport[]|\Illuminate\Database\Eloquent\Collection $noiUnparsed
     * @var \Modules\Common\Models\Loan[]|\Illuminate\Database\Eloquent\Collection $loansWithNoi
     * @var \Illuminate\Support\Collection $ccrUnparsed
     * @var \Illuminate\Support\Collection $mvrUnparsed
     * @var \Illuminate\Support\Collection $a4eUnparsed
     */
@endphp

<html>
<body>
<h4>Noi reports</h4>

<p>Total loans for online office yesterday: {{ $loansAllCount }}</p>
<p>Total reports for yesterday: {{ $noiAllCount }}</p>
<br>
<p>Loans without reports for yesterday: {{ $loansWithNoi->count() }}</p>
<br>
<p>Reports with empty parsed data for yesterday: {{ $noiUnparsed->count() }}</p>
<p>Reports with empty parsed data for last week: {{ $noiWeekUnparsedCount . '/(' . round($noiWeekUnparsedCount/7) . ')' }}</p>
<br>
<p>Reports with callStatus=4 for yesterday: {{ $noiCallStatusFour->count() }} - BROKEN PROXY</p>
<p>Reports with callStatus=1 for yesterday: {{ $noiCallStatusOne->count() }} - NO DATA IN NOI REGISTER</p>
<br>

@if($loansWithNoi->isNotEmpty())
    <p>Loans without reports:</p>
    <table>
        <tr>
            <th>Loan ID</th>
            <th>PIN</th>
        </tr>
        @foreach($loansWithNoi as $loan)
            <tr>
                <td>{{ $loan->loan_id }}</td>
                <td>{{ $loan->client->pin }}</td>
            </tr>
        @endforeach
    </table>
@endif

@if($noiUnparsed->isNotEmpty())
    <p>Reports with empty parsed data:</p>
    <table>
        <tr>
            <th>Loan ID</th>
            <th>Report ID</th>
            <th>PIN</th>
            <th>Date</th>
        </tr>
        @foreach($noiUnparsed as $report)
            <tr>
                <td>{{ $report->loans->first()?->loan_id }}</td>
                <td>{{ $report->noi_report_id }}</td>
                <td>{{ $report->pin }}</td>
                <td>{{ $report->created_at }}</td>
            </tr>
        @endforeach
    </table>
@endif

@if($noiCallStatusFour->isNotEmpty())
    <p>Reports with callStatus=4:</p>
    <table>
        <tr>
            <th>Loan ID</th>
            <th>Report ID</th>
            <th>PIN</th>
            <th>Date</th>
        </tr>
        @foreach($noiCallStatusFour as $report)
            <tr>
                <td>{{ $report->loans->first()?->loan_id }}</td>
                <td>{{ $report->noi_report_id }}</td>
                <td>{{ $report->pin }}</td>
                <td>{{ $report->created_at }}</td>
            </tr>
        @endforeach
    </table>
@endif

<hr>

<h4>CCR reports</h4>
@if($ccrUnparsed->isNotEmpty())
    <p>Bad reports for yesterday: {{ $ccrUnparsed->count() }}</p>
    <p>Details:</p>
    <table>
        @foreach($ccrUnparsed as $report)
            <tr>
                <td>{{$report->pin}}</td>
                <td>{{$report->created_at}}</td>
            </tr>
        @endforeach
    </table>
@else
    <p>No bad reports</p>
@endif

<hr>

<h4>MVR reports</h4>
@if($mvrUnparsed->isNotEmpty())
    <p>Bad reports for yesterday: {{ $mvrUnparsed->count() }}</p>
    <p>Details:</p>
    <table>
        @foreach($mvrUnparsed as $report)
            <tr>
                <td>{{$report->pin}}</td>
                <td>{{$report->created_at}}</td>
            </tr>
        @endforeach
    </table>
@else
    <p>No bad reports</p>
@endif

<hr>

<h4>A4E reports</h4>
@if($a4eUnparsed->isNotEmpty())
    <p>Bad reports for yesterday: {{ $a4eUnparsed->count() }}</p>
    <p>Details:</p>
    <table>
        @foreach($a4eUnparsed as $report)
            <tr>
                <td>{{$report->client_id}}</td>
                <td>{{$report->created_at}}</td>
            </tr>
        @endforeach
    </table>
@else
    <p>No bad reports</p>
@endif
</body>
<style>
    td, th {
        text-align: center;
        width: 100px;
    }
</style>
</html>
