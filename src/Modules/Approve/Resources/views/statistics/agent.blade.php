@extends('layouts.app')

@section('content')
    <x-card-filter-form :filter-form="$agentStatsFilterForm" col-lg="2"/>

    <x-card>
        <x-slot:title>{{ __('table.AgentStatistics') }}</x-slot:title>

        <div id="refreshTable">
            @include('approve::statistics.agent-table')
        </div>

    </x-card>
@endsection
@push('scripts')
    <script>
        $(document).ready(function () {
            $('#exportBtn').on('click', function (event) {
                event.preventDefault();
                var form = $('#AgentStatsFilterForm');
                var originalURL = form.attr('action');
                var exportURL = $(this).attr('href');
                form.attr('action', exportURL);
                form.submit();
                form.attr('action', originalURL);
            });

            $('[data-daterange-picker=true]').focusout(function(){
                var val = $(this).val().trim();
                if(val.split('-')[0] > 1900) {
                    var d = new Date(val);
                    var day = d.getDate() > 9 ? d.getDate() : '0' + d.getDate();
                    var month = d.getMonth() > 8 ? d.getMonth() + 1 : '0' + (d.getMonth() + 1);
                    $(this).val(day + '-' + month + '-' + d.getFullYear());
                }
            })
        });
    </script>
@endpush