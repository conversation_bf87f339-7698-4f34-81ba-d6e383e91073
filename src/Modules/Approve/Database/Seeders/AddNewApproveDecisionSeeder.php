<?php

namespace Modules\Approve\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;

/**
 * php artisan db:seed --class=\\Modules\\Approve\\Database\\Seeders\\AddNewApproveDecisionSeeder
 */
class AddNewApproveDecisionSeeder extends Seeder
{
    public function run(): void
    {
        ApproveDecision::create([
            'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_WAITING_DOCUMENTS,
            'name' => ApproveDecision::APPROVE_DECISION_WAITING_DOCUMENTS,
            'type' => ApproveDecision::APPROVE_DECISION_TYPE_WAITING,
            'active' => 1,
            'deleted' => 0,
            'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
        ]);
    }
}
