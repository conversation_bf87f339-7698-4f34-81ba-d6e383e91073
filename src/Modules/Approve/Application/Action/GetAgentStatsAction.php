<?php

namespace Modules\Approve\Application\Action;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Approve\Repositories\ApproveAgentStatsRepository;

final readonly class GetAgentStatsAction
{
    public function __construct(private ApproveAgentStatsRepository $repo)
    {
    }

    public function execute(array $filters): LengthAwarePaginator
    {
        return $this->repo->getPaginatorByFilters($filters);
    }
}