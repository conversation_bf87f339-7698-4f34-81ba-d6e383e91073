<?php

namespace Modules\Approve\Tests\Integration\Domain\Loan;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithoutEvents;
use Illuminate\Support\Facades\DB;
use Modules\Approve\Domain\Entities\AdminOffice as Sut;
use Modules\Approve\Domain\Exceptions\Admin\AdminFromLocalOfficeCannotApproveWeb;
use Modules\Approve\Domain\Exceptions\Admin\AdminOfficeNotAllowedToApprove;
use Modules\Approve\Domain\Exceptions\Admin\AdminOfficeRelationNotFound;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\AdministratorOffice;
use Modules\Common\Models\Office;
use Modules\Common\Models\OfficeType;
use Tests\TestCase;

class AdminOfficeTest extends TestCase
{
    use DatabaseTransactions;
    use WithoutEvents;

    public function testHappyPath()
    {
        $adminId = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $officeId = Office::OFFICE_ID_WEB;
        $dbOffice = Office::find($officeId);
        DB::table('administrator_office')->insert(['administrator_id'=>$adminId, 'office_id'=>$officeId]);
        $sut = app()->make(Sut::class);
        $sut->loadByIds($adminId, $officeId);
        $this->assertInstanceOf(Sut::class, $sut->checkApprovalPermissionsForOffice($dbOffice));
        $this->assertEquals($adminId, $sut->dbModel()->administrator_id);
    }

    public function testNotWebOfficeAdmin()
    {
        $adminId = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $officeId = Office::OFFICE_ID_WEB;
        AdministratorOffice::where(['office_id'=>$officeId, 'administrator_id'=>$adminId])->delete();
        $sut = app()->make(Sut::class);
        $this->expectException(AdminFromLocalOfficeCannotApproveWeb::class);
        $sut->loadByIds($adminId, $officeId);
    }

    public function testNotSameLocalOfficeAdmin()
    {
        $adminId = Administrator::DEFAULT_EASYPAY_USER_ID;
        $officeId = Office::OFFICE_ID_NOVI_PAZAR_1;
        $sut = app()->make(Sut::class);
        $this->expectException(AdminOfficeRelationNotFound::class);
        $sut->loadByIds($adminId, $officeId);
    }

    public function testSameLocalOfficeAdminButNotSelfApproveOffice()
    {
        $adminId = Administrator::DEFAULT_EASYPAY_USER_ID;
        $officeId = Office::OFFICE_ID_NOVI_PAZAR_1;
        $dbOffice = Office::find($officeId);
        $dbOffice->office_type_id=OfficeType::OFFICE_TYPE_NOT_SELF_APPROVE_ID;
        $dbOffice->save();
        DB::table('administrator_office')->insert(['administrator_id'=>$adminId, 'office_id'=>$officeId]);
        $sut = app()->make(Sut::class);
        $sut->loadByIds($adminId, Office::OFFICE_ID_NOVI_PAZAR_1);
        $this->expectException(AdminOfficeNotAllowedToApprove::class);
        $sut->checkApprovalPermissionsForOffice($dbOffice);
    }
}