<?php

namespace Modules\Approve\Tests\Integration\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Application\Action\ProcessLoanAction as Sut;
use Modules\Common\Database\Seeders\Test\SignedLoanSeeder;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Tests\TestCase;

class ProcessLoanActionTest extends TestCase
{
    use DatabaseTransactions;

    public function testHappyPath()
    {
        $this->seed(SignedLoanSeeder::class);
        $sut = app()->make(Sut::class);
        $sut->execute(Loan::get(SignedLoanSeeder::LOAN_ID));
        $this->assertEquals(LoanStatus::PROCESSING_STATUS_ID, $sut->loan->dbModel()->loan_status_id);
    }
}
