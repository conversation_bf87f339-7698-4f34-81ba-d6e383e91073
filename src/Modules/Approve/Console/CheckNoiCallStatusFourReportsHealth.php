<?php

namespace Modules\Approve\Console;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Mail;
use Modules\Approve\Mail\ReportNoiCallStatusFourProblemMail;
use Modules\Common\Console\CommonCommand;
use Modules\ThirdParty\Repositories\NoiReportRepository;

final class CheckNoiCallStatusFourReportsHealth extends CommonCommand
{
    protected $name = 'script:noi-call-status-four-report-health';
    protected $signature = 'script:noi-call-status-four-report-health';
    protected $description = 'Counts bad reports for the last day for: NOI with callStatus=4';
    protected string $logChannel = 'single';

    public function __construct(
        private readonly NoiReportRepository $noiRepo,
    ) {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->startLog();

        $from = Carbon::yesterday()->startOfDay();
        $to = Carbon::yesterday()->endOfDay();

        $noiCallStatusFour = $this->getNoiCallStatusFourReports($from, $to);

        $total = $noiCallStatusFour->count();

        if ($total) {
            Mail::to(config('mail.reports_monitor')['receivers'])->send(
                new ReportNoiCallStatusFourProblemMail(
                    $noiCallStatusFour,
                )
            );
        }

        $this->finishLog([$this->executionTimeString()], $total);
    }

    private function getNoiCallStatusFourReports(Carbon $from, Carbon $to): Collection
    {
        return $this->noiRepo->getCallStatusFour($from, $to)->with(['loans:loan_id'])->get([
            'noi_report_id', 'pin', 'created_at'
        ]);
    }
}
