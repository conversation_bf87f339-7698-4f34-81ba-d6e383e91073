<?php

namespace Modules\Admin\Services;

use Carbon\Carbon;
use Modules\Admin\Repositories\OfficeModuleRepository;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\AdministratorModule;
use Modules\Common\Models\OfficeModule;
use Modules\Common\Models\Permission;

class CreateAdministratorService
{

    public function __construct(protected OfficeModuleRepository $officeModuleRepository = new OfficeModuleRepository())
    {
    }

    public function run(OfficeModule $officeModule): void
    {
        $administrators = $officeModule->office->administrators;

        foreach ($administrators as $administrator) {
            $this->processAdministrator($officeModule, $administrator);
        }
    }

    private function processAdministrator(OfficeModule $officeModule, Administrator $administrator): void
    {
        $administratorModule = $this->getAdministratorModule($officeModule, $administrator);
        if (empty($administratorModule)) {
            $this->addAdministratorModule($officeModule, $administrator);
            return;
        }

        if ($this->isUpdatable($administratorModule, $officeModule)) {
            $this->updateAdministratorModule($administratorModule, $officeModule);
        }
    }

    /**
     * If permission type is higher - apply it,
     * or the administrator has not active anymore permissions and its in new office
     * with new/same module permissions
     * @param AdministratorModule $administratorModule
     * @param OfficeModule $officeModule
     * @return bool
     */
    private function isUpdatable(
        AdministratorModule $administratorModule,
        OfficeModule $officeModule
    ): bool {
        return ($administratorModule->type != $officeModule->type && $officeModule->type == Permission::TYPE_FULL)
            || !$administratorModule->active;
    }

    private function getAdministratorModule(
        OfficeModule $officeModule,
        Administrator $administrator
    ): ?AdministratorModule {
        return $administrator->modules()->where('module', $officeModule->module)->first();
    }

    private function addAdministratorModule(OfficeModule $officeModule, Administrator $administrator): void
    {
        $administratorModule = new AdministratorModule(
            [
                'administrator_id' => $administrator->getKey(),
                'module' => $officeModule->module,
                'type' => $officeModule->type,
                'start_at' => $officeModule->created_at,
            ]
        );
        $administratorModule->save();
    }

    public function updateAdministratorModule(
        AdministratorModule $administratorModule,
        OfficeModule $officeModule
    ): void {
        $administratorModule->type = $officeModule->type;
        $administratorModule->active = 1;
        $administratorModule->start_at = Carbon::now();
        $administratorModule->end_at = null;

        $administratorModule->save();
    }

}