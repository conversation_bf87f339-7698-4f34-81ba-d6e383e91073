<?php

namespace Modules\Admin\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Admin\Application\Actions\AgreementDataAction;
use Modules\Admin\Http\Requests\AgreementEditRequest;
use Modules\Admin\Http\Requests\AgreementSearchRequest;
use Modules\Admin\Services\AgreementService;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Agreement;

class AgreementController extends BaseController
{
    public string $pageTitle = 'Agreement list';
    public string $indexRoute = 'admin.agreements.list';
    private AgreementService $agreementService;

    public function __construct(
        AgreementService $agreementService
    )
    {
        $this->agreementService = $agreementService;

        parent::__construct();
    }

    public function list(
        AgreementSearchRequest $request,
        AgreementDataAction    $agreementDataAction
    )
    {
        $filters = $request->validated();

        $data = $agreementDataAction->executeIndex($filters);

        return view('admin::agreements.list', $data);
    }

    public function create(
        AgreementDataAction $agreementDataAction
    )
    {
        $data = $agreementDataAction->executeCreate();

        return view('admin::agreements.crud', $data);
    }

    public function edit(
        Agreement           $agreement,
        AgreementDataAction $agreementDataAction
    )
    {
        $data = $agreementDataAction->executeEdit($agreement);

        return view('admin::agreements.crud', $data);
    }

    public function store(AgreementEditRequest $request)
    {
        $this->agreementService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::agreementCrud.CreatedSuccessfully'));
    }

    public function update(Agreement $agreement, AgreementEditRequest $request)
    {
        $this->agreementService->update($agreement, $request->validated());

        return redirect()
            ->route('admin.agreements.list')
            ->with('success', __('admin::agreementCrud.UpdatedSuccessfully'));
    }

    public function delete(Agreement $agreement)
    {
        $this->agreementService->delete($agreement);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::agreementCrud.DeletedSuccessfully'));
    }

    public function disable(Agreement $agreement)
    {
        $this->agreementService->disable($agreement);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::agreementCrud.DisabledSuccessfully'));
    }

    public function enable(Agreement $agreement)
    {
        $this->agreementService->enable($agreement);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::agreementCrud.EnabledSuccessfully'));
    }
}
