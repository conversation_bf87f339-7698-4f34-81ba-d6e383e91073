<?php

namespace Modules\Admin\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Admin\Http\Requests\TmpRequestStepEditRequest;
use Modules\Admin\Http\Requests\TmpRequestStepSearchRequest;
use Modules\Admin\Services\TmpRequestStepService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\TmpRequestStep;

class TmpRequestStepController extends BaseController
{
    public string $pageTitle = 'Tmp request step list';
    public string $indexRoute = 'admin.tmp-request-steps.list';
    private TmpRequestStepService $tmpRequestStepService;

    /**
     * Tmp Request Step constructor.
     *
     * @param TmpRequestStepService $tmpRequestStepService
     *
     * @throws \ReflectionException
     */
    public function __construct(
        TmpRequestStepService $tmpRequestStepService
    ) {
        $this->tmpRequestStepService = $tmpRequestStepService;

        parent::__construct();
    }

    /**
     *
     * @param TmpRequestStepSearchRequest $request
     *
     * @return RedirectResponse|View
     *
     * @throws Exception
     */
    public function list(TmpRequestStepSearchRequest $request)
    {
        $this->checkForRequestParams($request);

        return view(
            'admin::tmp-request-steps.index',
            [
                'tmpRequestSteps' => $this->refresh(),
                'cacheKey' => $this->cacheKey,
            ]
        );
    }

    /**
     *
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function create()
    {
        return view('admin::tmp-request-steps.crud');
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return View|RedirectResponse
     *
     * @throws Exception
     */
    public function edit(TmpRequestStep $tmpRequestStep)
    {
        return view(
            'admin::tmp-request-steps.crud',
            compact('tmpRequestStep')
        );
    }

    /**
     * @param TmpRequestStepEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(TmpRequestStepEditRequest $request): RedirectResponse
    {
        $this->tmpRequestStepService->create($request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::tmpRequestStepCrud.CreatedSuccessfully'));
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     * @param TmpRequestStepEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function update(TmpRequestStep $tmpRequestStep, TmpRequestStepEditRequest $request): RedirectResponse
    {
        $this->tmpRequestStepService->update($tmpRequestStep, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::tmpRequestStepCrud.UpdatedSuccessfully'));
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function delete(TmpRequestStep $tmpRequestStep): RedirectResponse
    {
        $this->tmpRequestStepService->delete($tmpRequestStep);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::tmpRequestStepCrud.DeletedSuccessfully'));
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function disable(TmpRequestStep $tmpRequestStep): RedirectResponse
    {
        $this->tmpRequestStepService->disable($tmpRequestStep);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::tmpRequestStepCrud.DisabledSuccessfully'));
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(TmpRequestStep $tmpRequestStep): RedirectResponse
    {
        $this->tmpRequestStepService->enable($tmpRequestStep);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('admin::tmpRequestStepCrud.EnabledSuccessfully'));
    }

    /**
     * @param TmpRequestStepSearchRequest $request
     *
     * @return bool|RedirectResponse
     *
     * @throws Exception
     */
    public function setFilters(TmpRequestStepSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    /**
     * @param TmpRequestStepSearchRequest $request
     *
     * @return RedirectResponse|void
     *
     * @throws Exception
     */
    protected function checkForRequestParams(TmpRequestStepSearchRequest $request)
    {
        if ($request->exists(
            [
                'name',
                'description',
                'url',
                'active',
                'createdAt',
                'updatedAt',
            ]
        )) {
            $this->cleanFilters();
            $this->setFilters($request);
        }
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    public function refresh()
    {
        return $this->tmpRequestStepService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }
}
