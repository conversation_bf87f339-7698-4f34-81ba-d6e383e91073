<?php

namespace Modules\Admin\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;
use Modules\Common\Traits\DateBuilderTrait;

/**
 * Class AdministratorSearchRequest
 * @package Modules\Admin\Http\Requests
 */
class AdministratorSearchRequest extends BaseRequest implements ListSearchInterface
{
    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:50',
            'username' => 'nullable|string|max:50',
            'phone' => $this->getConfiguration('requestRules.phoneNullable'),
            'email' => 'nullable|string',
            'role_ids' => 'nullable|array',
            'role_ids.*' => 'sometimes|integer|exists:role,id',
            'office_ids' => 'nullable|array',
            'office_ids.*' => 'sometimes|integer|exists:office,office_id',
            'active' => $this->getConfiguration('requestRules.active'),
            'created_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
            'updated_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
        ];
    }
}
