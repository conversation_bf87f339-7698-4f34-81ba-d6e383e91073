<?php

namespace Modules\Admin\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class FiscalDeviceRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => $this->getConfiguration('requestRules.name'),
            'office_id' => 'required|numeric|exists:office,office_id',
            'tcp' => 'required|in:0,1',
            'server_ip' => 'required|ip',
            'server_version' => 'required|numeric',
            'server_port' => 'required|numeric|digits_between:1,5',
            'operator_num' => 'required|numeric|digits_between:1,10',
            'operator_pass' => 'nullable|numeric|digits_between:1,10',
        ];

        if ($this->input('tcp') == 1) {
            return array_merge(
                $rules,
                [
                    'ip' => 'required|ip',
                    'port' => 'required|numeric|digits_between:1,5',
                    'password' => 'nullable|string|max:100',
                ]
            );
        }

        return array_merge(
            $rules,
            [
                'com' => 'required|string|max:100',
                'baud' => 'required|numeric|digits_between:1,8',
            ]
        );
    }
}
