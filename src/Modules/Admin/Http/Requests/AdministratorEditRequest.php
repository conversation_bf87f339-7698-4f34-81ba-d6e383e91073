<?php

namespace Modules\Admin\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Common\Http\Requests\BaseRequest;

/**
 * Class AdministratorEditRequest
 *
 * @package Modules\Admin\Http\Requests
 */
class AdministratorEditRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //Remove duplicates in permissions
        $this->request->set('permissions', array_unique($this->input('permissions', [])));
        $rules = [
            'username' => [
                'required',
                'min:2',
                Rule::unique('administrator')
                    ->ignore($this->admin ? $this->admin->username : '', 'username')
            ],
            'first_name' => $this->getConfiguration('requestRules.firstName'),
            'middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'last_name' => $this->getConfiguration('requestRules.lastName'),
            'phone' => $this->getConfiguration('requestRules.phoneNullable'),
            'email' => [
                'nullable',
                'email:rfc',
                'min:5',
                'max:50',
            ],
            'permissions' => 'nullable|array',
            'permissions.*' => 'numeric|exists:permission,id',
            'role' => 'numeric|exists:role,id',
            'offices' => 'required|array',
            'offices.*' => 'exists:office,office_id',
            'settings' => 'required|array',
            'settings.*.setting_key' => 'required|exists:setting,setting_key',
            'settings.*.value' => 'nullable|string',
            'avatar' => 'image|mimes:jpeg,jpg,png|max:1024|nullable',
            'permission_additional_info' => 'nullable|array',
            'permission_additional_info.*' => 'nullable|array',
        ];

        if ($this->filled('password')) {
            $rules['password'] = 'required | confirmed';
        }

        return $rules;
    }

    /**
     * @return array|string[]
     */
    public function messages()
    {
        $messages = [
            'offices.required' => __('messages.AdminOffice'),
            'roles.required' => __('messages.AdminRole'),
            'permissions.required' => __('messages.AdminPermission'),
        ];

        return $messages;
    }
}
