<?php

namespace Modules\Admin\Repositories;

use Modules\Common\Models\CloseReason;
use Modules\Common\Repositories\BaseRepository;

class CloseReasonRepository extends BaseRepository
{
    protected CloseReason $closeReason;

    public function __construct(CloseReason $closeReason)
    {
        $this->closeReason = $closeReason;
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'close_reason_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = $this->closeReason::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );
        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    /**
     * @param int $closeReasonId
     *
     * @return mixed
     */
    public function getById(int $closeReasonId)
    {
        $closeReason = $this->closeReason::where(
            'close_reason_id',
            '=',
            $closeReasonId
        )->get();

        return $closeReason->first();
    }

    /**
     * @param array $data
     */
    public function create(array $data)
    {
        $closeReason = new CloseReason();
        $closeReason->fill($data);
        $closeReason->save();
    }

    /**
     * @param CloseReason $closeReason
     * @param array $data
     */
    public function update(CloseReason $closeReason, array $data)
    {
        $closeReason->fill($data);
        $closeReason->save();
    }

    /**
     * @param CloseReason $closeReason
     */
    public function delete(CloseReason $closeReason)
    {
        $closeReason->delete();
    }

    /**
     * @param CloseReason $closeReason
     */
    public function disable(CloseReason $closeReason)
    {
        $closeReason->disable();
    }

    /**
     * @param CloseReason $closeReason
     */
    public function enable(CloseReason $closeReason)
    {
        $closeReason->enable();
    }
}
