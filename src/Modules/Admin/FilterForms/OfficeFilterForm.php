<?php

namespace Modules\Admin\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\Branch;
use Modules\Common\Models\OfficeType;

class OfficeFilterForm extends BaseFilterForm
{
    public function buildForm(): void
    {
        $this->add('name', 'text', [
            'label' => __('table.Name'),
            'value' => $this->request->get('name', '')
        ]);

        $this->addPhoneFilter('office_phone');

        $this->addEmailFilter('email');

        $this->addActiveInActiveFilter();

        $officeTypeSelectOptions = $this->getOfficeTypeSelectOptions();
        $this->addSimpleSelectFilter(
            'officeTypeId',
            $officeTypeSelectOptions,
            __('table.OfficeSelectByOfficeType')
        );

        $officeBranchSelectOptions = $this->getOfficeBranchSelectOptions();
        $this->addSimpleSelectFilter(
            'branchId',
            $officeBranchSelectOptions,
            __('table.FilterByBranch')
        );

        $this->addDateFilterFromTo('created_at', __('table.FilterByCreatedAt'));

        $this->addDateFilterFromTo('updated_at', __('table.FilterByUpdatedAt'));
    }

    protected function getOfficeBranchSelectOptions(): array
    {
        return Branch::selectOptions('name', 'branch_id');
    }

    protected function getOfficeTypeSelectOptions(): array
    {
        $officeTypeSelectOptions = OfficeType::selectOptions('name', 'office_type_id');
        foreach ($officeTypeSelectOptions as $id => $name) {
            $officeTypeSelectOptions[$id] = __('admin::officeTypes.' . $name);
        }

        return $officeTypeSelectOptions;
    }
}
