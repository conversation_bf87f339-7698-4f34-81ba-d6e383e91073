<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\AdministratorClient;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'administrator_client',
            function ($table) {
                $table->bigIncrements('administrator_client_id');
                $table->integer('administrator_id')->unsigned()->index();
                $table->integer('client_id')->unsigned()->index();
                $table->enum('type', AdministratorClient::getTypes())->index();
                $table->integer('value');
                $table->tableCrudFields();

                $table->foreign('administrator_id')->references('administrator_id')->on('administrator');
                $table->foreign('client_id')->references('client_id')->on('client');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'administrator_client',
            function (Blueprint $table) {
                $table->dropForeign('administrator_client_administrator_id_foreign');
                $table->dropForeign('administrator_client_client_id_foreign');
            }
        );

        Schema::dropIfExists('administrator_client');
    }
};
