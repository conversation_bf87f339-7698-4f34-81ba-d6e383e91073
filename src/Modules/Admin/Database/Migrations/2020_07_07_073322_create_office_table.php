<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'office',
            function ($table) {
                $table->bigIncrements('office_id');
                $table->integer('branch_id')->unsigned()->nullable();
                $table->integer('city_id')->unsigned()->nullable();
                $table->integer('office_type_id')->unsigned()->nullable();

                $table->string('name');
                $table->string('description')->nullable();
                $table->string('address')->nullable();
                $table->string('phone')->nullable();
                $table->string('phone_additional')->nullable();
                $table->string('email')->nullable();
                $table->timestamp('access_start_at', 0)->nullable();
                $table->timestamp('access_end_at', 0)->nullable();

                $table->tableCrudFields();

                $table->foreign('branch_id')->references('branch_id')->on('branch');
                $table->foreign('city_id')->references('city_id')->on('city');
                $table->foreign('office_type_id')->references('office_type_id')->on('office_type');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('office', function (Blueprint $table) {
            $table->dropForeign('office_city_id_foreign');
            $table->dropForeign('office_office_type_id_foreign');
            $table->dropForeign('office_branch_id_foreign');
        });

        Schema::dropIfExists('office');
    }
};
