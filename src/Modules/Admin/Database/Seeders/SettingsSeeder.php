<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\SettingType;

class SettingsSeeder extends Seeder
{

    public function __construct(
        public SettingRepository $settingRepository
    ) {
    }

    /**
     * @return void
     */
    public function run(): void
    {
        $settingTypesCounter = count(SettingType::SETTING_TYPES);
        for ($i = 1; $i <= $settingTypesCounter; $i++) {
            $settingType = new SettingType();
            $settingType->fill(['name' => SettingType::SETTING_TYPES[$i]]);
            $settingType->save();
        }
        $this->seedDataFromEnum();
    }

    protected function seedDataFromEnum()
    {
        $cases = SettingsEnum::cases();
        foreach ($cases as $item) {
            $this->settingRepository->createFromEnum($item);
        }
    }
}
