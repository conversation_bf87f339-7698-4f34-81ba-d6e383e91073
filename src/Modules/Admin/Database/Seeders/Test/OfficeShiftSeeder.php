<?php

namespace Modules\Admin\Database\Seeders\Test;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Office;

class OfficeShiftSeeder extends Seeder
{
    public static string $currentDateString = '2023-12-01 00:00:00';
    public function run()
    {
        $data = [];
        //foreach (Office::all() as $office){
            $date = (new CurrentDate(self::$currentDateString))->now();
            while ($date->year < 2024){
                $data[] = [
                    'office_id'=>Office::OFFICE_ID_NOVI_PAZAR_1,//$office->getKey(),
                    'shift_date'=>$date->format('Y-m-d'),
                    'starts_at'=>'08:00',
                    'ends_at'=>'20:00',
                    'created_at'=>$date,
                    'day_of_week'=>strtolower($date->englishDayOfWeek),
                    'is_holiday' => false,
                    'comment' => 'test',
                ];
                $date = $date->addDay();
            }
        //}

        DB::table('office_shift')->insert($data);

    }
}
