<?php

namespace Modules\Admin\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\Admin\Models\ClientRateUs;
use Modules\Common\Models\Administrator;

class ClientRateUsFactory extends Factory
{
    protected $model = ClientRateUs::class;

    public function definition(): array
    {
        return [
            'created_by' => 1,
            'is_active' => 'yes',
            'client_name' => $this->faker->name(),
            'youtube_url' => 'https://www.youtube.com/watch?v=Yg--mAjIwC0',
            'description' => $this->faker->text,
            'slug' => $this->faker->slug(),
        ];
    }
}
