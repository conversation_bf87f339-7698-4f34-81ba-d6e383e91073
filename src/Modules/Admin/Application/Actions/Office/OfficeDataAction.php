<?php

namespace Modules\Admin\Application\Actions\Office;

use <PERSON>\LaravelFormBuilder\Form;
use <PERSON>\LaravelFormBuilder\FormBuilder;
use Modules\Admin\FilterForms\OfficeFilterForm;
use Modules\Admin\Repositories\OfficeRepository;

class OfficeDataAction
{
    public function __construct(
        private readonly FormBuilder      $formBuilder,
        private readonly OfficeRepository $officeRepository
    )
    {
    }

    public function execute(array $filters = [], int $perPage = 10): array
    {
        $data = [];
        $data['officeFilterForm'] = $this->getOfficeFilterForm();
        $data['offices'] = $this->officeRepository->getByFilters($filters, $perPage);

        return $data;
    }

    protected function getOfficeFilterForm(): Form
    {
        return $this->formBuilder->create(OfficeFilterForm::class, [
            'route' => 'admin.offices.list'
        ]);
    }
}
