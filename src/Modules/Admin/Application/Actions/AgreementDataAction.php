<?php

namespace Modules\Admin\Application\Actions;

use <PERSON>\LaravelFormBuilder\FormBuilder;
use <PERSON><PERSON>les\Admin\FilterForms\AgreementFilterForm;
use Modules\Admin\Forms\AgreementForm;
use Modules\Admin\Repositories\AgreementRepository;
use Modules\Common\Models\Agreement;

class AgreementDataAction
{
    public function __construct(
        private readonly AgreementRepository $agreementRepository,
        private readonly FormBuilder         $formBuilder
    )
    {
    }

    public function executeEdit(Agreement $agreement): array
    {
        $data['agreement'] = $agreement;
        $data['agreementForm'] = $this->formBuilder->create(AgreementForm::class, [
            'method' => 'POST',
            'route' => ['admin.agreements.update', $agreement->getKey()],
            'data-parsley-validate' => 'true',
            'model' => $agreement
        ]);

        return $data;
    }

    public function executeCreate(): array
    {
        $data['agreementForm'] = $this->formBuilder->create(AgreementForm::class, [
            'method' => 'POST',
            'route' => 'admin.agreements.store',
            'data-parsley-validate' => 'true'
        ]);

        return $data;
    }

    public function executeIndex(array $filters = []): array
    {
        $data['agreementFilterForm'] = $this->formBuilder->create(AgreementFilterForm::class, [
            'route' => 'admin.agreements.list'
        ]);
        $data['agreements'] = $this->agreementRepository->getFilterBy($filters);

        return $data;
    }
}
