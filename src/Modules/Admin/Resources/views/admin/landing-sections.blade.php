@php

$sectionsTemplateMap = [
    '#',
    __('table.SectionName'),
    __('table.CreatedAt'),
    __('table.UpdatedAt'),
    __('table.CreatedBy'),
    __('table.UpdatedBy'),
    __('table.Actions'),
];
@endphp

@extends('layouts.app')

@section('content')

    <div class="row">
        
        <div class="col-lg-12">
            
            <div class="d-block">

                <x-btn-create url="{{ route('admin.landing.sectionsCreate') }}" name="{{ __('btn.CreateNewSection') }}"/>
            </div>
        </div>

        <div class="col-lg-12 mt-5">
            
            <div class="table-responsive">

                <div>
                    <table class="table">

                        <thead>

                            <tr>

                                @foreach ($sectionsTemplateMap as $header) 

                                    <th scope="col" class="text-left">
                                        {{ $header }}
                                    </th>
                                @endforeach
                            </tr>
                        </thead>

                        <tbody>
                            
                            @if (!empty($sections))

                                @foreach($sections as $sectionIndex => $section)

                                    <tr
                                        @if(!$section['active'])
                                        class="not-active"
                                        @endif
                                    >
                                    
                                        <td>{{ $section['landing_section_id'] }}</td>
                                        <td>{{ $section['name'] }}</td>
                                        <td>{{ $section['created_at'] }}</td>
                                        <td>{{ $section['updated_at'] }}</td>
                                        <td>{{ $section['created_by'] }}</td>
                                        <td>{{ $section['updated_by'] }}</td>
                                        
                                        <td class="button-div">
                                            <div class="button-actions">
                                                
                                                <x-btn-edit
                                                    url="{{ route('admin.landing.sectionsEdit', $section['landing_section_id']) }}"/>

                                                @if (!empty($section['active']))
                                                    
                                                    <x-btn-disable
                                                        url="{{ route('admin.landing.sectionsDisableEnable', $section['landing_section_id']) }}"/>
                                                @else 

                                                    <x-btn-enable
                                                        url="{{ route('admin.landing.sectionsDisableEnable', $section['landing_section_id']) }}"/>
                                                @endif

                                                @if ($sectionIndex > 0)

                                                    <x-btn-priority-up
                                                        url="{{ route('admin.landing.sectionsPriorityUpDown', $section['landing_section_id']) .'?priority=1' }}"/>
                                                @endif

                                                @if ($sectionIndex < count($sections) - 1)

                                                    <x-btn-priority-down
                                                        url="{{ route('admin.landing.sectionsPriorityUpDown', $section['landing_section_id']) .'?priority=0' }}"/>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection