@extends('layouts.app')

@section('content')

    <div class="row">
        
        <div class="col-lg-12">
            
            <div class="d-block">

                <form method="POST"
                    action="{{ route('admin.landing.sectionsCreateSubmit') }}"
                    accept-charset="UTF-8" class="col-12" enctype='multipart/form-data'>
                    @csrf
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="name" class="control-label required">{{ __('table.SectionName') }}</label>
                                        <input class="form-control" required="required" minlength="2" maxlength="255"
                                            value="{{ $section['name'] ?? '' }}"
                                            name="name" type="text"
                                            id="name">
                                    </div>
                                    <div class="form-group">
                                        <label for="description" class="control-label required">{{ __('table.SectionDescription') }}</label>
                                        <input class="form-control" required="required" minlength="2" maxlength="255"
                                            value="{{ $section['description'] ?? '' }}"
                                            name="description" type="text"
                                            id="description">
                                    </div>
                                    <div class="d-none">
                                        <input class="form-control"
                                            value="{{ $section['landing_section_id'] ?? 0 }}"
                                            name="landing_section_id" type="hidden"
                                            id="landing_section_id">
                                    </div>
                                    <div class="form-group">

                                        <x-btn-update-simple />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection