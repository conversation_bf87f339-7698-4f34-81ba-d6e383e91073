@extends('layouts.app')
@php
    $sortingArray = [
       'setting' =>[
           'name' => __('table.Name'),
           'description' => __('table.Description'),
           'default_value' => __('table.SettingsDefaultValue'),
           'setting_type_id' => __('table.SettingsSettingTypeName'),
           'active' => __('table.Active'),
           'created_at' => __('table.CreatedAt'),
           'created_by' => __('table.CreatedBy'),
           'updated_at' => __('table.UpdatedAt'),
           'updated_by' => __('table.UpdatedBy')
   ]
   ];
@endphp

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="settingsForm" class="form-inline card-body" action="{{ route('admin.settings.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">
                        <div class="col-lg-2">
                            <input name="name" class="form-control w-100" type="text"
                                   placeholder="{{__('table.FilterByName')}}"
                                   value="{{ session($cacheKey . '.name') }}">
                        </div>
                        <div class="col-lg-2">
                            <input name="description" class="form-control w-100" type="text"
                                   placeholder="{{__('table.SettingsFilterDescription')}}"
                                   value="{{ session($cacheKey . '.description') }}">
                        </div>
                        <div class="col-lg-2">
                            <input name="default_value" class="form-control w-100" type="text"
                                   placeholder="{{__('table.SettingsFilterDefaultValue')}}"
                                   value="{{ session($cacheKey . '.default_value') }}">
                        </div>

                        <div class="col-lg-2">
                            <select name="setting_type_id" id="setting_type_id" class="form-control w-100">
                                <option value="">{{ __('table.SettingsFilterSettingType') }}</option>
                                @foreach($settingTypes as $settingType)
                                    <option
                                        @if(session($cacheKey . '.setting_type_id') == $settingType->setting_type_id)
                                        selected
                                        @endif
                                        value="{{ $settingType->setting_type_id }}">{{ $settingType->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2">
                            <x-select-active active="{{ session($cacheKey . '.active') }}"/>
                        </div>

                        <div class="col-lg-2">
                            <input type="text" autocomplete="off" name="created_at" class="form-control"
                                   id="createdAt"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                                   placeholder="{{__('table.FilterByCreatedAt')}}">
                        </div>
                        <div class="col-lg-2">
                            <input type="text" autocomplete="off" name="updated_at" class="form-control mt-3"
                                   id="updatedAt"
                                   value="{{ session($cacheKey . '.updatedAt') }}"
                                   placeholder="{{__('table.FilterByUpdatedAt')}}">
                        </div>
                        <div class="col-lg-12 mt-4">
                            <x-btn-filter/>
                            <div id="btns-panel">
                                <x-btn-create url="{{ route('admin.settings.create') }}" name="{{ __('btn.Create') }}"/>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            @include('admin::settings.sorting-head')
                            </thead>
                            <tbody id="settingsTable">
                            @include('admin::settings.list-table')
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script>
        loadSimpleDataGrid('{{ route('admin.settings.refresh') }}', $("#settingsForm"), $("#settingsTable"));
    </script>
@endpush

