@extends('layouts.test')

@section('content')
<div class="d-flex justify-content-center align-items-center" style="height: 40vh;">
    <div class="text-center">
        <!-- Collage Header -->
        <div class="collage-header mb-4">
            @for ($i = 1; $i <= 8; $i++)
                <img src="{{ asset('images/admin_test/m'.$i.'.jpg') }}" class="collage-img" alt="Image {{ $i }}">
            @endfor
        </div>
        <br/>
        <br/>
        <br/>
        <h1>Quick vote - Gypsy detector</h1>

        @if(isset($error))
            <div class="alert alert-danger">
                {{ $error }}
            </div>
        @else
            <p>Click the button below to start voting.</p>
            <a href="{{ route('admin.test.load-vote') }}" class="btn btn-primary">Continue to Start</a>
        @endif
    </div>
</div>

<!-- Additional CSS (optional) -->
<style>
    .collage-header {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
    }

    .collage-img {
        object-fit: cover;
        height: 120px; /* Set a fixed height for all images */
        width: auto; /* Let the width adjust automatically */
        margin-right: 5px; /* 5px space between images */
        border-radius: 8px;
    }

    /* Optional: Remove margin from the last image to avoid extra space */
    .collage-header .collage-img:last-child {
        margin-right: 0;
    }
</style>
@endsection
