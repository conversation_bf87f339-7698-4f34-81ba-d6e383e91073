@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-body">
            <ul class=" nav nav-tabs mt-1" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="settings-tab" data-toggle="tab" href="#settings" role="tab"
                       aria-controls="settings" aria-selected="true">{{__('menu.Settings')}}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="price-increase-tab" data-toggle="tab" href="#priceIncrease" role="tab"
                       aria-controls="priceIncrease" aria-selected="false">{{__('menu.PriceIncrease')}}</a>
                </li>
                @if(!empty($product))
                    <li class="nav-item">
                        <a class="nav-link" id="history-tab" data-toggle="tab" href="#history" role="tab"
                           aria-controls="history" aria-selected="false">{{__('menu.History')}}</a>
                    </li>
                @endif
            </ul>
            <br>
            <div class="tab-content mb-1 mt-1" id="myTabContent">
                <div class="tab-pane fade show active" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                    @include('product::product.crud')
                </div>
                <div class="tab-pane fade" id="priceIncrease" role="tabpanel" aria-labelledby="price-increase-tab">
                    @include('product::product.price-increase')
                </div>
                @if(!empty($product))
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        @include('product::product.history')
                    </div>
                @endif
            </div>
            <div class="clearfix"></div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $('body').addClass('settings');
        $('.live-search-city').selectpicker();
        const selects = $('#offices');

        let urlMenu = document.location.toString();
        if (urlMenu.match('#')) {
            $('.nav-tabs a[href="#' + urlMenu.split('#')[1] + '"]').tab('show');
        }

        // Change hash for page-reload
        $('.nav-tabs a').on('show.bs.tab', function (e) {

            window.location.hash = e.target.hash;

            if (e.target.hash === '#communication') {
                e.preventDefault();
                location.reload();
            }

            $("html, body").scrollTop(0);
        })


    </script>
@endpush
