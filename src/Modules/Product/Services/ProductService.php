<?php

namespace Modules\Product\Services;

use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductHistory;
use Modules\Common\Models\ProductType;
use Modules\Common\Models\ProductProductSetting;
use Modules\Common\Services\BaseService;
use Modules\Discounts\Services\DiscountService;
use Modules\Head\Services\InterestTermService;
use Modules\Head\Services\PenaltyTermService;
use Modules\Product\Repository\ProductRepository;
use Modules\Product\Repository\ProductSettingRepository;
use Modules\Product\Repository\ProductTypeRepository;

class ProductService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    protected string $productCacheKey = 'all_products';

    /**
     * ProductService constructor.
     */
    public function __construct(
        protected OfficeService            $officeService,
        protected InterestTermService      $interestTermService = new InterestTermService(),
        protected PenaltyTermService       $penaltyTermService = new PenaltyTermService(),
        protected ProductRepository        $productRepository = new ProductRepository(),
        protected ProductTypeRepository    $productTypeRepository = new ProductTypeRepository(),
        protected ProductSettingRepository $productSettingRepository = new ProductSettingRepository(),
        protected ProductSettingsService   $productSettingsService = new ProductSettingsService(),
    )
    {
        parent::__construct();
    }

    public function getProductSettingByKey(Product $product, $settingKey): mixed
    {
        $cacheKey = ProductProductSetting::getCacheKey($product->getKey(), $settingKey);

        $productSetting = Cache::get($cacheKey, function () use ($settingKey, $cacheKey, $product) {
            $row = ProductProductSetting::where('product_id', $product->product_id)
                ->whereRaw(
                    "product_setting_id = (SELECT ps.product_setting_id FROM product_setting ps WHERE ps.name = '" . $settingKey . "')"
                )->first();

            /// put in cache
            Cache::put($cacheKey, $row, Carbon::now()->addHours(24));

            return $row;
        });

        if (!isset($productSetting->value)) {
            return null;
        }

        return $productSetting->value;
    }

    /**
     * @param int $id
     *
     * @return Product
     *
     * @throws ProblemException
     */
    public function getById(int $id): Product
    {
        $product = $this->productRepository->getProductById($id);
        if (!$product || $product->isDeleted()) {
            throw new ProblemException(__('head::productCrud.productNotFound'));
        }

        return $product;
    }

    /**
     * @return Collection
     */
    public function all(): Collection
    {
        $data = \Cache::remember($this->productCacheKey, self::CACHE_TTL, function () {
            return $this->productRepository->getAllProductsSimple();
        });

        return $this->collect(Collection::class, $data);
    }

    /**
     * @param array $data
     *
     * @return Product
     * @throws ProblemException
     * @throws NotFoundException
     */
    public function create(array $data): Product
    {
        $product = $this->productRepository->create($data);

        if (!$product) {
            throw new ProblemException(__('head::productCrud.productCreationFailed'));
        }

        $data['product_id'] = $product->product_id;
        $this->updateRelations($product, $data);

        $this->clearProductsCache();

        return $product;
    }

    public function duplicate(int $productId): Product
    {
        $product = $this->getById($productId);

        $newProduct = $product->replicate();
        $newProduct->name = $product->name . '(copy)';
        $newProduct->trade_name = $product->trade_name . '(copy)';
        $newProduct->created_at = Carbon::now();
        $newProduct->updated_at = Carbon::now();
        $newProduct->save();

        if (empty($newProduct->product_id)) {
            throw new ProblemException(__('head::productCrud.productDuplicationFailed'));
        }



        $ofRel = $product->officeProducts();
        if ($ofRel->count() > 0) {
            foreach ($ofRel as $ofRelRow) {
                $newOfRelRow = $ofRelRow->replicate();
                $newOfRelRow->product_id = $newProduct->product_id;

                $newOfRelRow->save();
            }
        }

        $docTpls = $product->documentTemplates();
        if ($docTpls->count() > 0) {
            foreach ($docTpls as $docTplRow) {
                $newDocTplRow = $docTplRow->replicate();
                $newDocTplRow->product_id = $newProduct->product_id;
                $newDocTplRow->save();
            }
        }

        $prodSets = $product->productSettings();
        if ($prodSets->count() > 0) {
            foreach ($prodSets as $prodSetRow) {
                $newProdSetRow = $prodSetRow->replicate();
                $newProdSetRow->product_id = $newProduct->product_id;
                $newProdSetRow->save();
            }
        }

        $iTerms = $product->interestTerms();
        if ($iTerms->count() > 0) {
            foreach ($iTerms as $iTermsRow) {
                $newITermsRow = $iTermsRow->replicate();
                $newITermsRow->product_id = $newProduct->product_id;
                $newITermsRow->save();
            }
        }

        $pTerms = $product->penaltyTerms();
        if ($pTerms->count() > 0) {
            foreach ($pTerms as $pTermsRow) {
                $newPTermsRow = $pTermsRow->replicate();
                $newPTermsRow->product_id = $newProduct->product_id;
                $newPTermsRow->save();
            }
        }


        $this->clearProductsCache();

        return $product;
    }

    /**
     * @param Product $product
     * @param array $data
     *
     * @return Product
     *
     * @throws ProblemException
     * @throws NotFoundException
     */
    public function edit(Product $product, array $data): Product
    {
        $product = $this->productRepository->edit($product, $data);
        $this->updateRelations($product, $data);

        $this->clearProductsCache();

        return $product;
    }

    /**
     * @throws NotFoundException
     * @throws ProblemException
     */
    protected function updateRelations(Product $product, array $data)
    {
        if (!isset($data['offices'])) {
            $data['offices'] = [];
        }

        $officeChanges = $product->adopt('offices', $data['offices']);
        $this->createHistory(
            $product,
            $officeChanges['attached'] ?? [],
            ProductHistory::TRANSLATION_KEY_PRODUCT_OFFICES_ADDED
        );
        $this->createHistory(
            $product,
            $officeChanges['detached'] ?? [],
            ProductHistory::TRANSLATION_KEY_PRODUCT_OFFICES_REMOVED
        );

        $this->productSettingsService->update($product, $data['product_settings']);

        $this->updateDocumentTemplate(
            $product,
            !empty($data['document_templates']) ? $data['document_templates'] : []
        );
        $this->resolveTerms($data['interest_term_file'] ?? null, $product, 'interest');
        $this->resolveTerms($data['penalty_term_file'] ?? null, $product, 'penalty');
    }

    /**
     * @param Product $product
     */
    public function delete(Product $product)
    {
        $this->productRepository->delete($product);

        $this->clearProductsCache();
    }

    /**
     * @param Product $product
     *
     * @throws ProblemException
     */
    public function enable(Product $product)
    {
        if ($product->isActive()) {
            throw new ProblemException(__('head::productCrud.productEnableForbidden'));
        }

        $this->productRepository->enable($product);
        $this->clearProductsCache();
    }

    /**
     * @param Product $product
     *
     * @throws ProblemException
     */
    public function disable(Product $product)
    {
        if (!$product->isActive()) {
            throw new ProblemException(__('head::productCrud.productDisableForbidden'));
        }

        $this->productRepository->disable($product);
        $this->clearProductsCache();
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(
        int   $limit,
        array $data
    )
    {
        $where = [];
        if (array_key_exists('limit', $data) && $data['limit'] !== null) {
            $limit = $data['limit'];
            unset($data['limit']);
        }

        if (!empty($data['office_id'])) {
            $where[] = ['office_product.office_id', '=', $data['office_id']];
            unset($data['office_id']);
        }

        return $this->productRepository->getAll(
            $limit,
            $this->getJoins($data),
            array_merge($where, $this->getWhereConditions($data)),
            [
                'product.active' => 'DESC',
                'product.product_id' => 'DESC',
            ]
        );
    }

    /**
     * @param int $limit
     * @param Product $product
     *
     * @return mixed
     */
    public function getHistory(
        int     $limit,
        Product $product
    )
    {
        return $this->productRepository->getProductHistory(
            $limit,
            $product,
            [
                'product_history.product_history_id' => 'DESC',
            ]
        );
    }


    /**
     * END PRODUCTS
     */

    /**
     * BEGIN PRODUCT GROUPS
     */

    /**
     * @param int $id
     *
     * @return ProductType
     *
     * @throws ProblemException
     */
    public function getGroupById(int $id): ProductType
    {
        $productGroup = $this->productTypeRepository->getProductGroupById($id);
        if (!$productGroup || $productGroup->isDeleted()) {
            throw new ProblemException(__('head::productGroupCrud.productGroupNotFound'));
        }

        return $productGroup;
    }

    /**
     * @param ProductType $productType
     *
     * @throws ProblemException
     */
    public function disableGroup(ProductType $productType)
    {
        if (!$productType->isActive()) {
            throw new ProblemException(__('head::productGroup.productGroupDisableForbidden'));
        }

        $this->productTypeRepository->disable($productType);
    }

    /**
     * END PRODUCT GROUPS
     */

    /**
     * BEGIN COMMON METHODS
     */

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    private function updateDocumentTemplate(
        Product $product,
        array   $documentTemplates
    ): void
    {
        // Array filter to remove null values
        foreach ($documentTemplates as $key => $documentTemplateId) {
            if ($documentTemplateId == null) {
                $documentTemplateId = 0;
            }

            $this->productRepository->updateOrCreateDocumentTemplate(
                $product,
                $key,
                $documentTemplateId
            );
        }
    }

    /**
     * @throws ProblemException
     */
    private function resolveTerms(?UploadedFile $file, Product $product, $type = 'interest')
    {
        if ($file && $type === 'interest') {
            $this->interestTermService->import($file, $product);
        }

        if ($file && $type === 'penalty') {
            $this->penaltyTermService->import($file, $product);
        }
    }

    /**
     * END COMMON METHODS
     */
    private function clearProductsCache()
    {
        \Cache::forget($this->productCacheKey);
        \Cache::forget(DiscountService::CACHE_KEY_PRODUCTS);
    }


    private function createHistory(
        Product $product,
                $officeChanges,
        string  $translationKey
    )
    {
        $officeNames = $this->officeService->getOfficeNamesByIds($officeChanges);

        if ($officeNames->isEmpty()) {
            return;
        }

        $officeNames = self::naturalLanguageImplode(
            $officeNames->toArray(),
            ', ',
            __('head::clientCard.and')
        );

        $productHistoryData = [
            'product_id' => $product->getKey(),
            'change_type' => $product->wasRecentlyCreated
                ? ProductHistory::TYPE_CREATE
                : ProductHistory::TYPE_EDIT,
            'field' => 'offices',
            'translation_key' => $translationKey,
            'new_value' => $officeNames,
            'old_value' => null,
        ];

        $this->productSettingRepository->createProductHistory($productHistoryData);
    }
}
