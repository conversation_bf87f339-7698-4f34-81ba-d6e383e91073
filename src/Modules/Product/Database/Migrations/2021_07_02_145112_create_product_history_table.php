<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration {
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'product_history',
            function ($table) {
                $table->bigIncrements('product_history_id');

                $table->bigInteger('product_id')->unsigned();
                $table->enum('change_type', ['create', 'edit', 'delete']);
                $table->string('field');
                $table->string('translation_key');
                $table->text('old_value')->nullable();
                $table->text('new_value')->nullable();

                $table->foreign('product_id')->references('product_id')->on('product');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table(
            'product_history',
            function (Blueprint $table) {
                $table->dropForeign('product_history_product_id_foreign');
            }
        );

        Schema::dropIfExists('product_history');
    }
};
