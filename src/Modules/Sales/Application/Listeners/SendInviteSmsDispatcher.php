<?php

namespace Modules\Sales\Application\Listeners;

use Modules\Api\Application\Actions\SendUrlInSmsToClientAction;
use Modules\Common\Enums\InviteUrlActionEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\LoanStatus;
use Modules\Communication\Services\SmsService;
use Modules\Sales\Domain\Events\NewLoanDocsGenerated;

// SMS с линк към страницата за подписване на договор в профила на клиента.
// Изпраща се при връщане на кредит за подпис (промяна на заявка) и създадена заявка по телефона.
class SendInviteSmsDispatcher
{
    public function __construct(
        private SendUrlInSmsToClientAction $action
    ) {}

    public function handle(NewLoanDocsGenerated $event)
    {
        $dbLoan = $event->loan;

        if (!$dbLoan->isOnlineLoan()) {
            return;
        }


        // auto-approve logic, we send custom sms
        $meta = $dbLoan->getMeta('ap_changed_loan_params_sms');
        if (!empty($meta->value)) {

            $data = json_decode($meta->value, true);
            $smsVars = $data['vars'];
            $tplKey = $data['tpl'];

            if (!empty($smsVars) && !empty($tplKey)) {
                app(SmsService::class)->sendByTemplateKeyAndLoan(
                    $tplKey,
                    $dbLoan,
                    $smsVars,
                    false, // $force
                    2, // $delayInSec
                );

                // when we used it once, we delete it
                $meta->forceDelete();
            }

            return;
        }


        // check if loan is created by admin from CRM
        $fromAgent = ($dbLoan->created_by != Administrator::SYSTEM_ADMINISTRATOR_ID);

        $alreadyHasNew = $dbLoan->loanStatusHistory
            ->where('loan_status_id', LoanStatus::NEW_STATUS_ID)
            ->count();
        $loanParamsChanged = ($alreadyHasNew > 1);

        if (!$loanParamsChanged && !$fromAgent) {
            return;
        }

        $this->action->sendSmsLinkForSign(
            $dbLoan,
            InviteUrlActionEnum::signLink,
            [
                'now' => now(),
                'loan_id' => $dbLoan->loan_id,
                'loan_status_id' => $dbLoan->loan_status_id,
                'admin_id' => getAdminId()
            ]
        );

        return;
    }
}
