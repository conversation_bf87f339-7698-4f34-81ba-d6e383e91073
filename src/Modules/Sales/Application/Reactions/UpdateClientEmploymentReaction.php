<?php

namespace Modules\Sales\Application\Reactions;

use Mo<PERSON>les\Head\Http\Commands\UpdateClientEmploymentCommand;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Domain\Entities\Client\Client;

class UpdateClientEmploymentReaction
{
    public function __construct(private Client $client){}

    public function __invoke(UpdateClientEmploymentCommand $command): void
    {
        $this->client->updateEmploymentForExistingClient(
            $command->clientId(),
            $command->employmentDto()
        );
    }
}