<?php

namespace Modules\Sales\Application\Actions;

use Illuminate\Support\Collection;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\SaleTask;
use Modules\Sales\Repositories\SaleTaskRepository;

readonly class ExportSaleTasksAction
{
    public function __construct(private SaleTaskRepository $repo) {}

    public function execute(array $filters = []): Collection
    {
        $rows = new Collection();
        /** @var SaleTask $task */
        foreach ($this->repo->getByFilters($filters) as $task) {
            $row = [
                'Номер' => $task->getKey(),
                'Вид Продажба' => __('sales::saleTaskType.'.$task->saleTaskType->name, ['discount' => $task->discount]),
                'Продукт' => $task->productString(),
                'Сума' => intToFloat($task->loan?->amount_requested ?: $task->amount ),
                'Приод на Кредита' => $task->getProductPeriodLabel(),
                'Клиент' => $task->client?->isNewLabel(),
                'Имена на Клиента' => $task->client?->getFullName(),
                'ЕГН' => $task->client?->pin?:$task->pin,
                'Телефон' => $task->client?->phone?:$task->phone,
                'Дата на Създаване' => $task->created_at->format('Y-m-d H:i:s'),
                'Таймер' => $task->status === TaskStatusEnum::NEW->value ? $task->timer() : '',
                'Статус' => $task->status
            ];

            $rows->push((object)$row);
        }

        return $rows;
    }
}