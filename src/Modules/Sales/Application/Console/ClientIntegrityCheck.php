<?php

namespace Modules\Sales\Application\Console;

use Illuminate\Support\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientRepresentor;
use Modules\Head\Repositories\ClientRepository;
use Symfony\Component\Console\Command\Command;

class ClientIntegrityCheck extends CommonCommand
{
    //php artisan script:client-integrity-check
    protected $name = 'client-integrity-check';
    protected $signature = 'script:client-integrity-check {client_id?}';
    protected $description = 'Check if client data follows business rules';

    public function __construct(
        private readonly ClientRepository $clientRepository
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->startLog();
        $clientId = empty($this->argument('client_id')) ? null : (int) $this->argument('client_id');
        if($clientId){
            $client = $this->clientRepository->getById($clientId);
            if(! $client){
                $this->error('Client with Id:'.$clientId.' was not found');
                return Command::INVALID;
            }
            $this->line('checking client:'.$client->getKey());
            $this->checkRules($client);
            return Command::SUCCESS;
        }

        foreach ($this->clientRepository->getAllClients() as $client){
            $this->line('checking client:'.$client->getKey());
            $this->checkRules($client);
        }
        $this->finishLog();

        return Command::SUCCESS;
    }

    public function checkRules(Client $client): void
    {
        //concatenate all checks that you can think of
        $this->checkAddressRule($client);
        $this->checkPhoneRule($client);
        $this->checkIdCardRule($client);
        $this->checkCompanyRule($client);
        $this->checkBirthdayRule($client);
    }

    public function checkAddressRule(Client $client): void
    {
        foreach (['id_card', 'current'] as $type){
            $clientAddresses = ClientAddress::where(['client_id'=>$client->getKey(), 'type'=>$type, 'active'=>1])->get();
            if(! $clientAddresses->count()){
                $this->warn('ClientAddress of type "'.$type.'" is missing');
                continue;
            }
            if($clientAddresses->count() > 1){
                $this->warn('Multiple active ClientAddresses of type "'.$type.'"');
            }

            if(! $clientAddresses->first()->address){
                $this->warn('ClientAddress of type "'.$type.'" has no address');
            }
        }
    }

    public function checkPhoneRule(Client $client): void
    {
        if(! $client->phone){
            $this->warn('Client has empty phone field');
        }
        if(! $client->clientPhones->count()){
            $this->warn('No client phones');
        }
    }

    public function checkIdCardRule(Client $client): void
    {
        if(! $client->idcard_number){
            $this->warn('Client has empty id_card_number');
        }
        if(! $client->clientIdCards()->count()){
            $this->warn('client has no active id_cards');
        }
        if($client->clientIdCards()->count()>1){
            $this->warn('client has multiple ACTIVE id_cards');
        }
        $expirationDate = $client->clientIdCards?->last()?->valid_date;
        if(! $expirationDate){
            $this->warn('Client Id Card has no expiration date');
        } else if(now()->gt(Carbon::parse($expirationDate))){
            $this->warn('ID card expired on'.$expirationDate);
        }
    }

    public function checkCompanyRule(Client $client): void
    {
        if(! $client->legal_status && ! in_array($client->legal_status, ['individual','company'])){
            $this->warn('Client has incorrect legal_status');
        }
        if($client->legal_status === 'individual'){
            return;
        }
        if(! $client->clientRepresentors()->count()){
            $this->warn('client is missing a representative');
            return;
        }
        /** @var ClientRepresentor $rep */
        foreach ($client->clientRepresentors() as $rep){
            if(! $client->isValidPin($rep->representor?->pin)){
                $this->warn('Representative has incorrect pin:'.$rep->representor->pin);
            }
        }
    }

    public function checkBirthdayRule(Client $client): void
    {
        if(! $client->birthday){
            $this->warn('Client has empty birthday field');
        } else {
            $age = Carbon::parse($client->birth_date)->age;
            if($age < 18){
                $this->warn('Client has age of '.$age.' and should not have been able to register');
            }
            if($age >= 70){
                $this->warn('Client has age of '.$age.' and should be deactivated');
            }
        }
    }
}
