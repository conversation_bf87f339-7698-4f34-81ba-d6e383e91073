<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'sale_task_history',
            function ($table) {
                $table->bigIncrements('sale_task_history_id');
                $table->integer('sale_task_type_id')->unsigned()->index();
                $table->integer('client_id')->unsigned()->index()->nullable();
                $table->integer('office_id')->unsigned()->index();
                $table->decimal('discount', 11, 2)->nullable();
                $table->string('discount_from')->nullable();
                $table->integer('loan_id')->unsigned()->index()->nullable();
                $table->integer('tmp_request_id')->unsigned()->nullable();
                $table->string('pin', 12);
                $table->string('client_full_name');
                $table->string('phone');
                $table->string('email');
                $table->string('details')->nullable();
                $table->enum('status', ['new', 'processing', 'done']);
                $table->datetime('last_status_update_date')->nullable();
                $table->dateTime('processed_at')->nullable();
                $table->integer('processed_by')->unsigned()->index()->nullable();
                $table->dateTime('show_after')->nullable();

                $table->integer('parent_task_id')->unsigned()->index()->nullable();
                $table->foreign('sale_task_type_id')->references('sale_task_type_id')->on('sale_task_type');
                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('office_id')->references('office_id')->on('office');
                $table->foreign('loan_id')->references('loan_id')->on('loan');
                $table->foreign('processed_by')->references('administrator_id')->on('administrator');

                $table->tableCrudFieldsHistory(false);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'sale_task_history',
            function (Blueprint $table) {
                $table->dropForeign('sale_task_history_sale_task_type_id_foreign');
                $table->dropForeign('sale_task_history_client_id_foreign');
                $table->dropForeign('sale_task_history_office_id_foreign');
                $table->dropForeign('sale_task_history_loan_id_foreign');
                $table->dropForeign('sale_task_history_processed_by_foreign');
            }
        );

        Schema::dropIfExists('sale_task_history');
    }
};
