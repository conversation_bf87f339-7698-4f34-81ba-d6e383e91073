<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_params_history', function (Blueprint $table) {
            $table->dropColumn('direction');
            $table->integer('discount_percent')->nullable();
            $table->renameColumn('tax_id', 'product_id');
            $table->renameColumn('amount_approved', 'amount_requested');
            $table->renameColumn('period_approved', 'period_requested');
            $table->dropForeign('loan_params_history_tax_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_params_history', function (Blueprint $table) {
            $table->dropColumn('discount_percent');
            $table->enum('direction', ['out', 'in'])->nullable();
            $table->renameColumn('product_id', 'tax_id');
            $table->renameColumn('amount_requested', 'amount_approved');
            $table->renameColumn('period_requested', 'period_approved');
        });
    }
};
