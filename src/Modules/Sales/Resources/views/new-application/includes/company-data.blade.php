<x-card>
    <x-slot:title>
        <i class="fa-duotone fa-user"></i>&nbsp;
        {{__('table.CompanyData')}}
    </x-slot:title>

    {!! form_row($newLoanForm->pin) !!}

    <div class="form-group">
        <button type="button" class="btn btn-sm btn-primary" name="searchByPin">
            <i class="fa fa-loader"></i>&nbsp;
            {{__('btn.SearchByEik')}}
        </button>
    </div>
    <!-- End ./form-group -->

    {!! form_row($newLoanForm->idcard_number) !!}

    {!! form_row($newLoanForm->first_name) !!}

    {!! form_row($newLoanForm->first_name_latin) !!}
</x-card>

@push('scripts')
    <script>
        let $newApplicationReqUrl = '{{route('sales.newApplication')}}';
        $(document).ready(function () {
            $(document).on('click', 'button[name="searchByPin"]', function () {
                let $pinInput = $('input[name="client_idcard[pin]"]');

                $pinInput.parsley().validate();
                if (!$pinInput.parsley().isValid()) {
                    return false;
                }

                location.replace($newApplicationReqUrl + '/?pin=' + $pinInput.val());
            });
        });
    </script>
@endpush
