<div class="card-body">
    <input type="hidden" name="loan[loan_id]" value="{{ $loan->loan_id ?? '' }}">
    <input type="hidden" name="sae_task[sale_task_id]"
           value="{{ $saleTask->sale_task_id ?? '' }}">
    <div class="container-fluid">
        <div class="row">
            @foreach($saleDecisions as $saleDecision)
                <button
                    class="btn btn-primary primary-ch-btn btn-circle-blue p-2 w-lg-30 m-1 min-h-100 saleDecision form-inline"
                    value="{{$saleDecision->sale_decision_id}}"
                    name="sale_decision_id"
                >
                    {{( __('sales::saleDecision.' . $saleDecision->name) )}}
                </button>
            @endforeach
        </div>
    </div>
</div>
@push('scripts')
    <script>
        $(document).ready(function (){
            const TYPE_OTHER = '{{ \Modules\Common\Models\SaleDecision::SALE_DECISION_ID_OTHER }}';
            const TYPE_CALL_LATER = '{{ \Modules\Common\Models\SaleDecision::SALE_DECISION_ID_RECALL }}';
            const MODAL_MAPPER = {
                6: "#addCommentModal",
                3: "#callLaterSaleDecisionModal"
            };
            const $saleDecisionRoute = '{{ route('head.clientCard.completeSaleTask', ['saleTask' => !empty($saleTask) ? $saleTask->getKey() : 0]) }}';

            const $callLaterDateInput = $('#callLaterDate')
                .val(moment().format('DD.MM.YYYY'));
            const $minutesOnlyInput = $('#callLaterMinutesOnly');

            $minutesOnlyInput.focus(function () {
                $('#callLaterForm input.clear')
                    .removeAttr('required min max')
                    .val('');
            });

            $('#callLaterForm input.clear').focus(function () {
                $minutesOnlyInput
                    .removeAttr('required min max')
                    .val('');
            });

            $('#callLaterDiv').click(function () {
                $minutesOnlyInput
                    .removeAttr('required min max')
                    .val('');
            });

            $('#callLaterDiv').daterangepicker({
                minDate: moment(),
                maxDate: moment().add(1, 'days'),
                autoUpdateInput: false,
                autoApply: true,
                singleDatePicker: true,
                locale: {
                    format: 'DD.MM.YYYY',
                }
            });

            $('#callLaterDiv').on('apply.daterangepicker', function (ev, picker) {
                $callLaterDateInput.val(picker.startDate.format('DD.MM.YYYY'));
            });

            $('.saleDecision').click(function (event) {
                event.preventDefault()
                let value = $(this).val();
                if (value == TYPE_OTHER || value == TYPE_CALL_LATER) {

                    $(MODAL_MAPPER[value]).modal('show');

                    return;
                }

                $('<form/>', { action: $saleDecisionRoute, method: 'POST' }).append(
                    $('<input>', {type: 'hidden', name: 'sale_decision_id', value: value}),
                    $('<input>', {type: 'hidden', name: '_token', value: '{{ csrf_token() }}' }),
                ).appendTo('body').submit();
            });
        });
    </script>
@endpush
