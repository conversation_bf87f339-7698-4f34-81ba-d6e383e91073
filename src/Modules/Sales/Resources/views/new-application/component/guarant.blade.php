<div class="card-body" id="addNewGuarant">
    <div class="sale_block_titles">
        <h4>{{ __('table.AddingGuarant') }}</h4>
        <button class="btn btn-primary btn-circle-small btn-circle-blue d-flex
        fa fa-plus justify-content-center" type="button"
                id="addNewGuarantButton" role="button">
        </button>
    </div>
    <div id="cstm-success-guarant"></div>
    <div id="accordion" class="custom-accordion">
        @php
            $nameGuarant = old('guarant.first_name') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->first_name : '');
            $middleName = old('guarant.middle_name') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->middle_name : '');
            $lastName = old('guarant.last_name') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->last_name : '');
            $phone = old('guarant.phone') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->phone : '');
            $pin = old('guarant.pin') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->pin : '');
            $idCardNumber = old('guarant.idcard_number') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->idcard_number : '');
            $address = old('guarant.address') ?? (!empty($lastClientGuarant) ? $lastClientGuarant->address : '');
        @endphp
        <x-new-application-guarant-only-forms
            :cities="$cities"
            citiesName="guarant_city_id[city_id]"
            citiesId="guarant_city_id_id"
            selectedCityId=" "
            nameGuarant="{{ $nameGuarant }}"
            middleName="{{ $middleName }}"
            lastName="{{ $lastName }}"
            phone="{{ $phone }}"
            pin="{{ $pin }}"
            seqNumGuarant="1"
            idCardNumber="{{ $idCardNumber }}"
            address="{{ $address }}"
            issueDate=" "
            :idCardIssuedTypes="$idCardIssuedTypes"
        />
        <input type="hidden" id="guarantIdClientId" value="">
        <input type="hidden" id="guarantId" class="guarantId" value="">
        <div id="toggle-guarant-additional-input-container2"></div>
    </div>
</div>
