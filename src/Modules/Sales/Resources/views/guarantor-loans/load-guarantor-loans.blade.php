@php
    /**
* @var \Modules\Common\Models\Loan $guarantorLoan
 */
@endphp
<x-table>
    @foreach($loans as $guarantorPin => $guarantorLoans)
        @if(!empty($guarantorLoans))
            <tr>
                <th>{{__('table.LoanId')}}</th>
                <th>{{__('table.CreatedAt')}}</th>
                <th>{{__('table.AmountApproved')}}</th>
                <th>{{__('table.Product')}}</th>
                <th>{{__('table.OverdueDays')}}</th>
                <th>{{__('table.OverdueAmount')}}</th>
            </tr>

            <tr>
                <th colspan="6" class="text-danger">{{$guarantorPin}}</th>
            </tr>
            @foreach($guarantorLoans as $guarantorLoan)
                <tr>
                    <td>{{$guarantorLoan->getKey()}}</td>
                    <td>{{$guarantorLoan->created_at->format('d.m.Y')}}</td>
                    <td>{{intToFloat($guarantorLoan->amount_approved)}}</td>
                    <td>{{$guarantorLoan->product->trade_name}}</td>
                    <td>{{$guarantorLoan->getCartonDb()['current_overdue_days']}}</td>
                    <td>{{intToFloat($guarantorLoan->getCartonDb()['current_overdue_amount'])}}</td>
                </tr>
            @endforeach
        @endif
    @endforeach
</x-table>