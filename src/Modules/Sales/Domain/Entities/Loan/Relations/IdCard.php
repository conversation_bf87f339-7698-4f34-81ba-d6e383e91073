<?php

namespace Modules\Sales\Domain\Entities\Loan\Relations;

use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\LoanIdCard as DbModel;
use Modules\Head\Repositories\Loan\LoanIdCardRepository as Repo;
use Modules\Sales\Domain\Exceptions\LoanRelations\IdCardNotSaved;

class IdCard implements DomainModelInterface
{
    private LoanInterface $loan;

    public function __construct(private DbModel $dbModel, private Repo $repo){}

    public function build(LoanInterface $loan, ClientIdCard $dbClientIdCard): self
    {
        return $this->setLoan($loan)->setDbModel($dbClientIdCard)->save();
    }

    public function buildFromExisting(LoanInterface $loan, DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this->setLoan($loan);
    }

    public function setLoan(LoanInterface $loan): self
    {
        $this->loan = $loan;
        $this->dbModel->loan_id = $loan->dbModel()->getKey();
        return $this;
    }

    private function setDbModel(ClientIdCard $dbClientIdCard): self
    {
        $existing = $this->loan->dbModel()->bankAccount()->first();
        if($existing && $existing->equals('client_idcard_id', $dbClientIdCard->client_idcard_id)){
            $this->dbModel = $existing;
        }
        $this->dbModel->client_idcard_id = $dbClientIdCard->client_idcard_id;
        return $this;
    }

    private function save(): self
    {
        if(! $this->repo->save($this->dbModel)){
            throw new IdCardNotSaved();
        }
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
