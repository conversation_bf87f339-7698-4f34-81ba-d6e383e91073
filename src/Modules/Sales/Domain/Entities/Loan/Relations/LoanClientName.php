<?php

namespace Modules\Sales\Domain\Entities\Loan\Relations;

use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\ClientName;
use Modules\Common\Models\LoanClientName as DbModel;
use Modules\Head\Repositories\Loan\LoanClientNameRepository as Repo;
use Modules\Sales\Domain\Exceptions\LoanRelations\ClientNameNotSaved;

class LoanClientName implements DomainModelInterface
{
    private LoanInterface $loan;

    public function __construct(private DbModel $dbModel, private Repo $repo){}

    public function build(LoanInterface $loan, ClientName $dbClientName): self
    {
        return $this->setLoan($loan)->setDbModel($dbClientName)->save();
    }

    public function buildFromExisting(LoanInterface $loan, DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this->setLoan($loan);
    }

    public function setLoan(LoanInterface $loan): self
    {
        $this->loan = $loan;
        $this->dbModel->loan_id = $loan->dbModel()->getKey();
        return $this;
    }

    private function setDbModel(ClientName $dbClientName): self
    {
        $existing = $this->loan->dbModel()->bankAccount()->first();
        if($existing && $existing->equals('client_name_id', $dbClientName->client_name_id)){
            $this->dbModel = $existing;
        }
        $this->dbModel->client_name_id = $dbClientName->client_name_id;
        return $this;
    }

    private function save(): self
    {
        if(! $this->repo->save($this->dbModel)){
            throw new ClientNameNotSaved($this->dbModel->client_name_id);
        }
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
