<?php

namespace Modules\Sales\Domain\Entities\Bank;

use Modules\Common\Models\ClientBankAccount;
use Modules\Common\Models\ClientBankAccount as DbModel;
use Modules\Common\Models\Client as ClientModel;
use Modules\Head\Repositories\ClientBankAccountRepository;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Exceptions\ClientRelations\ClientBankNotSaved;
use Modules\Sales\Domain\Exceptions\ClientRelations\IbanDoesntMatchBank;
use Modules\Sales\Http\Dto\BankDto;

class ClientBank
{
    private ?BankDto $dto = null;

    public function __construct(
        private ClientBankAccountRepository $repo,
        private Bank $bank,
        private DbModel $dbModel
    ){}

    public function build(Client $client, ?BankDto $dto): self
    {
        return $this->setClient($client->dbModel())
            ->setDto($dto)
            ->setDbModel()
            ->setBank()
            ->setBic()
            ->setIban()
            ->save();
    }

    public function buildFromRealClient(ClientModel $client, ?BankDto $dto): self
    {
        return $this->setClient($client)
            ->setDto($dto)
            ->setDbModel()
            ->setBank()
            ->setBic()
            ->setIban()
            ->save();
    }

    private function setClient(ClientModel $client): self
    {
        $this->dbModel->client_id = $client->getKey();
        return $this;
    }

    private function setDto(?BankDto $dto): self
    {
        $this->dto = $dto;
        return $this;
    }

    private function setDbModel(): self
    {
        $all = $this->repo->getAllByClientId($this->dbModel->client_id);
        /** @var ClientBankAccount $bankAccount */
        foreach ($all as $bankAccount){
            //Loading existing account
            if (! $this->dto && $bankAccount->last)
            {
                $this->dbModel = $bankAccount;

                return $this;
            }
        }
        foreach ($all as $bankAccount){
            //Loading account that has same details as dto even if not last
            if ($this->dto?->iban === $bankAccount->iban && $this->dto?->bank_id === $bankAccount->bank_id)
            {
                $this->dbModel = $bankAccount;

                return $this;
            }
        }
        foreach ($all as $bankAccount){
            //Loading by IBAN only
            if ($this->dto?->iban === $bankAccount->iban){
                $this->dbModel = $bankAccount;

                return $this;
            }
        }
        //failed to find any existing matching accounts
        return $this;
    }

    private function setBank(): self
    {
        $existing = $this->dbModel->exists ? $this->dbModel : null;
        $this->bank->build($this->dto, $existing);
        $this->dbModel->bank_id = $this->bank->getKey();
        return $this;
    }

    private function setBic(): self
    {
        if($this->bank->dbModel()->exists){
            $this->dbModel->bic = $this->bank->dbModel()->bic;

            return $this;
        }
        if($this->dto?->bic){
            $this->dbModel->bic = $this->dto->bic;

            return $this;
        }

        if($this->dto?->iban){
            $this->dbModel->bic = substr($this->dto->iban, 4, 4);
        }

        return $this;
    }

    private function setIban(): self
    {
        if(! $this->dto?->iban){
            return $this;
        }
//        /** @var ClientBankAccount $account */
//        foreach ($this->repo->getByIBAN($this->dto->iban) as $account){
//            if($account->client_id !== $this->client->getKey()){
//                throw new \Exception('Iban belongs to different user');
//            }
//        }
        $ibanBic = substr($this->dto->iban, 4, 4);

        if($this->dbModel->bic && !str_contains($this->dbModel->bic, $ibanBic)){
            throw new IbanDoesntMatchBank($this->dto->iban, $this->dbModel->bic);
        }
        $this->dbModel->iban = $this->dto->iban;
        return $this;
    }

    private function save(): self
    {
        if(! $this->repo->save($this->dbModel)){
            throw new ClientBankNotSaved();
        }
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function bank(): Bank
    {
        return $this->bank;
    }
}
