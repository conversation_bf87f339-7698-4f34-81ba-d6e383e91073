<?php

namespace Modules\Sales\Domain\Entities\Representative;

use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\ClientRepresentor as DbModel;
use Modules\Head\Repositories\ClientRepresentorRepository;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Exceptions\ClientRelations\ClientRepresentativeNotSaved;
use Modules\Sales\Http\Dto\RepresentativeDto;

class ClientRepresentative implements DomainModelInterface
{
    private Client $client;

    public function __construct(
        private ClientRepresentorRepository $repo,
        private Representative $representative,
        private DbModel $dbModel
    ){}

    public function build(Client $client, ?RepresentativeDto $dto): self
    {
        return $this->setClient($client)
            ->setRepresentative($dto)
            ->setDbModel()
            ->save();
    }

    private function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    private function setRepresentative(?RepresentativeDto $representativeDto): self
    {
        $this->representative->build($this->client, $representativeDto);
        return $this;
    }

    private function setDbModel(): self
    {
        $existing = $this->repo->getLastByClient($this->client->dbModel());
        if($existing && $existing->representor_id === $this->representative->dbModel()->getKey()){
            $this->dbModel = $existing;
            return $this;
        }
        $this->dbModel->client_id = $this->client->dbModel()->getKey();
        $this->dbModel->representor_id = $this->representative->dbModel()->getKey();
        $this->dbModel->active = 1;
        return $this;
    }

    private function save(): self
    {
        if($this->dbModel->isDirty() && ! $this->repo->save($this->dbModel)){
            throw new ClientRepresentativeNotSaved();
        }
        return $this;
    }

    public function dbModel(): DbModel|BaseModel
    {
        return $this->dbModel;
    }

    public function client(): Client
    {
        return $this->client;
    }

    public function representative(): Representative
    {
        return $this->representative;
    }
}
