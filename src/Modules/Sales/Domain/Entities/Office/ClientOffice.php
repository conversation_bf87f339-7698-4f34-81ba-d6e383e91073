<?php

namespace Modules\Sales\Domain\Entities\Office;

use Modules\Common\Domain\LoanInterface;
use Modules\Head\Repositories\ClientOfficeRepository as Repo;
use Modules\Common\Models\ClientOffice as DbModel;
use Modules\Sales\Domain\Exceptions\LoanRelations\ClientOfficeNotSaved;

class ClientOffice
{
    private LoanInterface $loan;

    public function __construct(private DbModel $dbModel, private Repo $repo){}

    public function build(LoanInterface $loan): self
    {
        return $this->setLoan($loan)
            ->setDbModel()
            ->setData()
            ->save();
    }

    private function setLoan(LoanInterface $loan): self
    {
        $this->loan = $loan;
        return $this;
    }

    private function setDbModel()
    {
        $existing = $this->repo->getByLoanId($this->loan->dbModel()->getKey());
        if($existing){
            $this->dbModel = $existing;
        }
        return $this;
    }

    private function setData(): self
    {
        $l = $this->loan->dbModel();
        $this->dbModel->setAttribute('client_id', $l->client_id)
            ->setAttribute('loan_id', $l->loan_id)
            ->setAttribute('office_id', $l->office_id);
        return $this;
    }

    private function save(): self
    {
        if(! $this->repo->save($this->dbModel)){
            throw new ClientOfficeNotSaved();
        }
        return $this;
    }
}
