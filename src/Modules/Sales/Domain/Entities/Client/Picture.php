<?php

namespace Modules\Sales\Domain\Entities\Client;

use Modules\Common\Models\ClientPicture as DbModel;
use Modules\Head\Repositories\ClientPictureRepository;
use Modules\Sales\Domain\Exceptions\ClientRelations\PictureNotSaved;
use Modules\Sales\Http\Dto\PictureDto as Dto;

class Picture
{
    private Client $client;

    public function __construct(
        private ?DbModel $dbModel,
        private ClientPictureRepository $repo
    ){}

    public function build(Client $client, ?Dto $dto): self
    {
        return $this->setClient($client)
            ->setDbModel($dto);
    }

    private function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    private function setDbModel(?Dto $dto): self
    {
        //by this point I have no idea if we need to create new picture or update existing
        $dbClient = $this->client->dbModel();
        if(! $dto){
            /// тут мъ берем по клиенту
            /// по умолчанию параметръ правельнъе нет смъсла тут подавать их опять
            /// string $type = ClientPicture::TYPE_MVR,
            //  string $source = ClientPicture::SOURCE_MVR_PICT
            $this->dbModel = $this->repo->getLastByClient($dbClient);

            return $this;
        }

        if($dto->pin) {
            $this->repo->updateClientIdByPin($dto?->pin, $dbClient->getKey());
        }

        $this->dbModel = $this->repo->actualizeMvrPictures($dto, $dbClient->getKey());

        return $this;
    }

    private function save(): self
    {
        if($this->dbModel && ! $this->repo->save($this->dbModel)){
            throw new PictureNotSaved();
        }
        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
