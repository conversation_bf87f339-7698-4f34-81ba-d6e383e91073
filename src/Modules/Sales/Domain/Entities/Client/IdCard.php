<?php

namespace Modules\Sales\Domain\Entities\Client;

use Illuminate\Support\Carbon;
use Modules\Common\Models\ClientIdCard as DbModel;
use Modules\Common\Models\MvrReport;
use Modules\Head\Repositories\ClientIdCardRepository;
use Modules\Sales\Domain\Exceptions\ClientRelations\IdCardNotSaved;
use Modules\Sales\Domain\Exceptions\ClientRelations\NewIdCardBuiltWithoutDto;
use Modules\Sales\Domain\Exceptions\IdCardExpired;
use Modules\Sales\Domain\Exceptions\IdCardContainsNoExpirationDate;
use Modules\Sales\Http\Dto\IdCardDto as Dto;

class IdCard
{
    private Client $client;
    private ?MvrReport $mvrReport = null;
    private ?Dto $dto = null;

    public function __construct(
        private DbModel $dbModel,
        private ClientIdCardRepository $repo
    ){}

    public function build(Client $client, ?Dto $dto): self
    {
        return $this->setClient($client)
            ->setMvrReport()
            ->setDto($dto)
            ->setDbModel()
            ->setSex()
            ->save();
    }

    public function checkExpirationDate(?Dto $idCardDto = null): void
    {
        $expirationDate = $idCardDto?->valid_date;
        $expirationDate = $expirationDate
            ? Carbon::parse($expirationDate)
            : $this->mvrReport?->getData()?->validDate;
        if(! $expirationDate){
            throw new IdCardContainsNoExpirationDate();
        }

        if(now()->startOfDay()->gt($expirationDate)){
            throw new IdCardExpired($expirationDate);
        }
    }

    private function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    private function setMvrReport(): self
    {
        $this->mvrReport = $this->client->mvrReport();
        return $this;
    }

    private function setDto(?Dto $dto): self
    {
//        if(! $dto && $this->mvrReport){
//            $data = $this->mvrReport->getData();
//            $array = [
//                'pin' => $data->pin,
//                'idcard_number' => $data->idCardNumber,
//                'image' => $data->appearancePicture,
//                'idcard_issued_id' => $this->mvrReport->getIssuerId(),
//                'issue_date' => $this->mvrReport->
//                'valid_date'
//                'city_id'
//                'address'
//                'post_code'
//            ];
//            $dto = Dto::from($array);
//        }
        if($dto) {
            $this->dto = $dto;
        }

        return $this;
    }

    private function setDbModel(): self
    {
        $dbClient = $this->client->dbModel();
        $existing = $this->repo->getLastFromClient($dbClient);
        if($this->importantFieldsUnchanged($existing)){
            $this->dbModel = $existing;
            return $this;
        }

        $this->dbModel->setRelation(DbModel::class, $dbClient);
        $this->dbModel->client_id = $dbClient->getKey();
        $this->dbModel->pin = $this->dto->pin;
        $this->dbModel->idcard_number = $this->dto->idcard_number;
        $this->dbModel->idcard_issued_id = $this->dto->idcard_issued_id;
        $this->dbModel->issue_date = $this->dto->issue_date ? Carbon::parse($this->dto->issue_date)->format('Y-m-d') : null;
        $this->dbModel->valid_date = $this->dto->valid_date ? Carbon::parse($this->dto->valid_date)->format('Y-m-d') : null;
        $this->dbModel->city_id = $this->dto->city_id;
        $this->dbModel->address = $this->dto->address;
        $this->dbModel->post_code = $this->dto->post_code;
        $this->dbModel->lifetime_idcard = $this->dto->lifetime_idcard;

        return $this;
    }

    public function setSex(): self
    {
        if($this->dto && $this->dto->sex){
            $this->dbModel->sex = $this->dto->sex;

            return $this;
        }

        $this->dbModel->sex = $this->mvrReport?->getData()?->genderLatin === 'Man' ? 'male' : 'female';

        return $this;
    }

    private function save(): self
    {
        if($this->dbModel->isDirty() && ! $this->repo->save($this->dbModel)){
            throw new IdCardNotSaved();
        }
        return $this;
    }

    private function importantFieldsUnchanged(?DbModel $dbModel): bool
    {
        if($dbModel && ! $this->dto){
            return true;
        }
        if(! $dbModel && $this->dto){
            return false;
        }
        if($dbModel && $this->dto) {
            return $dbModel->pin === $this->dto->pin
                && $dbModel->idcard_number === $this->dto->idcard_number
                && $dbModel->idcard_issued_id === $this->dto->idcard_issued_id
                && Carbon::parse($dbModel->issue_date)->eq(Carbon::parse($this->dto->issue_date))
                && Carbon::parse($dbModel->valid_date)->eq(Carbon::parse( $this->dto->valid_date))
                && $dbModel->city_id === $this->dto->city_id
                && $dbModel->post_code === $this->dto->post_code
                && $dbModel->address === $this->dto->address;
        }

        throw new NewIdCardBuiltWithoutDto();
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
