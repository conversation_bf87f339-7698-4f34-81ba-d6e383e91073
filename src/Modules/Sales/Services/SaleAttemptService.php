<?php

namespace Modules\Sales\Services;

use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Services\BaseService;
use Modules\Sales\Repositories\SaleAttemptRepository;

class SaleAttemptService extends BaseService
{
    public function __construct(
        private readonly SaleAttemptRepository $saleAttemptRepository = new SaleAttemptRepository(),
        private readonly SettingRepository $settingRepository = new SettingRepository(),
    ) {
        parent::__construct();
    }

    public function getPreviousSaleAttempt(int $saleTaskId): ?SaleAttempt
    {
        $conditions = [
            'sale_task_id' => $saleTaskId,
            'last' => 1,
            'active' => 1,
            'deleted' => 0,
        ];

        return $this->saleAttemptRepository->getByCriteria($conditions);
    }

    public function isLastDeferred(int $skipCounter): bool
    {
        $maxAllowedPostponements = $this
            ->settingRepository
            ->getSetting(SettingsEnum::max_allowed_sale_postponements_sales)
            ->default_value;

        return $skipCounter > (int) $maxAllowedPostponements;
    }

    /**
     * @deprecated
     */
    public function disapproveTooManyPostponements(
        array $newAttemptData
    ): SaleAttempt {

        $newAttemptData['details'] = 'Sale task is disapproved. Too many postponements.';

        return $this->saleAttemptRepository->create($newAttemptData);
    }
}
