<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Guarantor;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\Payday30LoanSeeder;
use Modules\Common\Models\GuarantAddress;
use Modules\Common\Models\GuarantIdCard;
use Modules\Common\Models\GuarantPicture;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanGuarantActual;
use Modules\Common\Models\LoanGuarantHistory;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Guarantor\LoanGuarantors as Sut;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\NewLoan;
use Modules\Sales\Http\Dto\GuarantorDto;
use Tests\TestCase;

class LoanGuarantorsRepoTest extends TestCase
{
    use DatabaseTransactions;

    private GuarantorDto $clientDto;
    private NewLoan $loan;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(Payday30LoanSeeder::class);
        $this->loan = LoanForUpdate::loadSelf(1);
        $this->dtos = [
            GuarantorDto::getFrom([
                'guarant_type_id' => 1,
                'pin' => '6609254591',
                'phone' => '0897778899',
                'idcard_number' => '1111111111',
                'first_name' => 'kolia',
                'middle_name' => 'nikolevich',
                'last_name' => 'putin',
                'idcard_issued_id' => 2,
                'address' => 'ul. rozi 2',
                'seq_num' => 1,
                'currentAddress'=>[
                    'address'=>'guarantor address',
                    'city_id'=>1,
                    'post_code'=>'1111'
                ]
            ]),
            GuarantorDto::getFrom([
                'guarant_type_id' => 1,
                'pin' => '6609254592',
                'phone' => '0897778898',
                'idcard_number' => '1111111112',
                'first_name' => 'vasya',
                'middle_name' => 'petrovich',
                'last_name' => 'bobin',
                'idcard_issued_id' => 2,
                'address' => 'ul. rozi 2',
                'seq_num' => 2,
                'currentAddress'=>[
                    'address'=>'guarantor address2',
                    'city_id'=>1,
                    'post_code'=>'2222'
                ]
            ])
        ];
    }

    public function testSettingTwoNewContacts()
    {
        $sut = app(Sut::class);
        $sut->build($this->loan, $this->dtos);
        $this->assertCount(2, LoanGuarantActual::where(['loan_id'=>1])->get());
        $this->assertEquals(1, $sut->first()->dbModel()->loan_id);
    }


    public function testNotChangingExisting()
    {
        DB::table('guarant')->insert([
            [
                'guarant_id'=>1,
                'pin' => $this->dtos[0]->pin,
                'phone' => $this->dtos[0]->phone,
                'idcard_number' => $this->dtos[0]->idcard_number,
                'first_name' => $this->dtos[0]->first_name,
                'middle_name' => $this->dtos[0]->middle_name,
                'last_name' => $this->dtos[0]->last_name,
                'idcard_issued_id' => $this->dtos[0]->idcard_issued_id,
                'address' => $this->dtos[0]->address,
            ],
            [
                'guarant_id'=>2,
                'pin' => $this->dtos[1]->pin,
                'phone' => $this->dtos[1]->phone,
                'idcard_number' => $this->dtos[1]->idcard_number,
                'first_name' => $this->dtos[1]->first_name,
                'middle_name' => $this->dtos[1]->middle_name,
                'last_name' => $this->dtos[1]->last_name,
                'idcard_issued_id' => $this->dtos[1]->idcard_issued_id,
                'address' => $this->dtos[1]->address,
            ]
        ]);
        DB::table('loan_guarant_actual')->insert([
            ['loan_guarant_actual_id'=>1, 'client_id'=>1, 'loan_id'=>1, 'guarant_id'=>1,'seq_num'=>1,'guarant_type_id'=>1],
            ['loan_guarant_actual_id'=>2, 'client_id'=>1, 'loan_id'=>1, 'guarant_id'=>2,'seq_num'=>2,'guarant_type_id'=>1],
        ]);

        /** @var Sut $sut */
        $sut = app(Sut::class);
        $sut->build($this->loan, $this->dtos);
        $this->assertCount(2, LoanGuarantActual::where(['loan_id'=>1])->get());
        $this->assertCount(0, LoanGuarantHistory::where(['loan_id'=>1])->get());
        $this->assertCount(2, GuarantAddress::all());
        $this->assertCount(2, GuarantIdCard::all());
    }
}