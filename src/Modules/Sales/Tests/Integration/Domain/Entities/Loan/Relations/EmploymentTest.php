<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Loan\Relations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\Payday30LoanSeeder;
use Modules\Common\Models\LoanEmployer as DbModel;
use Modules\Head\Repositories\Loan\LoanEmployerRepository as Repo;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\Relations\Employment as Sut;
use Tests\TestCase;

class EmploymentTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(Payday30LoanSeeder::class);
    }

    public function testBuildingOne()
    {
        $client = Client::selfLoad(1, true);
        $dbClientEmployment = $client->relations()->employment()->dbModel();
        $sut = new Sut(new DbModel(), new Repo());
        $sut->build(LoanForUpdate::loadSelf(1), $dbClientEmployment);
        $this->assertEquals($dbClientEmployment->client_employer_id, $sut->dbModel()->client_employer_id);
    }
}