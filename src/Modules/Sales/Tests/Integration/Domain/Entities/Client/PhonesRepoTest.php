<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Client;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Providers\TestDataProvider;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Client\Phones as Sut;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\PhoneDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class PhonesRepoTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private array $existingPhoneData = ["**********","**********"];

    public function setUp(): void
    {
        parent::setUp();
        $this->clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
    }

    public function testSettingTwoNewPhones()
    {
        $this->seed(ClientSeeder::class);
        $dtos = $this->clientDto->relationDto->phoneDtoArr;
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dtos);
        $this->assertCount(2, $sut);
    }


    public function testSkippingExistingWhenNothingChanged()
    {
        $this->seed(ClientRelationsSeeder::class);
        $dtos = $this->clientDto->relationDto->phoneDtoArr;
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dtos);
        $this->assertCount(2, $sut);
    }

    public function testAddingTwoNewPhonesToExisting()
    {
        $this->seed(ClientRelationsSeeder::class);
        $dtos = [new PhoneDto('0896667786',1), new PhoneDto('0896667787',2)];
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dtos);
        $this->assertCount(4, ClientPhone::all());
        $this->assertCount(2, ClientPhone::where(['last'=>1])->get());
    }

    public function testReorderingPhones()
    {
        $this->seed(ClientRelationsSeeder::class);
        $dtos = [new PhoneDto('**********',2), new PhoneDto('**********',1)];
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dtos);
        $this->assertCount(4, ClientPhone::all());
        $this->assertCount(2, ClientPhone::where(['last'=>1])->get());
    }

    public function testSettingOneExistingAndOneNew()
    {
        $this->seed(ClientRelationsSeeder::class);
        $dtos = [new PhoneDto('**********',1), new PhoneDto('0896667790',2)];
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dtos);
        $this->assertCount(3, ClientPhone::all());
        $this->assertCount(2, ClientPhone::where(['last'=>1])->get());
    }
}