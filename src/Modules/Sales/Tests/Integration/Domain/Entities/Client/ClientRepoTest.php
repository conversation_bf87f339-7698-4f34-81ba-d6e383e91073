<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Client;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Models\ClientEmail;
use Modules\Common\Models\NotificationSetting;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Http\Dto\AddressDto;
use Modules\Sales\Http\Dto\NotificationSettingDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class ClientRepoTest extends TestCase
{
    use DatabaseTransactions;

    private AddressDto $addressDto;
    private ClientAddressRepository $addrRepo;
    private array $existingAddressData;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testCreatingNewClientWithRelations()
    {
        $sut = app(Client::class);
        $clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $sut->processNewApplication($clientDto);
        $this->assertEquals(1, $clientDto->relationDto->loanDto->product_id);
        $this->assertEquals('CEN1', (new LoanRepository())->getProductById(1)->getAttribute('code'));
        $this->assertEquals($clientDto->first_name, $sut->dbModel()->first_name);
        $this->assertEquals($clientDto->first_name_latin, $sut->dbModel()->first_name_latin);
    }

    public function testUpdateFromApi()
    {
        $this->seed(ClientRelationsSeeder::class);
        $sut = app(Client::class);
        $sut->updateFromApi(1, '<EMAIL>', [NotificationSettingDto::from(
            ["type" => "marketing","channel" => "sms","value" => 0]
        )]);
        $dbEmail = ClientEmail::where(['client_id'=>1, "last" => 1])->first();
        $this->assertEquals('<EMAIL>', $dbEmail->email);
        $setting = NotificationSetting::where(['client_id'=>1, "type" => "marketing","channel" => "sms"])->first();
        $this->assertEquals('0', $setting->value);
    }
}