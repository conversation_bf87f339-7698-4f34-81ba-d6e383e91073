<?php

namespace Modules\Sales\Tests\Integration\Application\Reactions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Modules\Common\CommandBus\CommandBus;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Models\ClientAddress;
use Modules\Head\Http\Commands\UpdateClientAddressesCommand;
use Modules\Sales\Http\Dto\AddressDto;
use Tests\TestCase;

class UpdateClientAddressesReactionTest extends TestCase
{
    use DatabaseTransactions;

    private CommandBus $bus;

    public function setUp(): void
    {
        parent::setUp();
        $this->bus = app(CommandBus::class);
    }

    public function testNewAddressesMatchOld()
    {
        $this->seed(ClientRelationsSeeder::class);
        $command = new UpdateClientAddressesCommand(1,
            [
                new AddressDto('1','alalal 27', '1111', 'id_card'),
                new AddressDto('1','alalal 28', '1112','current')
            ]
        );
        $this->bus->dispatch($command);
        $this->assertCount(2, ClientAddress::where(['client_id'=>1, 'last'=>1])->get());
    }

    public function testBothAddressesChanged()
    {
        $this->seed(ClientRelationsSeeder::class);
        $command = new UpdateClientAddressesCommand(1,
            [
                new AddressDto('1','alalal 29', '1111', 'id_card'),
                new AddressDto('1','alalal 30', '1112','current')
            ]
        );
        $this->bus->dispatch($command);
        $this->assertCount(2, ClientAddress::where(['client_id'=>1, 'last'=>1])->get());
        $this->assertCount(2, ClientAddress::where(['client_id'=>1, 'last'=>0])->get());
    }
}