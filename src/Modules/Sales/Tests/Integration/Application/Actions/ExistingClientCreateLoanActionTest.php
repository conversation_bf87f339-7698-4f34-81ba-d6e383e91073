<?php

namespace Modules\Sales\Tests\Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\RefinancedInstallmentsSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class ExistingClientCreateLoanActionTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private ClientRepository $clientRepo;
    private array $existingAddressData;

    public function setUp(): void
    {
        parent::setUp();
        $this->clientRepo = new ClientRepository(new DbClient());
        $this->sut = new ExistingClientCreateLoanAction(app(Client::class));
    }

    public function testCreatingNewLoanHappyPath()
    {
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $this->seed(ClientRelationsSeeder::class);
        $dbClient = DbClient::where(['client_id'=>'1'])->first();
        $dto->relationDto->loanDto->administrator_id = 1;
        $this->sut->execute($dbClient, $dto->relationDto->loanDto);
        $client = $this->sut->getClient();
        $this->assertEquals($dto->first_name, $client->dbModel()->first_name);
        $this->assertEquals($dto->relationDto->loanDto->loan_sum, $this->sut->getDbLoan()->amount_approved);
    }

    public function testCreatingRefinancingFromOffice()
    {
        $this->seed([
            ActiveInstallmentsSeeder::class,
            RefinancedInstallmentsSeeder::class
        ]);
        /** @var ClientDto $dto */
        $dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $loanDto = $dto->relationDto->loanDto;
        $loanDto->office_id = 2;
        $loanDto->product_id = 3;
        $loanDto->payment_method_id = PaymentMethodEnum::BANK->id();
        $loanDto->refinanced_loan_ids = [ActiveLoanSeeder::LOAN_ID, /*RefinancedLoanSeeder::LOAN_ID*/];
        $sut = app()->make(ExistingClientCreateLoanAction::class);
        $sut->execute(DbClient::find(ActiveLoanSeeder::CLIENT_ID), $loanDto);
        $this->assertCount(1, LoanRefinance::all());
    }
}