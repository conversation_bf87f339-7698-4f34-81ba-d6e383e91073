<?php

namespace Modules\Sales\Tests\Unit\Domain;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\Bank as DbBank;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\ClientBankAccount;
use Modules\Head\Repositories\ClientBankAccountRepository;
use Modules\Sales\Domain\Entities\Bank\Bank;
use Modules\Sales\Domain\Entities\Bank\ClientBank;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Exceptions\ClientRelations\ClientBankNotSaved;
use Modules\Sales\Domain\Exceptions\ClientRelations\NewBankBuiltWithoutIban;
use Modules\Sales\Http\Dto\BankDto;
use PHPUnit\Framework\MockObject\Stub\ReturnValueMap;
use Tests\TestCase;

class ClientBankTest extends TestCase
{
    const CURRENT_CLIENT_ID = 12;
    const OTHER_CLIENT_ID = 13;
    const CURRENT_CLIENT_IBAN = '**********************';
    const BIC = 'BUINBGSF';
    private Client $client;
    private DbBank $dbModel;

    public function setUp(): void
    {
        parent::setUp();
        $dbClient = $this->createMock(DbClient::class);
        $dbClient->method('getKey')->willReturn(self::CURRENT_CLIENT_ID);
        $this->client = $this->createMock(Client::class);
        $this->client->method('dbModel')->willReturn($dbClient);
        $this->dbModel = $this->createMock(DbBank::class);
        $this->dbModel->method('getAttribute')
            ->willReturn(
                new ReturnValueMap([
                    ['bic', self::BIC],//BTOPQDMRA4Z
                    ['bank_id', 1]
                ])
            );
    }

    public function testSettingAccountWithNewIban()
    {
        $repo = $this->createMock(ClientBankAccountRepository::class);
        $repo->method('getByIBAN')->willReturn(new Collection());
        $repo->method('save')->willReturn($this->createMock(ClientBankAccount::class));
        $dto = new BankDto(self::CURRENT_CLIENT_IBAN, self::BIC, null);
        $bank = $this->createMock(Bank::class);
        $bank->method('dbModel')->willReturn($this->dbModel);
        $sut = new ClientBank($repo, $bank, new ClientBankAccount());
        $sut->build($this->client, $dto);
        $this->assertEquals(self::BIC, $sut->dbModel()->bic);
        $this->assertEquals(self::CURRENT_CLIENT_IBAN, $sut->dbModel()->iban);
    }
}