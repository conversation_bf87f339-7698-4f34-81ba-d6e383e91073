<?php

namespace Modules\Sales\Tests\Unit\Domain\Loan;

use Modules\Common\Domain\CurrentDate;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Domain\Entities\Loan\ActiveLoan;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Sales\Domain\Entities\Loan\Installments;
use StikCredit\Calculators\LoanCalculator;
use StikCredit\Calculators\LoanCarton;
use Tests\TestCase;

class ActiveLoanTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
    }

    public function testGetTotalDebtSum()
    {
        $dbLoan = $this->createMock(DbLoan::class);
        $calculator = $this->createMock(LoanCalculator::class);
        $carton = $this->createMock(LoanCarton::class);
        $carton->earlyRepaymentAmount = 2;
        $calculator->method('loanCarton')->willReturn($carton);
        $dbLoan->method('getCredit')->willReturn($calculator);
        $repo = $this->createMock(LoanRepository::class);
        $installments = $this->createMock(Installments::class);
        $installments->method('getTotalDebtSum')->willReturn(2.0);
        $installments->method('buildUnpaid')->willReturn($installments);
        $currentDate = $this->createMock(CurrentDate::class);
        $sut = new ActiveLoan($dbLoan, $repo, $installments, $currentDate);
        $sum = $sut->getProjectedDebtSum();
        $this->assertEquals(2, $sum);
    }
}