<?php

namespace Modules\Sales\Http\Dto;

use Modules\Common\Http\Dto\SpatieDto;
use Modules\Common\Models\ClientPicture;

class PictureDto extends SpatieDto
{
    public function __construct(
        public string $image,
        public string $type,
        public string $source,
        public ?string $pin
    ) {
    }

    public static function getFrom(?array $array): ?PictureDto
    {
        if (!isset($array['image']) || !$array['image']) {
            return null;
        }
        $array['type'] = $array['type'] ?? ClientPicture::TYPE_MVR;

        /// by default set picture
        $array['source'] = ClientPicture::SOURCE_MVR_PICT;
        if (isset($array['source']) && $array['source'] === ClientPicture::SOURCE_MVR_SIGN) {
            $array['source'] = ClientPicture::SOURCE_MVR_SIGN;
        }

        return self::from($array);
    }
}