<?php

namespace Modules\Sales\Http\Controllers;

use Exception;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\SaleDecision;
use Modules\Sales\Exceptions\SalesException;
use Modules\Sales\Http\Requests\SaleDecisionCrudRequest;
use Modules\Sales\Services\SaleDecisionService;
use ReflectionException;

class SaleDecisionController extends BaseController
{
    private const INDEX_ROUTE = 'sales.saleDecision.list';

    /**
     * @var string
     */
    protected string $pageTitle = 'Sale decision list';

    /**
     * @var SaleDecisionService
     */
    private SaleDecisionService $saleDecisionService;

    /**
     * SaleDecisionController constructor.
     *
     * @param SaleDecisionService $saleDecisionService
     *
     * @throws ReflectionException
     */
    public function __construct(SaleDecisionService $saleDecisionService)
    {
        $this->saleDecisionService = $saleDecisionService;

        parent::__construct();
    }

    /**
     * @return RedirectResponse|View
     * @throws Exception
     */
    public function list()
    {
        return view(
            'sales::sale-decision.list',
            [
                'saleDecisions' => $this->refresh(),
                'types' => $this->saleDecisionService->getTypes(),
            ]
        );
    }

    /**
     * @return Renderable|RedirectResponse
     * @throws Exception
     */
    public function create()
    {
        try {
            $this->setPageTitle('Create sale decision');

            $types = $this->saleDecisionService->getTypes();

            return view(
                'sales::sale-decision.crud',
                compact('types'),
            );
        } catch (\Throwable $t) {

            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @param SaleDecisionCrudRequest $request
     *
     * @return RedirectResponse
     * @throws ProblemException|Exception
     */
    public function store(SaleDecisionCrudRequest $request): RedirectResponse
    {
        try {
            $newSaleDecision = $this->saleDecisionService->store($request->validated());

            if (!$newSaleDecision) {
                return redirect()
                    ->route(self::INDEX_ROUTE)
                    ->with(
                        'fail',
                        __('sales::saleDecision.saleDecisionCreationFailed')
                    );
            }

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with(
                    'success',
                    __('sales::saleDecision.saleDecisionCreatedSuccessfully')
                );
        } catch (\Throwable $t) {

            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @param SaleDecision $saleDecision
     *
     * @return View|RedirectResponse
     *
     * @throws ProblemException|Exception
     */
    public function edit(SaleDecision $saleDecision)
    {
        try {
            $types = $this->saleDecisionService->getTypes();

            return view(
                'sales::sale-decision.crud',
                compact('saleDecision', 'types'),
            );
        } catch (\Throwable $t) {
            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @param SaleDecision $saleDecision
     * @param SaleDecisionCrudRequest $request
     *
     * @return RedirectResponse
     *
     * @throws ProblemException|Exception
     */
    public function update(SaleDecision $saleDecision, SaleDecisionCrudRequest $request): RedirectResponse
    {
        try {
            $saleDecision = $this->saleDecisionService->update(
                $saleDecision,
                $request->validated()
            );

            if (!$saleDecision) {
                return redirect()
                    ->route(self::INDEX_ROUTE)
                    ->with(
                        'fail',
                        __('sales::saleDecision.saleDecisionUpdateFailed')
                    );
            }

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with(
                    'success',
                    __('sales::saleDecision.saleDecisionUpdatedSuccessfully')
                );
        } catch (\Throwable $t) {
            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @param SaleDecision $saleDecision
     *
     * @return RedirectResponse
     *
     * @throws ProblemException|Exception
     */
    public function delete(SaleDecision $saleDecision): RedirectResponse
    {
        try {
            $isDeleted = $this->saleDecisionService->delete($saleDecision);

            if (!$isDeleted) {
                return redirect()
                    ->route(self::INDEX_ROUTE)
                    ->with(
                        'fail',
                        __('sales::saleDecision.saleDecisionDeletionFailed')
                    );
            }

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with(
                    'success',
                    __('sales::saleDecision.saleDecisionDeletedSuccessfully')
                );
        } catch (\Throwable $t) {
            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @return mixed
     */
    private function refresh()
    {
        return $this->saleDecisionService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param SaleDecision $saleDecision
     *
     * @return RedirectResponse
     *
     * @throws ProblemException|Exception
     */
    public function enable(SaleDecision $saleDecision): RedirectResponse
    {
        try {
            $this->saleDecisionService->enable($saleDecision);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with(
                    'success',
                    __('sales::saleDecision.saleDecisionEnabledSuccessfully')
                );
        } catch (\Throwable $t) {
            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    /**
     * @param SaleDecision $saleDecision
     *
     * @return RedirectResponse
     *
     * @throws ProblemException|Exception
     */
    public function disable(SaleDecision $saleDecision): RedirectResponse
    {
        try {
            $this->saleDecisionService->disable($saleDecision);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with(
                    'success',
                    __('sales::saleDecision.saleDecisionDisabledSuccessfully')
                );
        } catch (\Throwable $t) {
            return $this->handleException(new SalesException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }
}
