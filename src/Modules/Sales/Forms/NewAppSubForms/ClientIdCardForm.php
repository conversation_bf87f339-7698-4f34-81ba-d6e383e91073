<?php

namespace Modules\Sales\Forms\NewAppSubForms;

use Carbon\Carbon;
use Kris\LaravelFormBuilder\Form;
use Modules\Common\Models\IdCardIssued;

class ClientIdCardForm extends Form
{
    public $name = 'client_idcard';

    public function buildForm(): void
    {
        $this->add('pin', 'text', [
            'label' => __('table.Pin'),
            'value' => $this->getData('client_idcard.pin'),
            'attr' => [
                'required' => 'required',
                'minlength' => 10,
                'maxlength' => 10,
                'data-parsley-pattern' => '^[0-9]*$',
                'data-parsley-pattern-message' => __('Pin allowed only numbers.'),
                "autocomplete" => "nope"
            ]
        ]);

        $this->add('idcard_number', 'text', [
            'label' => __('table.IdCardNumber'),
            'value' => $this->getData('client_idcard.idcard_number'),
            'attr' => [
                'required' => 'required',
                'minlength' => 9,
                'maxlength' => 9,
                'data-parsley-pattern' => '^[A-Z0-9]+$',
                'data-parsley-pattern-message' => __('messages.FormatIdCardNumber'),
                "autocomplete" => "nope"
            ]
        ]);

        $this->add('idcard_issued_id', 'select', [
            'label' => __('sales::newApplication.issuedBy'),
            'choices' => IdCardIssued::selectOptions('name', 'idcard_issued_id'),
            'empty_value' => __('table.SelectOption'),
            'selected' => $this->getData('client_idcard.idcard_issued_id'),
            'attr' => [
                'required' => 'required',
                'data-live-search' => 'true',
                "autocomplete" => "nope"
            ]
        ]);

        $issueDate = '';
        if ($this->getData('client_idcard.issue_date')) {
            $issueDate = Carbon::parse($this->getData('client_idcard.issue_date'))->format('d.m.Y');
        }

        $this->add('issue_date', 'text', [
            'label' => __('table.IssueDate'),
            'value' => formatDate($this->getData('client_idcard.issue_date'), 'd.m.Y'),
            'attr' => [
                'required' => 'required',
                'data-parsley-datebeforetoday' => "",
                'data-date-picker-to-today-default-empty' => 'true',
                'autocomplete' => 'nope'
            ]
        ]);

        $validToDate = '';
        if ($this->getData('client_idcard.valid_date')) {
            $validToDate = Carbon::parse($this->getData('client_idcard.valid_date'))->format('d.m.Y');
        }

        $this->add('valid_date', 'text', [
            'label' => __('table.ValidDate'),
            'value' => $validToDate,
            'attr' => [
                'required' => 'required',
                'data-date-picker-from-today-default-empty' => 'true',
                'data-parsley-dateaftertoday' => "",
                'data-parsley-errors-container' => ".valid-date-errors",
                'autocomplete' => 'nope'
            ]
        ]);
    }
}
