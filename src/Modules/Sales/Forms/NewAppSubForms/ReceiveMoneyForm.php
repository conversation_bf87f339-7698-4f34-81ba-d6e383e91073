<?php

namespace Modules\Sales\Forms\NewAppSubForms;

use Kris\LaravelFormBuilder\Form;
use Modules\Common\Models\Bank;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;

class ReceiveMoneyForm extends Form
{
    public $name = 'loan';

    public function buildForm(): void
    {
        $this->add('payment_method', 'hidden', [
            'value' => $this->getData('payment_method')
        ]);

        $this->add('bank_account_id', 'select', [
            'label' => __('head::clientCrud.PaymentMethodDefaultOption'),
            'choices' => $this->getAvailablePaymentMethods(),
            'empty_value' => __('table.SelectOption'),
            'attr' => [
                'required' => 'required'
            ]
        ]);

        $this->add('iban', 'text', [
            'label' => __('table.Iban'),
            'attr' => [
                'data-parsley-minlength' => '16',
                'data-parsley-maxlength' => '34',
                'data-parsley-pattern' => config('validation.requestRules.commonIbanParsley'),
                'data-parsley-pattern-message' => __('Невалиден IBAN.'),
                "autocomplete" => "nope"
            ]
        ]);

        $this->add('comment', 'textarea', [
            'label' => __('table.Comment'),
            'attr' => [
                'rows' => 2,
                "autocomplete" => "nope"
            ]
        ]);
    }


    protected function getAvailablePaymentMethods(): array
    {
        $currentOfficeId = (int)session('currentOfficeId');
        if (empty($currentOfficeId)) {
            $administrator = auth()->user();
            $offices = $administrator->offices;
            if ($offices->count()) {
                $office = $offices->first();
                if (!empty($office->office_id)) {
                    $currentOfficeId = $office->office_id;
                }
            }
        }

        return Office::whereOfficeId($currentOfficeId)->first()?->getOfficePaymentMethods() ?? [];

//        if ($currentOfficeId === Office::OFFICE_ID_WEB) {
//            return PaymentMethod::getOnlinePaymentMethods();
//        }
//
//        return PaymentMethod::getPhysicalPaymentMethods();
    }
}
