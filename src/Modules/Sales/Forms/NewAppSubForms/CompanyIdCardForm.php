<?php

namespace Modules\Sales\Forms\NewAppSubForms;

use Kris\LaravelFormBuilder\Form;

class CompanyIdCardForm extends Form
{
    public $name = 'client_idcard';

    public function buildForm(): void
    {
        $this->add('pin', 'text', [
            'label' => __('table.Eik'),
            'value' => $this->getData('client_idcard.pin'),
            'attr' => [
                'required' => 'required',
                'minlength' => 8,
                'maxlength' => 10,
                'data-parsley-pattern' => '^[0-9]*$',
                'data-parsley-pattern-message' => __('Eik allowed only numbers.'),
            ]
        ]);

        $this->add('idcard_number', 'text', [
            'label' => __('table.CompanyTaxRegistryNumber'),
            'value' => $this->getData('client_idcard.idcard_number'),
            'attr' => [
                'required' => 'required',
                'minlength' => 9,
                'maxlength' => 9,
//                'data-parsley-pattern' => '^[0-9]*$',
//                'data-parsley-pattern-message' => __('Pin allowed only numbers.'),
            ]
        ]);
    }
}
