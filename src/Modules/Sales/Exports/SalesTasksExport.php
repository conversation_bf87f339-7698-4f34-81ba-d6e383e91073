<?php

namespace Modules\Sales\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SalesTasksExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    /**
     * @var Collection
     */
    private Collection $rows;

    /**
     * LoanExport constructor.
     *
     * @param Collection $rows
     */
    public function __construct(Collection $rows)
    {
        $this->rows = $rows;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    public function headings(): array
    {
        return [
            'Номер',
            'Вид Продажба',
            'Продукт',
            'Сума',
            'Приод на Кредита',
            'Клиент',
            '<PERSON><PERSON><PERSON><PERSON> на Клиента',
            'ЕГН',
            'Телефон',
            'Дата на Създаване',
            'Таймер',
            'Статус'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 20,
            'C' => 20,
            'D' => 8,
            'E' => 10,
            'F' => 8,
            'G' => 30,
            'H' => 15,
            'I' => 15,
            'J' => 20,
            'K' => 15,
            'L' => 10
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        foreach ($sheet->getColumnIterator('K', 'L') as $column) {
//            $column->getWorksheet()
//                ->getStyle($column->getColumnIndex().'1:'.$column->getColumnIndex().$sheet->getHighestDataRow())
//                ->getAlignment()
//                ->setWrapText(true);
//        }
    }
}
