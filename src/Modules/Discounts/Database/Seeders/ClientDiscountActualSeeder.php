<?php

namespace Modules\Discounts\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;

class ClientDiscountActualSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create();
        $clientDiscountActual = [];

        foreach (range(1, 10) as $index => $number) {
            $clientDiscountActual[] = [
                'client_id' => $faker->numberBetween(1, 10),
                'product_id' => $faker->numberBetween(1, 4),
                'percent' => $faker->numberBetween(10, 50),

                'valid_from' => now(),
                'valid_till' => now()->addDays($faker->numberBetween(10, 30)),

                'created_at' => now(),
                'created_by' => $faker->numberBetween(1, 3),
            ];
        }

        DB::table('client_discount_actual')->insert($clientDiscountActual);
    }
}
