<?php

namespace Modules\Discounts\Models;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Enums\YesNoEnum;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\Client;

/**
 * @mixin IdeHelperPreApprovedClient
 */
final class PreApprovedClient extends BaseModel
{
    protected $fillable = [
        'created_by',
        'updated_by',
        'client_id',
        'amount',
        'from_date',
        'to_date',
        'sale_task_created',
        'sale_task_id',
    ];

    protected $casts = [
        'sale_task_created' => YesNoEnum::class,
        'from_date' => 'date',
        'to_date' => 'date',
        'product_ids' => 'array',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }

    public function getFromDateAttribute(?string $value): ?CarbonInterface
    {
        return $value ? Carbon::parse($value)->startOfDay() : null;
    }

    public function getToDateAttribute(?string $value): ?CarbonInterface
    {
        return $value ? Carbon::parse($value)?->endOfDay() : null;
    }
}
