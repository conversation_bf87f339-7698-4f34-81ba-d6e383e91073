@extends('layouts.app')
@php
    $sortingArray['client']['client_id']['no'] = __('table.ClientDiscountId');
    $sortingArray['client']['client_full_name']['no'] = __('table.Name');
    $sortingArray['client']['pin']['no'] = __('table.Pin');
    $sortingArray['client']['discount_product_name']['no'] = __('table.Product');
    $sortingArray['client']['discount_percent']['no'] = __('table.DiscountPercent');
    $sortingArray['client']['discount_valid_from']['no'] = __('table.ValidFrom');
    $sortingArray['client']['discount_valid_till']['no'] = __('table.ValidTill');
    $sortingArray['client']['discount_created_at']['no'] = __('table.ClientDiscountActualCreatedAt');
    $sortingArray['client']['discount_creator']['no'] = __('table.ClientDiscountActualCreatedBy');

    $admin = getAdmin();
    $permissionManual = $admin->getPermissionAdditionalInfoByRouteName('head.clients.importClientsDiscountManual');
    $permissionAutomatic = $admin->getPermissionAdditionalInfoByRouteName('head.clients.importClientsDiscount');
    $adminDiscountPercent = $admin->getAdminDiscount();
@endphp
@section('style')
    <style>
        .custom-file .custom-file-label::after {
            content: " {{ __('table.AttachDiscountFile') }}" !important;
        }
    </style>
    <link rel="stylesheet" href="{{ asset('css/discount-style.css') }}">
@endsection

@section('content')

    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div class="card">
                <form id="clientDiscountForm" class="form-inline card-body"
                      action="{{ route('head.discountsClients.list') }}"
                      method="PUT">
                    @csrf
                    @if($permissionManual !== false || $permissionAutomatic !== false)
                        <div class="form-row w-100" style="margin-bottom: 16px;">
                            <div style="min-width: fit-content;" class="col-lg-1">
                                <button type="button" class="btn btn-primary" data-toggle="modal"
                                        data-target="#discountModal" style="width: 120px; border-radius: 5px; ">
                                    {{ __('table.NewDiscount') }}
                                </button>
                            </div>
                            <div style="min-width: fit-content;" class="col-lg-1">
                                <button id="deleteSelectedDiscounts" type="button" class="btn btn-primary">
                                    {{ __('table.ClientDiscountCheckbox') }}
                                </button>
                            </div>
                        </div>
                    @endif
                    <div class="form-row w-100">
                        <div class="col-lg-1">
                            <label for="client_id">{{__('table.ClientDiscountId')}}:</label>
                            <input name="client_id" class="form-control w-100 mb-3" type="text"
                                   value="{{ session($cacheKey . '.client_id') }}">
                        </div>

                        <div class="col-lg-2">
                            <label for="name">{{__('table.FilterByName')}}:</label>
                            <input name="name" class="form-control w-100 mb-3" type="text"
                                   value="{{ session($cacheKey . '.name') }}">
                        </div>

                        <div class="col-lg-1">
                            <label for="pin">{{__('table.FilterByClientPin')}}:</label>
                            <input name="pin" class="form-control w-100 mb-3" type="text"
                                   value="{{ session($cacheKey . '.pin') }}">
                        </div>

                        <div class="col-lg-2">
                            <label for="product_id">{{__('table.Product')}}:</label>
                            <select class="form-control w-100 mb-3" name="product_id">
                                <option value=""></option>
                                @if (!empty($products))
                                    @foreach($products as $productId => $productName)
                                        <option
                                                value="{{ $productId }}"
                                                @if($productId == session($cacheKey . '.product_id'))
                                                    selected
                                                @endif
                                        >
                                            {{ $productName }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                        </div>

                        <div class="col-lg-1">
                            <label for="percentFrom">{{__('table.FilterByDiscountPercentFrom')}}:</label>
                            <input name="percentFrom" class="form-control w-100 mb-3" type="number"
                                   min="0" max="100" placeholder=""
                                   value="{{ session($cacheKey . '.percentFrom') }}">
                        </div>

                        <div class="col-lg-1">
                            <label for="percentTo">{{__('table.FilterByDiscountPercentTo')}}:</label>
                            <input name="percentTo" class="form-control w-100 mb-3" type="number"
                                   min="0" max="100" placeholder=""
                                   value="{{ session($cacheKey . '.percentTo') }}">
                        </div>

                        <div class="col-lg-1">
                            <label for="valid[from]">{{__('table.FilterByValidFrom')}}:</label>
                            <input type="text" autocomplete="off" name="valid[from]"
                                   class="form-control w-100 singleDataPicker"
                                   value="{{ session($cacheKey . '.valid.from') }}"
                            >
                        </div>

                        <div class="col-lg-1">
                            <label for="valid[till]">{{__('table.FilterByValidTo')}}:</label>
                            <input type="text" autocomplete="off" name="valid[till]"
                                   class="form-control w-100 singleDataPicker"
                                   value="{{ session($cacheKey . '.valid.till') }}"
                            >
                        </div>
                    </div>

                    <div class="form-row w-100">
                        <div class="col-lg-1">
                            <label for="administrator_id">{{__('table.FilterByAdmin')}}:</label>
                            <select class="form-control w-100 mb-3" name="administrator_id">
                                <option value=""></option>
                                @if (!empty($admins))
                                    @foreach($admins as $adminId => $fullName)
                                        <option
                                            value="{{ $adminId }}"
                                            @if($adminId == session($cacheKey . '.administrator_id'))
                                                selected
                                            @endif
                                        >
                                            {{ $fullName }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <div class="col-lg-1">
                            <label for="createdAt">{{__('table.FilterByCreatedAtFrom')}}:</label>
                            <input type="text" autocomplete="off" name="createdAt" id="createdAt"
                                   class="form-control"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                            >
                        </div>
                    </div>

                    <x-btn-filter/>
                </form>
            </div>
        </div>
    </div>

    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card client-table">
                <div class="card-body">
                    <div class="table-responsive" id="discount-clients">
                        <table class="table">
                            <thead>
                            @include('discounts::client.sorting-head')
                            </thead>
                            <tbody id="clientsTable">
                            @include('discounts::client.discount-clients-list-table')
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div id="buttons">
            <x-btn-delete
                    url="{{ route('head.clients.removeDiscount', 0) }}"
                    title="btn.DeleteDiscount"
            />
        </div>
    </div>

    <!-- Modal -->
    @include('discounts::client.discount-modal')
    <!-- End Modal -->

@endsection

@push('scripts')
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.doubleScroll.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/bootstrap-input-spinner.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/loadSingleDataPicker.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/clientsSearch.js') }}"></script>
    <script src="{{ asset('dist/js/bootstrap-select.min.js') }}"></script>
    <script>
        $('body').addClass('discounts');
        $(".page-breadcrumb").hide();

        // init custom [+INPUT-]
        $("#discountPercent").inputSpinner();
        // add custom styles to [+INPUT-]
        $("input[id^='discountPercent_MP']").each(function (index, element) {
            $(element).attr('style', 'text-align: center; flex: 0.5 1 auto');
        });

        let clientDiscountControllerUrl = '{{ route('head.clients.refreshDiscounts') }}';
        let formId = $("#clientDiscountForm");
        let tableId = $('#clientsTable');

        loadSimpleDataGrid(clientDiscountControllerUrl, formId, tableId);


        $(document).ready(function () {

            loadSingleDataPicker($('.singleDataPicker'));
            loadDateRangePicker($('#createdAt'));
            loadDateRangePicker($('#validPeriod'));

            $('#discountProduct').selectpicker();

            $("#searchClients").on('keyup', function () {
                let query = $(this).val();
                fetchSearchedDiscountClients(
                    "{{ route('head.discountsClients.search') }}",
                    query,
                    translation,
                    '#discountClients',
                    '.discount-clients-list',
                    'input'
                );
            });

            window.onclick = function (event) {
                hideUserSearch('onClick');
            }
            $('.table-responsive').doubleScroll({
                resetOnWindowResize: true,
                onlyIfScroll: true
            });

            $("#maxRows").change(function () {
                let routeRefreshDiscountClients = '{{ route('head.clients.refreshDiscounts')}}';

                $.ajax({
                    type: 'get',
                    url: routeRefreshDiscountClients,
                    data: formId.serialize(),

                    success: function (data) {
                        tableId.html(data);
                    },
                });
            });
        });

    </script>
@endpush()
