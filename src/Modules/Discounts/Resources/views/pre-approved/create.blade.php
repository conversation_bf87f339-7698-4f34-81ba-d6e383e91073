@extends('layouts.app')
@section('content')
    <div class="row">
        <div class="col-lg-6">
            <x-card title="{{ __('discounts::pre-approved-clients.Create') }}">
                {!! form_start($preApprovedClientForm,['onsubmit'=>'disableSubmitButtonWithClass(this);']) !!}

                <a href="{{asset('/assets/sample/ExampleImportPreApprovedClients.xlsx')}}"
                   class="btn btn-outline-warning mb-3"
                >
                    <i class="fa fa-download"></i>&nbsp;
                    {{ __('discounts::pre-approved-clients.DownloadExampleFileToImport') }}
                </a>

                {!! form_rest($preApprovedClientForm) !!}

                <x-submit-form-btn url="{{ route('head.pre-approved.list') }}"/>

                {!! form_end($preApprovedClientForm) !!}
            </x-card>
        </div>
    </div>
    <!-- End ./row -->
@endsection
@push('scripts')
    <script>
        $(function () {
            let $findClientsEl = $('select[data-find-client="true"]');

            $findClientsEl.select2({
                width: "100%",
                ajax: {
                    url: $($findClientsEl).data('req-route'),
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: '[' + item.pin + ']: ' + item.name,
                                    id: item.client_id
                                }
                            })
                        };
                    }
                }
            });
        })
    </script>
@endpush
