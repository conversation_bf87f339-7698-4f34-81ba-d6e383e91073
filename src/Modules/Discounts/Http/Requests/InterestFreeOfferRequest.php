<?php

namespace Modules\Discounts\Http\Requests;

use Carbon\Carbon;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\Client;
use Modules\Common\Models\Product;

final class InterestFreeOfferRequest extends BaseRequest
{
    public function rules(): array
    {
        $firstDateRule = [];
        $lastDateRule = [];

        if ($this->input('dateFromTo')) {
            $dates = getDatesForFilter($this->input('dateFromTo'));

            $firstDateRule[] = 'after_or_equal:' . today();
            $lastDateRule[] = 'after_or_equal:firstDate';

            $this->merge([
                'firstDate' => Carbon::parse($dates['from'])->format('Y-m-d'),
                'lastDate' => Carbon::parse($dates['to'])->format('Y-m-d'),
            ]);
        }

        return [
            'importClients' => 'nullable|file',
            'clientIds' => 'nullable|array',
            'clientIds.*' => 'sometimes|required|integer|distinct|exists:' . Client::class . ',client_id',
            'productIds' => 'required|array',
            'productIds.*' => 'required|integer|distinct|exists:' . Product::class . ',product_id',
            'dateFromTo' => 'required|string|required|string|regex:/^\d{2}-\d{2}-\d{4} - \d{2}-\d{2}-\d{4}$/',
            'firstDate' => $firstDateRule,
            'lastDate' => $lastDateRule,
        ];
    }

    public function messages(): array
    {
        return [
            'dateFromTo.regex' => __('Грешен формат. Пример: :exampleDate', [
                'exampleDate' => date('d-m-Y') . ' - ' . date('d-m-Y')
            ]),
        ];
    }
}
