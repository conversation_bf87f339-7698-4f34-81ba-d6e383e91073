<?php

namespace Modules\Payments\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Payments\Application\Actions\ApsUpdatedLoanExportAction;
use Modules\Payments\Application\Actions\LmfUpdatedLoanExportAction;

class OuterCollectorLoansController extends BaseController
{
    public function index(): View
    {
        return view('payments::outer-collector-loans.index');
    }

    public function export($type)
    {
        $action = match ($type) {
            'aps' => app(ApsUpdatedLoanExportAction::class),
            'lmf' => app(LmfUpdatedLoanExportAction::class),
            default => throw new \Exception('Грешен тип за експорт'),
        };

        if (!$action->execute()) {
            return back()->with('warning', 'Нама данни за експорт');
        }
    }
}
