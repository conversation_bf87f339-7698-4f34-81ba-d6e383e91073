<?php

namespace Modules\Payments\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Carbon;
use Illuminate\View\View;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Payment;
use Modules\Payments\Application\Actions\ExportPaymentsAction;
use Modules\Payments\Application\Actions\PaymentDetailsAction;
use Modules\Payments\Application\Actions\PaymentsDataAction;
use Modules\Payments\Application\Actions\Task\RequestEasyPayRefundAction;
use Modules\Payments\Http\Requests\AllPaymentsSearchRequest;
use Modules\Payments\Services\PaymentService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

final class PaymentController extends BaseController
{
    public function __construct(
        private readonly PaymentService $paymentService
    )
    {
        parent::__construct();
    }

    public function list(
        AllPaymentsSearchRequest $request,
        PaymentsDataAction       $paymentsDataAction
    ): View
    {
        return view('payments::payments.list', $paymentsDataAction->execute($request->validated()));
    }

    public function details(
        Payment $payment,
        PaymentDetailsAction $paymentDetailsAction
    ): View {
        return view(
            'payments::payments.details',
            $paymentDetailsAction->execute($payment)
        );
    }

    public function export(AllPaymentsSearchRequest $request, ExportPaymentsAction $action): BinaryFileResponse
    {
        return $action->execute($request->validated())->download('payments_export_' . time() . '.xlsx');
    }

    public function refresh(
        AllPaymentsSearchRequest $request,
        PaymentsDataAction       $paymentsDataAction
    ): View
    {
        $filters = $request->validated();
        $data = $paymentsDataAction->execute($filters);
        $data['payments']->withPath(route('payment.payments.list'));

        return view('payments::payments.list-table', $data);
    }

    /**
     * @param int|null $limit
     *
     * @return LengthAwarePaginator|RedirectResponse
     * @throws Exception
     */
    public function getTableData(int $limit = null)
    {
        return $this->paymentService->getAllPaymentsByFilters(
            $limit ?? parent::getTableLength(),
            $this->getWhereConditions()
        );
    }

    /**
     * Edit Easypay payment in status SENT
     * @param Payment $payment
     * @return
     */
    public function refundPage(?Payment $payment)
    {
        $requests = $payment->getEasyPayRequests();

        return view(
            'payments::payment.refund',
            [
                'client' => $payment->client,
                'payment' => $payment,
                'requests' => $requests,
            ]
        );
    }

    public function refund(?Payment $payment)
    {
        return $this->refundCommon($payment, substr(strrchr(__METHOD__, ':'), 1));
    }

    public function refundState(?Payment $payment)
    {
        return $this->refundCommon($payment, substr(strrchr(__METHOD__, ':'), 1));
    }

    private function refundCommon(Payment $payment, string $method)
    {
        if (!$payment->canBeRefunded()) {
            throw new ProblemException(__('payments::paymentTask.couldNotRefundWrongStatus'));
        }


        $refundAction = app(RequestEasyPayRefundAction::class);
        $result = $refundAction->execute($payment, null, $payment->isRefundInProgress());


        if ('OK' == $result || ($method == 'refundState' && 'PROCESSING' == $result)) {
            return redirect()->route('payment.payments.list')
                ->with(['success' => __('payments::paymentTask.RefundOk')]);
        }


        if ('PROCESSING' == $result) {
            return redirect()
                ->back()
                ->with([
                    'success' => 'Стониране започна. Проверете статус на стонирането.',
                ]);
        }


        return redirect()
            ->back()
            ->with([
                'fail' => 'Грешка при стонирането. Не могат да се върнат парите от Изипей. Опитай отново.'
            ]);
    }

    /**
     * @throws Throwable
     */
    public function rollBack(Payment $payment): RedirectResponse
    {
        try {

            $this->paymentService->rollBack($payment);

            return redirect()
                ->route('payment.payments.list')
                ->with('success', __('payments::payments.deletedSuccessful'));

        } catch (\Throwable $e) {

            return redirect()
                ->back()
                ->with([
                    'fail' => 'Грешка. ' . $e->getMessage(),
                ]);
        }
    }

    private function getWhereConditions()
    {
        $changed = false;
        $where = session($this->cacheKey, []);
        if (empty($where['createdAt'])) {
            $today = Carbon::today();
            $beforeSevenDays = $today->copy()->subDays(7)->format(self::DATE_FORMAT);
            $today = $today->format(self::DATE_FORMAT);
            $where['createdAt'] = "$beforeSevenDays - $today";

            $changed = true;
        }

        if (!isset($where['active']) || null == $where['active']) {
            $where['active'] = "1";

            $changed = true;
        }

        if ($changed) {
            session()->put(
                $this->cacheKey,
                $where
            );
        }

        return $where;
    }
}
