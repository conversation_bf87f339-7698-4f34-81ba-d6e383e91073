<?php

namespace Modules\Payments\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\AbstractTask;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleDecisionReason;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskHistory;
use Modules\Common\Models\SaleTaskType;
use Modules\Head\Repositories\BankAccountRepository;
use Modules\Head\Services\BankAccountService;
use Modules\Head\Services\LoanService;
use Modules\Payments\Application\Actions\CancelPaymentAction;
use Modules\Payments\Application\Actions\ConfirmEasyPayDeliveryAction;
use Modules\Payments\Application\Actions\CreateBankTransferAction;
use Modules\Payments\Application\Actions\ExportPaymentTasksAction;
use Modules\Payments\Application\Actions\PaymentTaskDataAction;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToBankAction;
use Modules\Payments\Application\Actions\Task\ProcessTaskAction;
use Modules\Payments\Application\Actions\Task\RequestEasyPayRefundAction;
use Modules\Payments\Exports\PaymentTasksExport;
use Modules\Payments\Http\Requests\PaymentFileUploadRequest;
use Modules\Payments\Http\Requests\PaymentSearchRequest;
use Modules\Payments\Http\Requests\PaymentTaskAttemptRequest;
use Modules\Payments\Imports\PaymentsImport;
use Modules\Payments\Services\PaymentService;
use Modules\Payments\Services\PaymentTaskAttemptService;
use Modules\Payments\Services\PaymentTaskService;
use Modules\ThirdParty\Services\CurlEasyPayService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class PaymentTaskController extends BaseController
{
    const MAX_ATTEMPT_FOR_UNRECEIVED_MONEY = 3;

    protected LoanService $loanService;
    protected PaymentService $paymentService;
    protected PaymentTaskService $paymentTaskService;
    protected BankAccountService $bankAccountService;
    protected PaymentTaskAttemptService $paymentTaskAttemptService;

    private string $indexRoute = 'payment.paymentsTasks.list';

    public function __construct(
        LoanService $loanService,
        PaymentService $paymentService,
        BankAccountService $bankAccountService,
        PaymentTaskService $paymentTaskService,
        PaymentTaskAttemptService $paymentTaskAttemptService
    ) {
        parent::__construct();

        $this->loanService = $loanService;
        $this->paymentService = $paymentService;
        $this->paymentTaskService = $paymentTaskService;
        $this->bankAccountService = $bankAccountService;
        $this->paymentTaskAttemptService = $paymentTaskAttemptService;
    }

    public function list(
        PaymentSearchRequest $request,
        PaymentTaskDataAction $paymentTaskDataAction
    ): View {
        $filters = collect($request->validated())->filter()->toArray();

        $data = $paymentTaskDataAction->execute($filters);

        return view('payments::payment-tasks.list', $data);
    }

    public function export(PaymentSearchRequest $request, ExportPaymentTasksAction $action): BinaryFileResponse
    {
        $rows = $action->execute(collect($request->validated())->filter()->toArray());

        return Excel::download(
            new PaymentTasksExport($rows),
            'payment_tasks_export_' . time() . '.xlsx'
        );
    }

    public function refresh(
        PaymentSearchRequest $request,
        PaymentTaskDataAction $paymentTaskDataAction
    ): View {
        $filters = collect($request->validated())->filter()->toArray();
        $data = $paymentTaskDataAction->execute($filters);

        $data['paymentTasks']->withPath(route('payment.paymentsTasks.list'));

        return view('payments::payment-tasks.list-table', $data);
    }

    public function orderingTasksProcessing(
        PaymentTask $paymentTask,
        ProcessTaskAction $action
    ): View|RedirectResponse {

        $administrator = Auth::user();
        $stopMessage = $administrator->getProcessingTaskNotification('payment', $paymentTask->payment_task_id);
        if (!empty($stopMessage)) {
            return redirect()
                ->route('payments::payment-tasks.list', [])
                ->with('fail', $stopMessage);
        }

        if (!$administrator->can('processPaymentTask', [$paymentTask])) {
            return back()->with('fail', 'Already processing by another admin');
        }

        $params = [];
        if (
            in_array(
                $paymentTask->name,
                [
                    PaymentTaskNameEnum::DISTRIBUTE_PAYMENT,
                    PaymentTaskNameEnum::FIND_CLIENT,
                    PaymentTaskNameEnum::CORRECT_THE_AMOUNT,
                    PaymentTaskNameEnum::NONE,
                ]
            )
        ) {
            $params = ['payment_task_id' => $paymentTask->getKey()];
            if ($paymentTask->client_id) {
                $params['client_id'] = $paymentTask->client_id;
            } else {
                $pin = $paymentTask->payment->extractPin();
                if (!empty($pin)) {
                    $client = Client::where('pin', trim($pin))->first();
                    if (!empty($client->client_id)) {
                        $params['client_id'] = $client->client_id;
                    }
                }
            }
        }

        // attempt to process
        $data['paymentTask'] = $action->execute($paymentTask);

        $payment = $paymentTask->payment;

        $data['warning'] = '';
        $saleTask = $paymentTask->getRefundSaleTask();
        if (!empty($saleTask->sale_task_id)) {
            $tasksCount = $this->getPrevTaskCount($payment->loan_id);
            if ($tasksCount >= self::MAX_ATTEMPT_FOR_UNRECEIVED_MONEY) {
                $data['warning'] = 'Ако обработиш задачата по какъвто и да е начин, кредита ще бъде отказан.';
            }
        }

        if ($paymentTask->direction == PaymentTask::DIRECTION_IN) {
            return redirect()->route('payment.manual-payment.index', $params);
        }


        $data['bankAccounts'] = app(BankAccountRepository::class)->getBankAccountsByOfficeId($paymentTask->loan);


        $paymentBankAccount = null;
        if (!empty($payment->bank_account_id)) {
            $paymentBankAccount = $payment->bankAccount;
        }
        $data['paymentBankAccount'] = $paymentBankAccount;
        $data['payment'] = $payment;


        return view('payments::payment-tasks.outgoing-payment', $data);
    }

    public function exitTask(PaymentTask $paymentTask): RedirectResponse
    {
        $this->paymentTaskService->exitTask($paymentTask);

        return redirect()->back();
    }

    public function import(PaymentFileUploadRequest $request): RedirectResponse
    {
        $import = new PaymentsImport($this->loanService);
        Excel::import($import, $request->file('bankUpload'));

        $importedPayments = $import->getPayments();
        if (empty($importedPayments)) {
            return back()->with('fail', __('payments::paymentTask.paymentsUploadEmptyOrWrongFormat'));
        }

        $duplicates = $created = $createdTask = $errors = 0;
        foreach ($importedPayments as $importedPayment) {
            DB::beginTransaction();
            try {
                $resp = app(CreateBankTransferAction::class)->execute($importedPayment);

                DB::commit();
            } catch (\Throwable $e) {
                DB::rollBack();

                Log::debug(
                    'Bank import failed, #' . $importedPayment->getKey()
                    . ' - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
                );

                $errors++;

                continue;
            }

            if ($resp === 'duplicate') {
                $duplicates++;
            }
            if ($resp === 'success') {
                $created++;
            }
            if ($resp === 'task') {
                $createdTask++;
            }
            if ($resp === 'error') {
                $errors++;
            }
        }

        $result = [
            'Общо' => count($importedPayments),
            'Съществуващи' => $duplicates,
            'Създадени плащания' => $created,
            "Създадени задачи" => $createdTask,
            "Грешки" => $errors,
        ];


        $resultString = '';
        foreach ($result as $key => $value) {
            $resultString .= "$key: $value; ";
        }
        $resultString = rtrim($resultString, '; ');


        return back()->with('success', $resultString);
    }

    public function storeAttempt(
        PaymentTask $paymentTask,
        PaymentTaskAttemptRequest $request
    ): RedirectResponse|JsonResponse {
        $data = $request->validated();

        $this->paymentTaskAttemptService->storePaymentTaskAttempt($paymentTask, $data);

        if ($request->ajax()) {
            $request->session()->put(
                'successfullyProcessed',
                __('payments::paymentTask.successfullyProcessedPaymentTask')
            );
            $request->session()->put('success', __('payments::paymentTask.successfullyProcessedPaymentTask'));

            return response()->json([
                'redirectTo' => route('payment.paymentsTasks.list')
            ]);
        }

        return redirect()
            ->route('payment.paymentsTasks.list')
            ->with('successfullyProcessed', __('payments::paymentTask.successfullyProcessedPaymentTask'))
            ->with('success', __('payments::paymentTask.successfullyProcessedPaymentTask'));
    }

    public function storeInAttempt(
        PaymentTask $paymentTask,
        PaymentTaskAttemptRequest $request
    ): RedirectResponse|JsonResponse {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->paymentTaskAttemptService->storeIncomingPaymentTaskAttempt($paymentTask, $data);

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            return response()->json([
                'redirectTo' => route('payment.paymentsTasks.list'),
                'error' => $e->getMessage(),
            ]);
        }

        if ($request->ajax()) {
            $request->session()->put(
                'successfullyProcessed',
                __('payments::paymentTask.successfullyProcessedPaymentTask')
            );
            $request->session()->put('success', __('payments::paymentTask.successfullyProcessedPaymentTask'));

            return response()->json([
                'redirectTo' => route('payment.paymentsTasks.list')
            ]);
        }

        return redirect()
            ->route('payment.paymentsTasks.list')
            ->with('successfullyProcessed', __('payments::paymentTask.successfullyProcessedPaymentTask'))
            ->with('success', __('payments::paymentTask.successfullyProcessedPaymentTask'));
    }

    //  =================================== PAYMENT TASK DETAILS PAGE ===================================

    /**
     * Бутон - Откажи кредит (/payments/payment-tasks-processing/XXX) - BANK & EASYPAY
     */
    public function cancelTaskAndLoan(
        PaymentTask $paymentTask,
        Request $request,
        CancelPaymentAction $action
    ): RedirectResponse {
        if (empty($paymentTask->loan_id)) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Не е намерен заем на плащането.',
                ]);
        }

        if ($paymentTask->isStatusDone()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Задачата вече е приключена, не може да се обработва повторно',
                ]);
        }

        $loan = $this->loanService->getLoanById($paymentTask->loan_id);
        if (empty($loan->loan_id)) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Заем №' . $paymentTask->loan_id . ' не е намерен в системата',
                ]);
        }


        DB::beginTransaction();
        try {
            $request->validate([
                'cancelLoan.reason' => 'required|string|min:5',
                'cancelLoan.description' => 'nullable|string',
            ]);
            $requestData = $request->all();

            $action->execute(
                $loan,
                $paymentTask->payment_id,
                $requestData['cancelLoan']['description'] ?? '',
                $requestData['cancelLoan']['reason'] ?? null,
            );

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            return back()->with('fail', 'Грешка при отказ на заема');
        }


        return redirect()->route($this->indexRoute)
            ->with([
                'success' => 'Заем е отказан. Задачата е успешно приключена.',
            ]);
    }

    /**
     * Бутон - Маркирай като обработен (/payments/payment-tasks-processing/XXX) - BANK|EASYPAY
     */
    public function approveOutgoingTask(
        Request $request,
        PaymentTask $paymentTask
    ): RedirectResponse {

        if ($paymentTask->isStatusDone()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Задачата вече е приключена, не може да се обработва повторно',
                ]);
        }

        $payment = $paymentTask->payment;
        if (empty($payment->payment_id)) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Не е намерено плащане за тази задача.',
                ]);
        }

        if ($payment->isEasypay()) {
            // тука интересен случай:
            // ако изпратихме автоматично, статус ще бъде SENT
            // ако изпратихме ръчно, статус ще бъде SENT
            // И сега при обработка на не усвоени пари го слагаме в DELIVERED
            // НО има случай когато парите не са били изпратени и тука пак го слагаме в DELIVERED
            // в едини случай REFUND е възможен, в другия НЕ, защото не сме правили изпращане през наше api

            if ($payment->status == PaymentStatusEnum::EASY_PAY_SENT) { // Positive finish of task - Unreceived money

                DB::beginTransaction();
                try {
                    $saleTask = $paymentTask->getRefundSaleTask();

                    if (empty($saleTask->sale_task_id)) {

                        // just update payment status + close payment task
                        $action = app(ConfirmEasyPayDeliveryAction::class);
                        $action->execute($payment->payment_id);

                        $responseMsg = 'Задача е маркирана като обработена. Статус на плащане е променен успешно.';

                    } else {
                        // in case of - UNRECEIVED MONEY
                        // for 1 and 2 attepmt, we just close a payment task
                        // else we do auto-refund, return money and close the loan
                        $tasksCount = $this->getPrevTaskCount($payment->loan_id);
                        if ($tasksCount >= self::MAX_ATTEMPT_FOR_UNRECEIVED_MONEY) {

                            // proceed refund
                            $refundAction = app(RequestEasyPayRefundAction::class);
                            $result = $refundAction->execute($payment, $paymentTask);

                            $responseMsg = 'Задача е маркирана като обработена. Kредита е отказан след ' . $tasksCount . ' опит за обработка.';

                        } else {

                            // just close a payment task
                            $action = app(ConfirmEasyPayDeliveryAction::class);
                            $action->closePaymenTask($payment->payment_id);

                            $responseMsg = 'Задача е маркирана като обработена.';
                        }
                    }

                    // close sale task if exists
                    if (!empty($saleTask->sale_task_id)) {
                        $now = Carbon::now();
                        $saleTask->last_status_update_date = $now;
                        $saleTask->status = AbstractTask::TASK_STATUS_DONE;
                        $saleTask->save();

                        // also create sale attempt
                        $duration2 = $now->diffInSeconds(Carbon::parse($saleTask->processed_at));
                        $saleAttempt = new SaleAttempt();
                        $saleAttempt->fill([
                            'administrator_id' => getAdminId(),
                            'sale_task_id' => $saleTask->sale_task_id,
                            'office_id' => $payment->office_id,
                            'sale_decision_id' => SaleDecision::SALE_DECISION_ID_OTHER,
                            'sale_decision_reason_id' => SaleDecisionReason::SALE_DECISION_REASON_ID_NO_INTEREST,
                            'start_at' => $saleTask->processed_at,
                            'end_at' => $now,
                            'total_time' => $duration2,
                            'last' => 1,
                            'processing_time' => $duration2,
                            'comment' => 'Избрано: (Маркирай като обработена задача)',
                        ]);
                        $saleAttempt->save();
                    }

                    DB::commit();
                } catch (\Throwable $e) {
                    DB::rollBack();

                    Log::debug('ERROR PaymentTaskController->approveOutgoingTask(epay) - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

                    return back()->with('fail', 'Грешка при маркирането на плащането');
                }

                return redirect()->route($this->indexRoute)
                    ->with([
                        'success' => (!empty($responseMsg) ? $responseMsg : 'Задача е маркирана като обработена. Статус на плащане е променен успешно.'),
                    ]);

            } else { // STATUS: NEW or FAILED

                DB::beginTransaction();
                try {
                    // change status of payment from NEW to SENT (become visible in all payments)
                    // activate loan, accounting row, etc
                    $action = app(ConfirmEasyPaySendingAction::class);
                    $action->execute($payment);

                    // Mandatory change status DELIVERED, since money sent manually
                    // and if payment will stay 3 days and refund logic will be created,
                    // BUT refund is impossible in this case since money is not set via api
                    $payment->status = PaymentStatusEnum::DELIVERED;

                    // Also we put this flag, to know that loan activating without real sending via api
                    $payment->skip_easypay_sending = 1;

                    $payment->save();

                    DB::commit();
                } catch (\Throwable $e) {
                    DB::rollBack();

                    Log::debug('ERROR PaymentTaskController->approveOutgoingTask(epay2) - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

                    return back()->with('fail', 'Грешка при маркирането на плащането');
                }

                return redirect()->route($this->indexRoute)
                    ->with([
                        'success' => 'Задача е маркирана като обработена. Заем е активиран.',
                    ]);
            }
        }

        if ($payment->isBank()) {
            DB::beginTransaction();
            try {
                $data = $request->validate([
                    'bank_account_id' => 'sometimes|integer|exists:bank_account,bank_account_id'
                ]);
                $bankAccountId = $data['bank_account_id'] ?? null;

                // change status of payment from NEW to DELIVERED (become visible in all payments)
                // activate loan
                $action = app(ConfirmTransferToBankAction::class);
                $action->execute($payment, $bankAccountId);

                DB::commit();
            } catch (\Throwable $e) {
                DB::rollBack();

                Log::debug('ERROR PaymentTaskController->approveOutgoingTask(bank) - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

                return back()->with('fail', 'Грешка при маркирането на плащането. ' . $e->getMessage());
            }

            $msg = __('Задача е маркирана като обработена. Заем е активиран.');

            return to_route($this->indexRoute)->with('success', $msg);
        }


        return redirect()
            ->back()
            ->with([
                'fail' => 'Грешен начин на плащане, позволени са: По банка и Изипей',
            ]);
    }

    private function getPrevTaskCount(int $loanId): int
    {
        $count1 = (int) SaleTask::withTrashed()
            ->where('loan_id', $loanId)
            ->where('sale_task_type_id', SaleTaskType::SALE_TASK_TYPE_ID_UNRECEIVED_MONEY)
            ->count();

        $count2 = (int) SaleTaskHistory::withTrashed()
            ->where('loan_id', $loanId)
            ->where('sale_task_type_id', SaleTaskType::SALE_TASK_TYPE_ID_UNRECEIVED_MONEY)
            ->count();

        return ($count1 + $count2);
    }

    /**
     * Бутон - Опитай да изпратиш пак (/payments/payment-tasks-processing/XXX) - EASYPAY
     */
    public function reSendEasypayRequest(
        PaymentTask $paymentTask,
        Loan $loan
    ) {
        if ($paymentTask->isStatusDone()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Задачата вече е приключена, не може да се обработва повторно',
                ]);
        }

        $payment = $paymentTask->payment;
        if (empty($payment->payment_id)) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Не е намерено плащане за тази задача.',
                ]);
        }

        if (!$payment->canBeSent()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Плащането е в грешен статус, не може да бъде изпратено до Изипей',
                ]);
        }


        // try to send to easypay
        try {
            $curlEasyPayService = app(CurlEasyPayService::class);
            $result = $curlEasyPayService->sendMoney(
                $loan->client,
                $loan,
                $payment,
                $paymentTask->amount,
                $loan->getRefinancedId()
            );
        } catch (\Throwable $exception) {
            $msg = 'Failed to send money to Easypay: '
                . $exception->getMessage() . ' '
                . $exception->getFile() . ':'
                . $exception->getLine();

            Log::debug('ERROR PaymentTaskController->reSendEasypayRequest() - ' . $msg);

            return redirect()
                ->back()
                ->with([
                    'fail' => $msg,
                ]);
        }

        // take decision how to proceed, based on result from easypay
        $isSent = $result['success'] ?? false;
        if ($isSent) {
            DB::beginTransaction();
            try {
                // change status of payment from NEW to SENT (become visible in all payments)
                // activate loan
                $action = app(ConfirmEasyPaySendingAction::class);
                $action->execute($payment);

                DB::commit();
            } catch (\Throwable $e) {
                DB::rollBack();

                return back()->with('fail', 'Грешка при изпращане на пари към Изипей');
            }

            return redirect()->route($this->indexRoute)
                ->with([
                    'success' => 'Парите са успешно изпратени към Изипей. Заем е активиран.',
                ]);
        }

        return redirect()
            ->back()
            ->with([
                'fail' => 'Грешка при изпращане. Опитай след малко пак.',
            ]);
    }

    /**
     * Бутон - Стонирай плащането - EASYPAY     *
     */
    public function refundEasypay(PaymentTask $paymentTask)
    {
        $payment = $paymentTask->payment;
        if (!$payment->canBeRefunded()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Плащането е с грешен статус и не може да бъде стонирано',
                ]);
        }

        if (!$paymentTask->isTaskForRefund()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Грешен тип на задачата, не съответсва на назначението си. Отказ на стониране.',
                ]);
        }


        $refundAction = app(RequestEasyPayRefundAction::class);
        $result = $refundAction->execute($payment, $paymentTask);

        if ('OK' == $result) {
            return redirect()->route('sales.saleTask.list')
                ->with([
                    'success' => 'Плащането е стонирано успешно.',
                ]);
        }


        if ('PROCESSING' == $result) {
            return redirect()
                ->back()
                ->with([
                    'success' => 'Стониране започна. Проверете статус на стонирането.',
                ]);
        }


        return redirect()
            ->back()
            ->with([
                'fail' => 'Грешка при стонирането. Не могат да се върнат парите от Изипей. Опитай отново.',
            ]);
    }

    /**
     * Бутон - Провери статус на стонирането - EASYPAY     *
     */
    public function refundStateEasypay(PaymentTask $paymentTask)
    {
        $payment = $paymentTask->payment;
        if (!$payment->canSentRefundState()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Плащането е с грешен статус, проверка на стониране е невъзможна',
                ]);
        }

        if (!$paymentTask->isTaskForRefundState()) {
            return redirect()
                ->back()
                ->with([
                    'fail' => 'Грешен тип на задачата, не съответсва на назначението си. Отказ на проверка на стониране.',
                ]);
        }


        $refundAction = app(RequestEasyPayRefundAction::class);
        $result = $refundAction->execute($payment, $paymentTask, true);

        if ('OK' == $result) {
            return redirect()->route('sales.saleTask.list')
                ->with([
                    'success' => 'Плащането е стонирано успешно.',
                ]);
        }


        if ('PROCESSING' == $result) {
            return redirect()
                ->back()
                ->with([
                    'success' => 'Стониране все още не е приключило. Проверете статус на стонирането след малко.',
                ]);
        }


        return redirect()
            ->back()
            ->with([
                'fail' => 'Грешка при стонирането. Не могат да се върнат парите от Изипей. Опитай отново.',
            ]);
    }
}
