<?php

namespace Modules\Payments\Console;

use Illuminate\Support\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Models\CronLog;
use Modules\Common\Models\EasyPayAttempt;
use Modules\Head\Repositories\ClientRepository;
use Modules\ThirdParty\Libraries\EmailProvider;
use Symfony\Component\Console\Command\Command;

// php artisan script:payment-sources-check
class EasyPayAttemptHealthCheck extends CommonCommand
{
    protected $name = 'script:payment-sources-check';
    protected $signature = 'script:payment-sources-check {source_name?}';
    protected $description = 'Search for unhandled easypay payments and notify about them';

    private ?CronLog $lastCronLog = null;

    public function __construct(
        private readonly ClientRepository $clientRepository,
    )
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $cronLogName = $this->getClassName() . '::' . __FUNCTION__;
        $this->lastCronLog = CronLog::where('command', $cronLogName)->get()->last();
        $this->startLog($cronLogName);
        $sourceString = empty($this->argument('source_name'))
            ? null
            : $this->argument('source_name');
        if ($sourceString) {
            $source = PaymentSourceEnum::tryFrom($sourceString);
            if (!$source) {
                $this->error('Invalid source:' . $sourceString);
                $this->finishLog();
                return Command::INVALID;
            }
            $this->finishLog();
            return $this->checkSource($source) ? Command::SUCCESS : Command::FAILURE;
        }

        $emailsSent = true;
        foreach (PaymentSourceEnum::cases() as $source) {
            $emailsSent = $emailsSent && $this->checkSource($source);
        }
        $this->finishLog();
        return $emailsSent ? Command::SUCCESS : Command::FAILURE;
    }

    public function checkSource(PaymentSourceEnum $source): bool
    {
        $this->line('checking source:' . $source->name);
        $messageData = match ($source) {
            PaymentSourceEnum::EASY_PAY_API => $this->checkEasyPayApi(),
            default => $this->underConstruction()
        };
        return $this->sendEmail($messageData);
    }

    private function checkEasyPayApi(): array
    {
        $fromDate = $this->lastCronLog ? $this->lastCronLog->updated_at : Carbon::parse('2023-09-11');
        $lost = EasyPayAttempt
            ::whereNull('payment_id')
            ->where('created_at', '>', $fromDate)
            ->get();
        if (!$lost->count()) {
            return [];
        }
        $messageData = [
            'source' => PaymentSourceEnum::EASY_PAY_API->name,
            'total' => $lost->count(),
            'details' => []
        ];
        /** @var EasyPayAttempt $easyPayAttempt */
        foreach ($lost as $easyPayAttempt) {
            $messageData['details'][] = [
                'time' => $easyPayAttempt->created_at->toDateTimeString(),
                'pin' => $easyPayAttempt->pin,
                'amount' => $easyPayAttempt->amount
            ];
        }
        return $messageData;
    }

    private function underConstruction(): array
    {
        //do nothing at the moment, will be deleted when every source will have its own method
        return [];
    }

    private function sendEmail(array $messageData): bool
    {
        if (!$messageData) {
            return true;
        }
        $receivers = config('mail.log_monitor')['receivers'];
        $this->table(['time', 'pin', 'amount'], $messageData['details']);
        $htmlData = '';
        foreach ($messageData['details'] as $row) {
            $htmlData .= '<tr><td>' . $row['time'] . '</td><td>' . $row['pin'] . '</td><td>' . $row['amount'] . '</td></tr>';
        }
        $htmlTable = '<table>
<thead>
<tr><td>' . $messageData['source'] . '</td><td colspan="2">Total Errors:' . $messageData['total'] . '</td></tr>
<tr><th>Time</th><th>PIN</th><th>Amount</th></tr>
</thead>
<tbody>' . $htmlData . '</tbody>
</table>';
        $result = true;
        foreach ($receivers as $receiver) {
            $result = $result && (new EmailProvider)->sendEmail(
                    '<EMAIL>',
                    $receiver,
                    $messageData['source'] . ' Problems',
                    $htmlTable
                );
        }
        return $result;
    }


}
