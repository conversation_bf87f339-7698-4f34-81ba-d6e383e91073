<?php

namespace Modules\Payments\Domain\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\Payment;

class LoanPayoutWasDelivered
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public Payment $payment, public ?object $totals = null){}
}
