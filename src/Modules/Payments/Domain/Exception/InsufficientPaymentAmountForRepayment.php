<?php

namespace Modules\Payments\Domain\Exception;

use Modules\Common\Domain\Exceptions\DomainException;

class InsufficientPaymentAmountForRepayment extends DomainException
{
    public function __construct(int $paymentAmount, int $repaymentAmount)
    {
        $this->baseMessage = sprintf('Insufficient amount %d to repay debt of %d', $paymentAmount, $repaymentAmount);
        parent::__construct(get_defined_vars());
    }

    public function getFrontEndMessage(): ?string
    {
        $params = $this->params();
        return __("Can't repaid loan because amount to repaid :amountToRepaid is larger than :inputAmount", [
            'amountToRepaid' => intToFloat($params['repaymentAmount']),
            'inputAmount' => intToFloat($params['paymentAmount'])
        ]);
    }
}
