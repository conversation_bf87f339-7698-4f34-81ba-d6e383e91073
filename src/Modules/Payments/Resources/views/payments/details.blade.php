@extends('layouts.app')
@section('content')
    @php
        use Modules\Common\Enums\PaymentDistributionAimEnum;
            /**
        * @var \Modules\Common\Models\Payment $payment
         * @var \Modules\Common\Models\Client $client
         * @var \Modules\Common\Models\Loan $loan
         * @var \Modules\Common\Models\Office $office
     * @var \Modules\Common\Models\UnclaimedMoney $unclaimedMoney
 * @var \Modules\Common\Models\PaymentDistribution $paymentDistribution
         */
    @endphp
    <div class="row">
        <div class="col-lg-4">
            <x-card title="{{__('table.PaymentDetails')}}">
                <x-table>
                    <tr>
                        <th>{{__('payments::payments.paymentNumber')}}</th>
                        <td>{{$payment->getKey()}}</td>
                    </tr>
                    <tr>
                        <th>{{__('table.LoanId')}}</th>
                        <td>{{$loan?->getKey()}}</td>
                    </tr>
                    <tr>
                        <th>{{__('table.LoanAmountApproved')}}</th>
                        <td>{{intToFloat($loan?->amount_approved)}}</td>
                    </tr>
                    <tr>
                        <th>{{__('table.Status')}}</th>
                        <td class="{{ $payment->isActive() ? 'text-success' : 'text-danger' }}">
                            {{ $payment->isActive() ? __('table.Active') : __('table.Deleted') }}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('payments::payments.paymentFrom')}}</th>
                        <td>{{$client->getFullName()}}</td>
                    </tr>
                    <tr>
                        <th>{{__('Created at')}}</th>
                        <td>{{$payment->created_at->format('d.m.Y H:i')}}</td>
                    </tr>
                    <tr>
                        <th>{{__('payments::payments.paymentSource')}}</th>
                        <td>
                            @if($payment->isCash())
                                В брой {{$office->name}}
                            @else
                                {{$payment->bankAccount?->name}}
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('payments::payments.paymentType')}}</th>
                        <td>{{$payment->isManualLabel()}}</td>
                    </tr>
                    <tr>
                        <th>{{__('head::clientCard.paidAmount')}}</th>
                        <td>{{intToFloat($payment->amount)}}</td>
                    </tr>
                    <tr>
                        <th>{{__('table.UnclaimedMoney')}}</th>
                        <td>{{ intToFloat($unclaimedMoney?->amount ?? 0)  }}</td>
                    </tr>
                    <tr>
                        <th>{{__('payments::payments.paymentInfo')}}</th>
                        <td>{{$payment->description}}</td>
                    </tr>
                </x-table>
            </x-card>
        </div>
        <!-- End ./col-lg-6 -->

        <div class="col-lg-4">
            <x-card title="{{__('table.LoanDistribution')}}">
                <x-table>
                    <tr>
                        <th>{{__('head::installment.principal')}}</th>
                        <td>{{$dbCarton['repaid_amount_principal']}}</td>
                    </tr>
                    <tr>
                        <th>{{__('head::installment.interest')}}</th>
                        <td>{{$dbCarton['repaid_amount_interest']}}</td>
                    </tr>
                    <tr>
                        <th>{{__('head::installment.penalty')}}</th>
                        <td>
                            {{$dbCarton['repaid_amount_penalty']}}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('head::clientCard.lateInterest')}}</th>
                        <td class="@if($dbCarton['repaid_amount_late_interest'] > 0) text-danger @endif">
                            {{$dbCarton['repaid_amount_late_interest']}}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('head::clientCard.latePenalty')}}</th>
                        <td class="@if($dbCarton['repaid_amount_late_penalty'] > 0) text-danger @endif">
                            {{$dbCarton['repaid_amount_late_penalty']}}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('payments::fiscalDevice.collector')}}</th>
                        <td>
                            {{$dbCarton['repaid_collector_amount_taxes']}}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('head::clientCard.Other')}}</th>
                        <td>
                            {{$dbCarton['repaid_amount_taxes']}}
                        </td>
                    </tr>
                    <tr>
                        <th>{{__('head::clientCard.Total')}}</th>
                        <th>{{$dbCarton['repaid_amount_total']}}</th>
                    </tr>
                </x-table>
            </x-card>
        </div>
        <!-- End ./col-lg-6 -->
    </div>
    <!-- End ./row -->

    <div class="row">
        <div class="col-lg-12">
            <x-card title="{{__('payments::payments.paymentDelivery')}}">

                <x-table>
                    <x-slot:head>
                        <tr>
                            <th>{{__('table.PaymentId')}}</th>
                            <th>{{__('table.Identifier')}}</th>
                            <th>{{__('table.Description')}}</th>
                            <th>{{__('table.Date')}}</th>
                            <th>{{__('payments::payments.Distribution date')}}</th>
                            <th>{{__('payments::payments.Distributed Amount')}}</th>
                            <th>{{__('payments::payments.Distributed Amount Before')}}</th>
                            <th>{{__('payments::payments.Distributed Amount After')}}</th>
                        </tr>
                    </x-slot:head>

                    @foreach($payment->paymentDistribution as $paymentDistribution)
                        @php
                            $description = $paymentDistribution->description;
                            if (!$description) {
                                $description = match ($paymentDistribution->aim) {
                                    PaymentDistributionAimEnum::LATE_PENALTY->value => __('head::clientCard.latePenalty'),
                                    PaymentDistributionAimEnum::LATE_INTEREST->value => __('head::clientCard.lateInterest'),
                                    default => null
                                };
                            }
                        @endphp
                        <tr class="@if($paymentDistribution->isLate()) text-danger @endif">
                            <td class="text-center">{{$paymentDistribution->payment_id}}</td>
                            <td>
                                {{$paymentDistribution->getLabel()}}
                            </td>
                            <td>{{ $description }}</td>
                            <td>{{formatDate($paymentDistribution->payment_date)}}</td>
                            <td>{{formatDate($paymentDistribution->distribution_date)}}</td>
                            <td class="text-success">{{amount($paymentDistribution->distributed_amount)}}</td>
                            <td>{{amount($paymentDistribution->distributed_amount_before)}}</td>
                            <td>{{amount($paymentDistribution->distributed_amount_after)}}</td>
                        </tr>
                    @endforeach
                </x-table>

            </x-card>
        </div>
    </div>
@endsection