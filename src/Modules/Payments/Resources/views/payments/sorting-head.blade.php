<tr>
    @foreach($sortingArray as $table => $columns)
        @foreach($columns as $column => $translate)

            @if(is_array($translate))
                <th scope="col" class="text-center sorting">
                    <input type="text" name="order[{{$column}}]"
                           value="{{session($cacheKey . '.order.'.$column) ?: 'desc'}}">
                    {{$translate['no']}}
                    <i class="fa {{session($cacheKey . '.order.'.$column) ?
                                                            'fa-sort-'.session($cacheKey . '.order.'.$column) : ''}}"
                       aria-hidden="true"></i>
                </th>
            @else
                <th scope="col" class="text-center sorting not-bold">
                    <input type="text" name="order[{{$table}}][{{$column}}]"
                           value="{{session($cacheKey . '.order.'.$table.'.'.$column) ?: 'desc'}}">
                    {{$translate}}
                    <i class="fa {{session($cacheKey . '.order.'.$table.'.'.$column) ?
                                                            'fa-sort-'.session($cacheKey . '.order.'.$table.'.'.$column) : ''}}"
                       aria-hidden="true"></i>
                </th>
            @endif
        @endforeach
    @endforeach
    <th scope="col">
        {{__('table.Edit')}}
    </th>
</tr>
