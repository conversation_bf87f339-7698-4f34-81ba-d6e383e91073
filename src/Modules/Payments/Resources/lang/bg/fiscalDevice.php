<?php

return [
    'paid_principal, principal' => 'Главница',
    'principal' => 'Главница',
    'paid_principal' => 'Главница',
    'paid_interest, interest' => 'Лихва',
    'interest' => 'Лихва',
    'paid_accrued_interest' => 'Лихва',
    'paid_late_interest, late_interest' => 'Лихва просрочие',
    'late_interest' => 'Лихва просрочие',
    'paid_penalty, penalty' => 'Неустойка',
    'paid_accrued_penalty' => 'Неустойка',
    'penalty' => 'Неустойка',
    'paid_late_penalty, late_penalty' => 'Неустойка просрочие',
    'late_penalty' => 'Неустойка просрочие',

    'extend' => 'Такса удължаване',
    'manual' => 'Ръчно добавен разход',
    'mail' => 'Такса писмо',
    'sms' => 'Такса SMS',
    'collector' => 'Такса колектор',
    'tax_fee' => 'Такса удостоверение',
    'disbursed_loan' => 'Усвоен к-т :loan_id/:date',

    'notSet' => 'Няма зададено фискално устройство.',
    'error' => 'Грешка във връзката с фискалното устройство.',
    'noSuchCashTransaction' => 'Не съществува такава кеш транзакция в базата данни. Създай разход и сторно ръчно.',
    'noSuchFiscalReceipt' => 'Не съществува такава касова бележка в базата данни. Създай сторно ръчно.',

    'outstandingLatePenaltyAmount' => 'Неустойка за просрочие (Principal&Penalty)',
    'outstandingLateInterestAmount' => 'Неустойка за просрочие (Interest)',
    'outstandingPenaltyAmount' => 'Неустойка',
    'outstandingInterestAmount' => 'Лихва',
    'outstandingPrincipleAmount' => 'Главница',
    'payLoanFees' => 'Такси',
];
