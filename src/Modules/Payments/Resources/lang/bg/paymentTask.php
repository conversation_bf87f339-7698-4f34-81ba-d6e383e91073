<?php

return [
    'paymentTaskCreationFailed' => 'Неуспешно създадена задача за изходящо плащане по банков път.',
    'paymentTaskSuccessfulCreation' => 'Успешно създадена задача за изходящо плащане по банков път.',
    'paymentTaskAlreadyExists' => 'Вече съществува задача за плащане със същите параметри.',
    'paymentTaskApprovalFailed' => 'Неуспешно одобряване на задача за изходящо плащане',
    'paymentTaskClosingFailed' => 'Неуспешно затваряне на задача за изходящо плащане',
    'paymentTaskCancellationFailed' => 'Неуспешно отказване на задача за изходящо плащане',
    'paymentsUploadEmptyOrWrongFormat' => 'Файлът е празен или е грешен формат',
    'paymentTaskAlreadyProcessing' => 'Задачата вече се обработва',
    'paymentTaskAlreadyProcessed' => 'Задачата вече е обработена',
    'paymentTaskNotForManualDelivery' => 'Задачата не е за ръчно разнасяне',
    'successfullyProcessedPaymentTask' => 'Успешно обработена задача',
    'moreThanOneCreditWarningManualPayments' => 'Клиентът има повече от един кредит. Избери следващ, ако искаш да разпределиш плащане и по друг кредит',

    // easypay
    'couldNotRefundWrongStatus' => 'СТОРНО e невъзможно. Грешен статус на заявка.',
    'FailedToSendDataToEasypay' => 'Не успешно изпращане на заявка за СТОРНО',
    'RefundOk' => 'Парите сторнирани успешно',
    'RefundKo' => 'Изпратихме заявка за сторниране, изчакайте 1-2 минути и проверете статуса',
    'RefundNoInfo' => 'Няма информация за заявено сториниране на плащането.',
    'alreadyRefunded' => 'Плащането вече е сторнирано',
];
