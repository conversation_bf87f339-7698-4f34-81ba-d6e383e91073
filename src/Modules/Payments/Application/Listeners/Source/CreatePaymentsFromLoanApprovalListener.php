<?php

namespace Modules\Payments\Application\Listeners\Source;

use Illuminate\Support\Carbon;
use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDescriptionEnum;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Payment as DbModel;
use Modules\Common\Models\PaymentMethod;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\IncomingPayment;
use Modules\Payments\Domain\Entities\OutgoingPayment;
use Modules\Payments\Domain\Events\EasyPayPaymentWasPreparedForSending;

readonly class CreatePaymentsFromLoanApprovalListener
{
    public function __construct(
        private IncomingPayment $incomingPayment,
        private OutgoingPayment $outgoingPayment
    ) {}

    public function handle(LoanWasApproved $event): string
    {
        $loan = $event->loan->dbModel();


        // we should create incoming closing payments first in case of refinance
        $refinancedPayments = [];
        if ($loan->isRefinanceLoan()) {
            $refLoans = $loan->refinancing;

            if (!empty($refLoans) && $refLoans->count() > 0) {
                foreach ($refLoans as $refLoan) {

                    $refinancedPayments[] = $this->incomingPayment->new()->buildFromDtoForRefinanceOnApprove(
                        $this->createRefDto($loan, $refLoan)
                    );

                }

                if (empty($refinancedPayments)) {
                    throw new \Exception('No incoming payments created for refinance');
                }

                if ($refLoans->count() != count($refinancedPayments)) {
                    throw new \Exception('Failed to create incoming payments for all ref.loans');
                }
            }
        }


        // create out payment
        $outPayment = $this->outgoingPayment
            ->buildFromDto($this->createDto($loan))
            ->dbModel();


        // when in payments and out payment is created we need to set link between them
        if (!empty($refinancedPayments)) {
            foreach ($refinancedPayments as $refPayment) {
                $refPayment->parent_payment_id = $outPayment->payment_id;
                $refPayment->save();
            }
        }


        // 1) for easypay we try to send money, by EasyPayPaymentWasPreparedForSending -> SendEasyPayListener
        // 2) if sending is successful changeStatusToSent():
        //  -> change payment status to SENT (SendEasyPayListener)
        //  -> activate loan
        //  -> set loan ccr flags
        //  -> dispatch event LoanWasActivated -> client stats & client.new
        // 3) if sending is failed changeStatusToFailure():
        //  -> change status to FAILED
        //  -> create task for manual sending
        if ($outPayment->payment_method_id == PaymentMethod::PAYMENT_METHOD_EASYPAY) {
            EasyPayPaymentWasPreparedForSending::dispatch($outPayment);
        }



        return 'success';
    }

    public function createRefDto(DbLoan $refinancingLoan, DbLoan $refinancedLoan): NewPaymentDto
    {
        if (empty($refinancedLoan->getOriginal()['pivot_refinanced_due_amount'])) {
            throw new \Exception('Loan pivot refiance has no due amount value!');
        }

        $array = [
            'source'    => PaymentSourceEnum::LOAN_REFINANCE,
            'method'    => PaymentMethodEnum::OFFSET, // used for incoming refinance
            'purpose'   => PaymentPurposeEnum::LOAN_EARLY_REPAYMENT,
            'amount'    => $refinancedLoan->getOriginal()['pivot_refinanced_due_amount'], // comes from loan_refinance table

            'office_id' => $refinancingLoan->office_id,
            'client_id' => $refinancedLoan->client_id,
            'loan_id'   => $refinancedLoan->getKey(),
            'loan'      => $refinancedLoan,

            'created_by'      => getAdminId(),
            'description'     => PaymentDescriptionEnum::REFINANCE_INCOMING->text($refinancingLoan->loan_id),
            'bank_account_id' => $refinancingLoan->bank_account_id,
        ];

        return NewPaymentDto::from($array);
    }

    public function createDto(DbLoan $approvedLoan): NewPaymentDto
    {
        $array = [
            'source'  => PaymentSourceEnum::LOAN_APPROVAL,
            'method'  => $approvedLoan->paymentMethodEnum(),
            'purpose' => PaymentPurposeEnum::LOAN_PAYOUT,
            'amount'  => $approvedLoan->amount_approved,

            'client_id' => $approvedLoan->client_id,
            'office_id' => $approvedLoan->office_id,
            'loan_id'   => $approvedLoan->getKey(),
            'loan'      => $approvedLoan,

            'description' => PaymentDescriptionEnum::LOAN_REPAYMENT->text($approvedLoan->getKey()),
            'created_by'  => getAdminId(),
        ];

        return NewPaymentDto::from($array);
    }
}
