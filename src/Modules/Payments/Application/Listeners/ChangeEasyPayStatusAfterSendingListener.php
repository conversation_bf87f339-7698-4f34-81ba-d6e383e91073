<?php

namespace Modules\Payments\Application\Listeners;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Administrator;
use Modules\Payments\Domain\Entities\OutgoingPayment;
use Modules\ThirdParty\Events\EasyPaySendingHasFinished;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;

readonly class ChangeEasyPayStatusAfterSendingListener
{
    public function __construct() {}

    public function handle(EasyPaySendingHasFinished $event): void
    {
        DB::beginTransaction();
        try {
            $payment = $event->payment;

            if ($event->result) {
                $action = app(ConfirmEasyPaySendingAction::class);
                $action->execute($payment);
            } else {
                $outgoing = app(OutgoingPayment::class)->buildFromExisting($payment);
                $outgoing->changeStatusToFailure(Administrator::SYSTEM_ADMINISTRATOR_ID);
            }

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();

            $msg = $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine();

            $sql = "INSERT INTO easypay_sent_failed (payment_id, reason, created_at) VALUES (?, ?, NOW())";
            DB::statement($sql, [$payment->payment_id, $msg]);

            Log::debug('ChangeEasyPayStatusAfterSendingListener(' . ($event->result ? 'OK' : 'KO') . ')' . ' - ' . $msg);
        }
    }
}
