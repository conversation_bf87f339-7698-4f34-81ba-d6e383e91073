<?php

namespace Modules\Payments\Application\Actions;

use Kris\LaravelFormBuilder\Form;
use <PERSON>\LaravelFormBuilder\FormBuilder;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Head\Repositories\ClientRepository;
use Modules\Payments\Forms\CreateManualPaymentForm;

readonly class ManualPaymentIndexDataAction
{
    public function __construct(
        private ClientRepository $clientRepository,
        private FormBuilder      $formBuilder,
    ) {}

    public function execute($args = []): array
    {
        $client = null;
        $payment = null;
        $paymentTask = null;

        if (!empty($args['client_id'])) {
            $client = $this->clientRepository->getById((int) $args['client_id']);
        }
        if (!empty($args['payment_task_id'])) {
            $paymentTask = PaymentTask::find((int) $args['payment_task_id']);

            if (!empty($paymentTask->payment_id)) {

                if ($paymentTask->status->value == 'done') {
                    return ['redirectRoute' => 'payment.paymentsTasks.list'];
                }

                $payment = $paymentTask->payment;

                // try to get client from payment
                if (empty($client->client_id) && !empty($payment->client_id)) {
                    $client = $payment->client;
                }
            }
        }

        $data['client'] = $client;
        $data['payment'] = $payment;
        $data['paymentTask'] = $paymentTask;

        $data['createManualPaymentForm'] = $this->getCreateManualPaymentForm([
            'office_id'         => $payment?->office?->getKey(),
            'payment_amount'    => intToFloat($payment?->amount),
            'payment_method_id' => $payment?->payment_method_id,
            'bank_account_id'   => $payment?->bank_account_id,
            'payment_id'        => $payment?->payment_id,
            'payment_task_id'   => $paymentTask?->payment_task_id,
            'description'       => $payment?->description,
            'document_number'   => $payment?->document_number,
        ]);

        return $data;
    }

    protected function getCreateManualPaymentForm(array $data): Form
    {
        return $this->formBuilder->create(CreateManualPaymentForm::class, [
            'data-parsley-validate' => 'true',
            'route'  => 'payment.manual-payment.storeManualPayment',
            'method' => 'POST',
            'data'   => $data,
        ]);
    }
}
