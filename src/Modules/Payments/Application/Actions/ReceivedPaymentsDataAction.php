<?php

namespace Modules\Payments\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Carbon\Carbon;
use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Payments\FilterForms\ReceivedPaymentsFilterForm;
use Modules\Payments\Repositories\ReceivedPaymentsRepository;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReceivedPaymentsDataAction
{
    public function __construct(
        private readonly ReceivedPaymentsRepository $repo,
        private readonly FormBuilder $formBuilder
    ) {}

    public function execute(array $filters = [], int $limit = 10): array
    {
        $filters = $this->actualizeFilters($filters);

        $data = [];
        $data['rows'] = $this->repo->getRowsByFilters($filters, $limit);
        $data['filterForm'] = $this->getPaymentsFilterForm();

        return $data;
    }

    public function exportCsv(array $filters = [])
    {
        try {
            $filters = $this->actualizeFilters($filters);

            $fileName = 'received_payments_' . time() . '.csv';
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename=' . $fileName);
            header("Pragma: no-cache"); // optional
            header("Expires: 0"); // optional

            $fp = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));

            // Add column headers as the first row
            $headers = [
                __('table.PaymentId'),
                __('table.FilterByOffice'),
                __('table.PaymentMethodShort'),
                __('table.datePayment'),
                __('table.PaymentYear'),
                __('table.PaymentMonth'),
                __('table.Consultant'),
                __('table.Client'),
                __('table.FilterByPin'),
                __('table.FilterByLoanId'),
                __('table.TotalPaidAmount'),
                __('table.UnclaimedMoney'),
                __('table.Principal'),
                __('table.InterestSingular'),
                __('table.Penalty'),
                __('table.interestOverdue'),
                __('table.OtherIncomes'),
                __('table.Profit'),
                __('table.OverdueDays1'),
            ];
            fputcsv($fp, $headers);

            $builder = $this->repo->getBuilderByFilters($filters);
            $builder->getQuery()->chunkById(
                100,
                function ($rows) use ($fp) {
                    foreach ($rows as $row) {

                        // skip unknown payments, closed by reason - Не е превод от клиент - Задачата се маркира като приключена и пеймент се затваря, все едно го нямаше
                        if ($row->status == 'unknown' && $row->deleted == 1) {
                            continue;
                        }

                        $amounts = getExplainedPaymentDetails($row->delivery_string);

                        $r = [
                            $row->payment_id,
                            getOfficeName($row->office_id),
                            getPaymentAccountName($row->bank_account_id),
                            $row->creation_date,
                            $row->payment_year,
                            $row->payment_month,
                            $row->consultant_name,
                            $row->client_name,
                            $row->pin,
                            $row->loan_id,
                            $row->total_received_amount,
                            $row->unclaimed_amount,
                            (float) $amounts['principal'],
                            (float) $amounts['interest'],
                            (float) $amounts['penalty'],
                            (float) $amounts['late_interest'],
                            (float) $amounts['late_penalty_and_taxes'],
                            (float) $amounts['profit'],
                            $row->payment_overdue_days,
                        ];

                        fputcsv($fp, $r);
                    }
                },
                'payment.payment_id',
                'payment_id'
            );

            fclose($fp);
            exit;

        } catch (\Throwable $e) {
            dd($e);
        }
    }

    public function exportXlsx(array $filters = [])
    {
        try {
            $filters = $this->actualizeFilters($filters);

            // Create a new Spreadsheet object
            $spreadsheet = new Spreadsheet();

            // Get the active sheet
            $sheet = $spreadsheet->getActiveSheet();

            // Add column headers as the first row
            $headers = [
                __('table.PaymentId'),
                __('table.FilterByOffice'),
                __('table.PaymentMethodShort'),
                __('table.datePayment'),
                __('table.PaymentYear'),
                __('table.PaymentMonth'),
                __('table.Consultant'),
                __('table.Client'),
                __('table.FilterByPin'),
                __('table.FilterByLoanId'),
                __('table.TotalPaidAmount'),
                __('table.UnclaimedMoney'),
                __('table.Principal'),
                __('table.InterestSingular'),
                __('table.Penalty'),
                __('table.interestOverdue'),
                __('table.OtherIncomes'),
                __('table.Profit'),
            ];
            $sheet->fromArray([$headers], null, 'A1');
            $rowIndex = 1;

            $builder = $this->repo->getBuilderByFilters($filters);
            $builder->getQuery()->chunkById(
                100,
                function ($rows) use (&$sheet, &$rowIndex) {

                    foreach ($rows as $row) {

                        $amounts = getExplainedPaymentDetails($row->delivery_string);

                        $r = [
                            $row->payment_id,
                            getOfficeName($row->office_id),
                            getPaymentAccountName($row->bank_account_id),
                            $row->creation_date,
                            $row->payment_year,
                            $row->payment_month,
                            $row->consultant_name,
                            $row->client_name,
                            $row->pin,
                            $row->loan_id,
                            $row->total_received_amount,
                            $row->unclaimed_amount,
                            (float) $amounts['principal'],
                            (float) $amounts['interest'],
                            (float) $amounts['penalty'],
                            (float) $amounts['late_interest'],
                            (float) $amounts['late_penalty_and_taxes'],
                            (float) $amounts['profit'],
                        ];

                        // Populate data from the array
                        $rowIndex++;
                        foreach ($r as $columnIndex => $value) {

                            $sheet->setCellValueByColumnAndRow(
                                $columnIndex + 1,
                                $rowIndex,
                                $value
                            );
                        }
                    }
                },
                'payment.payment_id',
                'payment_id'
            );

            // Create a writer object
            $writer = new Xlsx($spreadsheet);

            // Set the headers to force download the file
            $fileName = 'received_payments_' . time() . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');

            // Write the spreadsheet to the output
            $writer->save('php://output');

            exit;

        } catch (\Throwable $e) {
            dd($e);
        }
    }

    public function exportXlsx2(array $filters = [])
    {
        try {

            // Set the headers to force download the file
            $fileName = 'received_payments_' . time() . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            header('Cache-Control: cache, must-revalidate');
            header('Pragma: public');

            ob_start();

            $filters = $this->actualizeFilters($filters);

            // Create a new Spreadsheet object
            $spreadsheet = new Spreadsheet();

            // Get the active sheet
            $sheet = $spreadsheet->getActiveSheet();

            // Add column headers as the first row
            $headers = [
                __('table.PaymentId'),
                __('table.FilterByOffice'),
                __('table.PaymentMethodShort'),
                __('table.datePayment'),
                __('table.PaymentYear'),
                __('table.PaymentMonth'),
                __('table.Consultant'),
                __('table.Client'),
                __('table.FilterByPin'),
                __('table.FilterByLoanId'),
                __('table.TotalPaidAmount'),
                __('table.UnclaimedMoney'),
                __('table.Principal'),
                __('table.InterestSingular'),
                __('table.Penalty'),
                __('table.interestOverdue'),
                __('table.OtherIncomes'),
                __('table.Profit'),
                __('table.OverdueDays1'),
            ];
            $sheet->fromArray([$headers], null, 'A1');
            $rowIndex = 1;

            $builder = $this->repo->getBuilderByFilters($filters);
            $builder->getQuery()->chunkById(
                100,
                function ($rows) use (&$sheet, &$rowIndex) {

                    foreach ($rows as $row) {

                        // skip unknown payments, closed by reason - Не е превод от клиент - Задачата се маркира като приключена и пеймент се затваря, все едно го нямаше
                        if ($row->status == 'unknown' && $row->deleted == 1) {
                            continue;
                        }

                        // skip canceled easypay refinance
                        if ($row->status == 'canceled' && $row->direction == 'in' && $row->payment_method_id == 4) {
                            continue;
                        }

                        $amounts = getExplainedPaymentDetails($row->delivery_string);

                        $r = [
                            $row->payment_id,
                            getOfficeName($row->office_id),
                            getPaymentAccountName($row->bank_account_id),
                            $row->creation_date,
                            $row->payment_year,
                            $row->payment_month,
                            $row->consultant_name,
                            $row->client_name,
                            $row->pin,
                            $row->loan_id,
                            (string) $row->total_received_amount,
                            (string) $row->unclaimed_amount,
                            (string) (float) $amounts['principal'],
                            (string) (float) $amounts['interest'],
                            (string) (float) $amounts['penalty'],
                            (string) (float) $amounts['late_interest'],
                            (string) (float) $amounts['late_penalty_and_taxes'],
                            (string) (float) $amounts['profit'],
                            (!empty($row->payment_overdue_days) ? $row->payment_overdue_days : '0'),
                        ];

                        // Populate data from the array
                        $rowIndex++;
                        $sheet->fromArray($r, null, 'A' . $rowIndex);
                    }
                },
                'payment.payment_id',
                'payment_id'
            );

            // Create a writer object
            $writer = new Xlsx($spreadsheet);

            // Write the spreadsheet to the output
            $writer->save('php://output');

            ob_end_flush();

            exit;

        } catch (\Throwable $e) {
            dd($e);
        }
    }

    protected function getPaymentsFilterForm(): Form
    {
        $options = ['route' => 'received-payments.index'];
        if (getAdmin()->hasPermissionTo('received-payments.export')) {
            $options['exportRoute'] = 'received-payments.export';
        }

        return $this->formBuilder->create(ReceivedPaymentsFilterForm::class, $options);
    }

    private function actualizeFilters(array $filters): array
    {
        // mandatory filter, we show only these statuses
        $filters['paymentStatus'] = [
            PaymentStatusEnum::DELIVERED->value, // изпратени и усвоени от клиент
            PaymentStatusEnum::UNKNOWN->value, // остатъка при получени плащания
            PaymentStatusEnum::EASY_PAY_SENT->value, // изпратени по Изипей
            PaymentStatusEnum::CANCELED->value, // изпратени по Изипей
        ];
        $filters['direction'] = 'in';
        if (empty($filters['createdAt'])) {
            $filters['createdAt'] = Carbon::today()->format('d-m-Y');
        }
        if (empty($filters['offices'])) {
            $filters['offices'] = getAdminOfficeIds();
        }

        return $filters;
    }
}
