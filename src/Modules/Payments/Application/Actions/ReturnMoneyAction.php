<?php

namespace Modules\Payments\Application\Actions;

use Modules\Common\Models\UnclaimedMoney;
use Modules\Payments\Domain\Entities\ReturnMoneyModel;

readonly class ReturnMoneyAction
{
    public function __construct(
        private ReturnMoneyModel $returnMoneyModel
    )
    {
    }

    public function execute(UnclaimedMoney $unclaimedMoney): void
    {
        $this->returnMoneyModel->build($unclaimedMoney);
    }
}
