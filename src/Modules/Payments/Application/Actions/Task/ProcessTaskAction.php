<?php

namespace Modules\Payments\Application\Actions\Task;

use App\Exceptions\FrontEndExceptions;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Domain\Entities\OutgoingPayment;

readonly class ProcessTaskAction
{
    public function __construct(
        private OutgoingPayment $outgoingPayment,
    ) {}

    public function execute(PaymentTask $paymentTask): PaymentTask
    {
        if (
            empty($paymentTask->direction)
            || !in_array($paymentTask->direction, [PaymentTask::DIRECTION_IN, PaymentTask::DIRECTION_OUT])
        ) {
            throw new FrontEndExceptions('Не установено направление на плащане');
        }

        if ($paymentTask->direction == PaymentTask::DIRECTION_IN) {
            $paymentTask->status = PaymentTask::TASK_STATUS_PROCESSING;
            $paymentTask->last_status_update_date = now();
            $paymentTask->start_at = now();
            $paymentTask->handled_by = getAdminId();
            $paymentTask->save();

            return $paymentTask;
        }

        return $this->outgoingPayment
            ->buildFromExisting($paymentTask->payment)
            ->processTask($paymentTask, getAdminId())
            ->paymentTask()
            ->dbModel();
    }
}
