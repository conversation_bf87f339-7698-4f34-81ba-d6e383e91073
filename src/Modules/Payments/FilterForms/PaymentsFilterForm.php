<?php

namespace Modules\Payments\FilterForms;

use Mo<PERSON>les\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\BankAccount;

final class PaymentsFilterForm extends BaseFilterForm
{
    public function buildForm(): void
    {
        $this->addCreatedAtFilter()
            ->addSimpleSelectFilter(
                'direction',
                CashOperationalTransactionDirectionEnum::selectOptions(),
                __('table.direction')
            )
            ->addAmountFilterFromTo('sumFrom', 'sumTo')
//            ->add('paymentSources', 'select', [
//                'label' => __('table.Source'),
//                'choices' => $this->getPaymentSourcesOptions(),
//                'selected' => $this->request->get('paymentSources', ''),
//                'attr' => [
//                    'multiple' => 'multiple',
//                    'data-live-search' => 'true',
//                ]
//            ])
            ->add('bank_account_ids', 'select', [
                'label' => __('table.Source'),
                'choices' => $this->getBankAccountsOptions(),
                'empty_value' => '',
                'selected' => $this->request->get('bank_account_ids'),
                'attr' => [
                    'multiple' => 'multiple',
                    'data-live-search' => 'true',
                ]
            ])
            ->addLoanIdFilter()
            ->addClientNameFilter()
            ->addPinFilter()
            ->addOfficeIdsFilter();

        $statusOptions = [
            1 => __('table.Active'),
            0 => __('table.Deleted'),
        ];
        $this->addSimpleSelectFilter('paymentIsActive', $statusOptions, __('payments::payments.IsActive'));
    }

//    private function getPaymentSourcesOptions(): array
//    {
//        $paymentSources = [];
//        $rows = array_slice(Payment::getAllPaymentSources(), 0, -1);
//        foreach ($rows as $paymentSource) {
//            $paymentSources[$paymentSource] = __('payments::PaymentSource.' . strtolower($paymentSource));
//        }
//
//        return $paymentSources;
//    }

    private function getBankAccountsOptions(): array
    {
        return BankAccount::query()
            ->join('bank_office', 'bank_account.bank_account_id', 'bank_office.bank_account_id')
            ->whereIn('bank_office.office_id', getAdminOfficeIds())
            ->get(['bank_account.bank_account_id', 'name'])
            ->pluck('name', 'bank_account_id')
            ->toArray();
    }
}
