<?php

namespace Modules\Payments\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Repositories\BaseRepository;

class RemainingPrincipalsRepository extends BaseRepository
{
    public function getBuilderByFilters(array $filters = [])
    {
        if (empty($filters['offices'])) {
            return [];
        }

        $rawSql = "
            WITH LoanInstallments AS (
                SELECT
                    inst.loan_id,
                    inst.rest_principal,
                    inst.paid,
                    inst.due_date,
                    ll.office_id,
                    ll.consultant_id,
                    ll.juridical,
                    ll.\"isClosedJuridicalCase\"
                FROM installment inst
                JOIN loan ll ON ll.loan_id = inst.loan_id
                WHERE ll.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            ),
            LoansCount AS (
                SELECT
                    office_id,
                    COUNT(loan_id) AS loans_count
                FROM loan
                WHERE loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                AND office_id IN (" . implode(',', $filters['offices']) . ")
                GROUP BY office_id
            ),
            AggregatedLoanInstallments AS (
                SELECT
                    li.office_id,
                    SUM(CASE WHEN li.paid = 0 THEN li.rest_principal ELSE 0 END) AS remaining_principal,
                    COUNT(DISTINCT CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE THEN li.loan_id ELSE NULL END) AS loans_count_overdue,
                    SUM(CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE THEN li.rest_principal ELSE 0 END) AS remaining_principal_overdue,
                    SUM(CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE AND (li.juridical = 1 OR li.\"isClosedJuridicalCase\" = 1) THEN li.rest_principal ELSE 0 END) AS remaining_principal_overdue_juridical,
                    COUNT(DISTINCT CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE AND (li.juridical = 1 OR li.\"isClosedJuridicalCase\" = 1) THEN li.loan_id ELSE NULL END) AS loans_count_overdue_juridical
                FROM LoanInstallments li
                GROUP BY li.office_id
            )
            SELECT
                lc.office_id,
                lc.loans_count,
                COALESCE(ali.remaining_principal, 0) AS remaining_principal,
                COALESCE(ali.loans_count_overdue, 0) AS loans_count_overdue,
                COALESCE(ali.remaining_principal_overdue, 0) AS remaining_principal_overdue,
                COALESCE(ali.remaining_principal_overdue_juridical, 0) AS remaining_principal_overdue_juridical,
                COALESCE(ali.loans_count_overdue_juridical, 0) AS loans_count_overdue_juridical
            FROM LoansCount lc
            LEFT JOIN AggregatedLoanInstallments ali ON lc.office_id = ali.office_id
            ORDER BY lc.office_id;
        ";
        if (isset($filters['consultant_separation'])) {

            if ($filters['consultant_separation'] == 1) {
                $rawSql = "
                    WITH LoanInstallments AS (
                        SELECT
                            inst.loan_id,
                            inst.rest_principal,
                            inst.paid,
                            inst.due_date,
                            ll.office_id,
                            ll.consultant_id,
                            ll.juridical,
                            ll.\"isClosedJuridicalCase\"
                        FROM installment inst
                        JOIN loan ll ON ll.loan_id = inst.loan_id
                        WHERE ll.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                    ),
                    AggregatedLoanInstallments AS (
                        SELECT
                            li.office_id,
                            li.consultant_id,
                            COUNT(DISTINCT li.loan_id) AS loans_count,
                            SUM(CASE WHEN li.paid = 0 THEN li.rest_principal ELSE 0 END) AS remaining_principal,
                            COUNT(DISTINCT CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE THEN li.loan_id ELSE NULL END) AS loans_count_overdue,
                            SUM(CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE THEN li.rest_principal ELSE 0 END) AS remaining_principal_overdue,
                            SUM(CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE AND (li.juridical = 1 OR li.\"isClosedJuridicalCase\" = 1) THEN li.rest_principal ELSE 0 END) AS remaining_principal_overdue_juridical,
                            COUNT(DISTINCT CASE WHEN li.paid = 0 AND li.due_date < CURRENT_DATE AND (li.juridical = 1 OR li.\"isClosedJuridicalCase\" = 1) THEN li.loan_id ELSE NULL END) AS loans_count_overdue_juridical
                        FROM LoanInstallments li
                        GROUP BY li.office_id, li.consultant_id
                    ),
                    LoansCount AS (
                        SELECT
                            office_id,
                            consultant_id,
                            COUNT(loan_id) AS loans_count
                        FROM loan
                        WHERE loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                        GROUP BY office_id, consultant_id
                    )
                    SELECT
                        ali.office_id,
                        ali.consultant_id,
                        COALESCE(lc.loans_count, 0) AS loans_count,
                        ali.remaining_principal,
                        ali.loans_count_overdue,
                        ali.remaining_principal_overdue,
                        ali.remaining_principal_overdue_juridical,
                        ali.loans_count_overdue_juridical,
                        ct.name AS consultant_name
                    FROM AggregatedLoanInstallments ali
                    LEFT JOIN LoansCount lc ON ali.office_id = lc.office_id AND (ali.consultant_id = lc.consultant_id OR (ali.consultant_id IS NULL AND lc.consultant_id IS NULL))
                    LEFT JOIN consultant ct ON ct.consultant_id = ali.consultant_id
                    WHERE ali.office_id IN (" . implode(',', $filters['offices']) . ")
                    ORDER BY ali.consultant_id desc, ali.consultant_id IS null;
                ";
            }

            unset($filters['consultant_separation']);
        }

        $rows = DB::select(DB::raw($rawSql));

        return $rows;
    }
}
