<?php

namespace Modules\ThirdParty\Jobs;

use Illuminate\Support\Facades\Log;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Models\Payment;
use Modules\ThirdParty\Events\EasyPaySendingHasFinished;
use Modules\ThirdParty\Services\CurlEasyPayService;
use Throwable;

class SendEasyPayJob extends CommonJob
{
    const DELAY = 15;
    const MAX_ATTEMPT_COUNT = 3;
    const QUEUE_NAME = 'easypay';

    protected $logChannel = 'easypay';
    protected $queueName = self::QUEUE_NAME;

    public function __construct(
        private Payment            $payment,
        private CurlEasyPayService $curlEasyPayService,
        private CurrentDate        $currentDate,
        private readonly int       $attempt = 1
    ) {}

    public function dispatchThis(Payment $payment, CurrentDate $currentDate): void
    {
        SendEasyPayJob::dispatchNow($payment, $this->curlEasyPayService, $currentDate, 1);
    }

    public function handle()
    {
        try {
            return $this->makePayment();
        } catch (Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            $this->log($msg);
            return false;
        }
    }

    public function makePayment(): bool
    {
        $loan = $this->payment->loan;
        $result = $this->curlEasyPayService->sendMoney(
            $this->payment->client,
            $loan,
            $this->payment,
            $this->payment->amount,
            $loan->getRefinancedId()
        );
        $isSent = $result['success'] ?? false;

        $isSent = (bool) $isSent;
        $this->log(
            '- proceed(loan #' . ($loan->loan_id ?? '')
            . ', payment #' . $this->payment->getKey()
            . ', amount=' . $this->payment->amount
            . ', result=' . ($isSent ? 'OK' : 'KO')
            . ')',
            true
        );

        EasyPaySendingHasFinished::dispatch($this->payment, $isSent);

        return $this->finalLog($isSent);

    }

    private function doItAgain(): bool
    {
        if ($this->attempt < self::MAX_ATTEMPT_COUNT) {
            self::dispatch($this->payment, $this->curlEasyPayService, $this->currentDate, $this->attempt + 1)
                ->onQueue($this->getQueueName())
                ->delay($this->currentDate->now()->addSeconds(self::DELAY));

            return true;
        }

        return false;
    }

    public function finalLog(bool $isSent): bool
    {
        // $this->log('- done(loan #' . $this->payment->loan_id ?? '): ' . ($isSent ? 'success' : 'fail'));
        return true;
    }
}
