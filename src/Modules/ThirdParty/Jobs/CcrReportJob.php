<?php

namespace Modules\ThirdParty\Jobs;

use \Throwable;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Traits\ReportsChainTrait;
use Modules\ThirdParty\Services\CcrService;
use Modules\Common\Models\Loan;

/**
 * Getting CCR report on backgoround
 * Allow getting report for client and for loan(create relations)
 */
class CcrReportJob extends CommonJob
{
    use ReportsChainTrait;

    const MAX_TRY = 3;
    const META_KEY = 'CcrReportJob';

    private $try = 1;
    private $pin = null;
    private $loan = null;
    private $service = null;

    protected $logChannel = 'ccrErrors';
    protected $queueName  = 'reports';

    public function __construct(
        string $pin = null,
        Loan $loan = null,
        int $try = 1
    ) {
        $this->try = $try;
        $this->pin = $pin;
        $this->loan = $loan;
    }

    public function handle()
    {
        $loanId = ($this->loan->loan_id ?? null);
        $this->startLog($loanId, $this->try, $this->getClassName());


        // Avoiding parallel processes
        $lockKey = "ccr-job-lock-loan-{$loanId}";
        $lock = Cache::lock($lockKey, 10);
        if (!$lock->get()) {
            $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Skip: processing with another job');

            $errorMsg = "Skipped CcrReportJob: another job is processing loan #{$loanId}";
            $this->log($errorMsg);
            $this->finishedLog(0, $errorMsg);

            return false;
        }


        try {

            // validations
            if ($this->try > self::MAX_TRY) {
                $msg = 'Too many bad tryies, loan #' . ($this->loan->loan_id ?? '') . ', ' . ', pin: ' . ($this->pin ?? '');

                $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Fail: Too many bad tryies');
                $this->log($msg);
                $this->finishedLog(0, $msg);

                return false;
            }
            if (empty($this->pin) && empty($this->loan)) {
                $msg = 'There are no mandatory params, try: ' . $this->try . ', loan #' . ($this->loan->loan_id ?? '') . ', ' . ', pin: ' . ($this->pin ?? '');

                $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Fail: There are no mandatory params');
                $this->log($msg);
                $this->finishedLog(0, $msg);

                return false;
            }

            // for now we do such reports only for loan, but in theory it's possible to do for client also
            if (!empty($this->loan->loan_id)) {

                $report = $this->getService()->createReportForLoan($this->loan, 'job', $this->try);
                $msg = 'get report #' . (empty($report->ccr_report_id) ? 'none' : $report->ccr_report_id);

                // if we didnt get it let's try
                if (empty($report->ccr_report_id) || empty($report->parsed_data)) {
                    $newTry = 1 + $this->try;

                    $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Fail: bad report');
                    $this->log('Bad report, loan #' . ($this->loan->loan_id ?? '') . ', ' . ', pin: ' . ($this->pin ?? ''));

                    if ($newTry <= self::MAX_TRY) {
                        $waitSeconds = (int) config('reports.ccr.tries.' . $newTry, 0);
                        $this->pushLoanToQueue($this->loan, $newTry, $waitSeconds);

                        $msg .= '. Send next try, with delay: ' . $waitSeconds;
                    }

                } else {
                    $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', 'Success: received data');
                }

                $this->log($msg);
                $this->finishedLog(1, $msg);

                return true;
            }

        } catch (Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            $this->loan->addMeta(self::META_KEY . '(' . $this->try . ')', getMetaException($msg));
            $this->log($msg);
            $this->finishedLog(0, $msg);

            return false;
        } finally {
            $lock->release();
        }
    }

    /**
     * Used in: CcrReportListener - when we create loan from the office
     *
     * @param  Loan  $loan
     * @param  int   $delayInSec
     * @return bool
     */
    public function pushLoanToQueue(
        Loan $loan,
        int $try = 1,
        int $delayInSec = 0
    ): bool {

        $client = $loan->client;
        if (empty($client->pin)) {
            return false;
        }

        $queueName = $this->getQueueName();

        if ($delayInSec > 0) {
            $now = Carbon::now();

            self::dispatch($client->pin, $loan, $try)
                ->onQueue($queueName,)
                ->delay($now->addSeconds($delayInSec));

            return true;
        }

        self::dispatch($client->pin, $loan, $try)->onQueue($queueName);

        return true;
    }

    private function getService(): CcrService
    {
        if (null === $this->service) {
            $this->service = app(CcrService::class);
        }

        return $this->service;
    }
}
