<?php

namespace Modules\ThirdParty\Libraries;

use \Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\ThirdParty\Exceptions\NsiException;
use Modules\ThirdParty\Services\PathService;
use Modules\ThirdParty\Traits\ParseTrait;

class Nsi
{
    use ParseTrait;

    public const CERTIFICATE_KEY = 'key.pem';
    public const CERTIFICATE_MAIN = 'cert.pem';
    public const CERTIFICATE_INFO = 'cainfo.crt';
    public const CERTIFICATE_FODLER = 'Nsi';

    public const SSL_VERSION = 3;
    public const PERIOD_IN_MONTH = 12;

    // report prefix, Ex: prefix+code, noi51
    public const PREFIX = 'noi';

    // справка за пенсионер
    public const REPORT_ID_RETIRED = 51;

    // връща актуално състояние на трудови договори дали има активен, дали прекратен, къде е работил и т.н.
    public const REPORT_ID_FULL = 7;

    // връща осигурителния доход по юридически лица за времевия период който сме посочили
    public const REPORT_ID_SHORT = 2;

    private static $proxyIp;
    private static $proxyPort;
    private static $proxyPassword;

    private static $apiUrl;
    private static $apiDomain;
    private static $apiMethod;
    private static $apiHeader;
    private static $apiUsername;
    private static $apiHeaderUrl;

    private static $certificateInfPath;
    private static $certificateKeyPath;
    private static $certificatePemPath;
    private static $certificatePassword;

    public function __construct()
    {
        $this->init();
    }

    /**
     * Returns an array with codes of allowed reports
     * @return array
     */
    public static function getAllowedReports(): array
    {
        return [
            self::REPORT_ID_RETIRED => 1,
            self::REPORT_ID_FULL => 1,
            self::REPORT_ID_SHORT => 1,
        ];
    }

    /**
     * [getReport description]
     * @param  string $pin
     * @param  int    $reportCode
     * @return array
     */
    public function getReport(string $pin, int $reportCode): array
    {
        Log::channel('nsiExec')->info('pin:' . $pin . ', reportCode = ' . $reportCode);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, self::$apiUrl);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSLVERSION, self::SSL_VERSION);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSLCERTPASSWD, self::$certificatePassword);
        curl_setopt($ch, CURLOPT_CAINFO, self::$certificateInfPath);
        curl_setopt($ch, CURLOPT_SSLCERT, self::$certificatePemPath);
        curl_setopt($ch, CURLOPT_SSLKEY, self::$certificateKeyPath);

        /*
         * Proxy setup
         */
        curl_setopt($ch, CURLOPT_PROXY, self::$proxyIp);
        curl_setopt($ch, CURLOPT_PROXYPORT, self::$proxyPort);
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_PROXYUSERPWD, self::$proxyPassword);

        /*
         * Set data
         */
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->getSoapHeaders());
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->getSoapBody($pin, $reportCode));

        $response = curl_exec($ch);
        if ($errorCurl = curl_error($ch)) {
            Log::channel('nsiError')->info(
                'pin:' . $pin
                . ', reportCode = ' . $reportCode
                . ', curl error:' . $errorCurl
            );
            throw new NsiException('NSI curl error:' . $errorCurl);
        }
        curl_close($ch);

        try {
            $res = $this->parseXML($response);
        } catch (Exception $e) {
            Log::channel('nsiError')->info(
                'pin:' . $pin
                . ', reportCode = ' . $reportCode
                . ', error:' . $e->getMessage()
            );
            throw new NsiException(
                'NSI XML parse failed:' . $e->getMessage()
                . ', payload:' . $response
            );
        }

        if (!isset($res['soap:Body']['GetDataResponse']['GetDataResult'])) {
            return [];
        }

        return $res['soap:Body']['GetDataResponse']['GetDataResult'];
    }

    /**
     * [getSoapBody description]
     * @param  int    $pin
     * @param  int    $reportCode
     * @return string
     */
    private function getSoapBody(int $pin, int $reportCode): string
    {
        $dateTo = Carbon::now()->subMonth();
        $dateFrom = $dateTo->copy()->subMonth((self::PERIOD_IN_MONTH - 1));

        return trim('
            <?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <GetData xmlns="' . self::$apiDomain . '">
                  <ExternalUserName>' . self::$apiUsername . '</ExternalUserName>
                  <RepNo>' . $reportCode . '</RepNo>
                  <bulstat></bulstat>
                  <FromYear>' . $dateFrom->year . '</FromYear>
                  <ToYear>' . $dateTo->year . '</ToYear>
                  <FromMonth>' . $dateFrom->month . '</FromMonth>
                  <ToMonth>' .  $dateTo->month . '</ToMonth>
                  <EGN>' . $pin . '0</EGN>
                </GetData>
              </soap12:Body>
            </soap12:Envelope>
        ');
    }

    /**
     * [getSoapHeaders description]
     * @return array
     */
    private function getSoapHeaders(): array
    {
        return [
            "Content-type: text/xml;charset=\"utf-8\"",
            "Accept: text/xml",
            "Cache-Control: no-cache",
            "Pragma: no-cache",
            "SOAPAction: " . self::$apiHeaderUrl,
        ];
    }

    private function init()
    {
        self::$apiDomain = env('NSI_API_URL', null);
        self::$apiMethod = env('NSI_API_METHOD', null);
        self::$apiHeader = env('NSI_API_METHOD_HEADER', null);
        self::$apiUsername = env('NSI_API_USERNAME', null);
        if (
            empty(self::$apiDomain)
            || empty(self::$apiMethod)
            || empty(self::$apiHeader)
            || empty(self::$apiUsername)
        ) {
            throw new NsiException('No api params');
        }


        self::$proxyIp = env('NSI_PROXY_IP', null);
        self::$proxyPort = env('NSI_PROXY_PORT', null);
        self::$proxyPassword = env('NSI_PROXY_PASSWORD', null);
        self::$certificatePassword = env('NSI_CERTIFICATE_PASSWORD', null);
        if (
            empty(self::$proxyIp)
            || empty(self::$proxyPort)
            || empty(self::$proxyPassword)
            || empty(self::$certificatePassword)
        ) {
            throw new NsiException('No credentials');
        }


        self::$certificateInfPath = PathService::getCertificatePath(
            self::CERTIFICATE_FODLER,
            self::CERTIFICATE_INFO
        );
        if (!file_exists(self::$certificateInfPath)) {
            throw new NsiException('No info cerificate file');
        }
        self::$certificatePemPath = PathService::getCertificatePath(
            self::CERTIFICATE_FODLER,
            self::CERTIFICATE_MAIN
        );
        if (!file_exists(self::$certificatePemPath)) {
            throw new NsiException('No main cerificate file');
        }
        self::$certificateKeyPath = PathService::getCertificatePath(
            self::CERTIFICATE_FODLER,
            self::CERTIFICATE_KEY
        );
        if (!file_exists(self::$certificateKeyPath)) {
            throw new NsiException('No info cerificate file');
        }


        self::$apiUrl = self::$apiDomain . '/' . self::$apiMethod;
        self::$apiHeaderUrl = self::$apiDomain . '/' . self::$apiHeader;
    }
}
