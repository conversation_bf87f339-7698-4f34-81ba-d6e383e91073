<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('loan_offers_request_log', static function (Blueprint $table) {
            $table->foreignId('loan_id')->nullable()->constrained('loan', 'loan_id');
        });
    }

    public function down(): void
    {
        Schema::dropColumns('loan_offers_request_log', 'loan_id');
    }
};
