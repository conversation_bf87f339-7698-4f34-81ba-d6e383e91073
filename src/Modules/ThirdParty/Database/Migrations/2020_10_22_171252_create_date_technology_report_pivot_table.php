<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;
use Modules\ThirdParty\Libraries\DateTechnology;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'date_technology_report_pivot',
            function ($table) {
                $table->bigIncrements('date_technology_report_pivot_id');
                $table->integer('date_technology_report_id')->unsigned()->index();
                $table->integer('client_id')->unsigned()->index();
                $table->integer('loan_id')->unsigned()->index();
                $table->enum('name', DateTechnology::getAllowedReports());
                $table->tableCrudFields(true, true);

                $table->foreign('date_technology_report_id')->references('date_technology_report_id')->on('date_technology_report')->onDelete('cascade');
                $table->foreign('client_id')->references('client_id')->on('client')->onDelete('cascade');
                $table->foreign('loan_id')->references('loan_id')->on('loan')->onDelete('cascade');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'date_technology_report_pivot',
            function (Blueprint $table) {
                $table->dropForeign('date_technology_report_pivot_date_technology_report_id_foreign');
                $table->dropForeign('date_technology_report_pivot_client_id_foreign');
                $table->dropForeign('date_technology_report_pivot_loan_id_foreign');
            }
        );
        Schema::dropIfExists('date_technology_report_pivot');
    }
};
