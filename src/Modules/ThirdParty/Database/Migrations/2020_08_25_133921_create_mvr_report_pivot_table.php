<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('mvr_report_pivot')) {
            $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
                'mvr_report_pivot',
                function ($table) {
                    $table->bigIncrements('mvr_report_pivot_id');
                    $table->integer('mvr_report_id')->unsigned()->index();
                    $table->integer('client_id')->unsigned()->index();
                    $table->integer('loan_id')->unsigned()->index();
                    $table->foreign('mvr_report_id')->references('mvr_report_id')->on('mvr_report')->onDelete('cascade');
                    $table->foreign('client_id')->references('client_id')->on('client')->onDelete('cascade');
                    $table->foreign('loan_id')->references('loan_id')->on('loan')->onDelete('cascade');
                    $table->tableCrudFields(true, true);
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'mvr_report_pivot',
            function (Blueprint $table) {
                $table->dropForeign('mvr_report_pivot_mvr_report_id_foreign');
                $table->dropForeign('mvr_report_pivot_client_id_foreign');
                $table->dropForeign('mvr_report_pivot_loan_id_foreign');
            }
        );
        Schema::dropIfExists('mvr_report_pivot');
    }
};
