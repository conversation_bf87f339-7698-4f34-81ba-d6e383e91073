<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\NoiEmployer;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            NoiEmployer::getTableName(),
            function (Blueprint $table) {
                $table->bigIncrements('noi_employer_id');
                $table->bigInteger('noi_report_id')->unsigned();

                $table->string('employer_name');
                $table->enum('contract_reason', NoiEmployer::getContractReasons());
                $table->enum('contract_document_type', NoiEmployer::getContractDocumentTypes());
                $table->date('contract_foundation_date');
                $table->date('contract_end_date')->nullable();
                $table->date('contract_termination_date')->nullable();
                $table->decimal('salary', 11, 2);
                $table->tinyInteger('status')->unsigned();

                $table->tableCrudFields();

                $table->foreign('noi_report_id')->references('noi_report_id')->on('noi_report')->onDelete('cascade');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            NoiEmployer::getTableName(),
            function (Blueprint $table) {
                $table->dropForeign('noi_employer_noi_report_id_foreign');
            }
        );
        Schema::dropIfExists(NoiEmployer::getTableName());
    }
};
