<?php

namespace Modules\ThirdParty\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\ThirdParty\Traits\ReportTrait;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;

class A4EReportSeeder extends Seeder
{
    use ReportTrait;

    // Dummy array to create json response
    public const FAKE_ARR = [
        [
            'firstName' => 'Vladimir',
            'lastName' => 'Putin',
        ],
        [
            'firstName' => 'Donald',
            'lastName' => 'Trump',
        ],
        [
            'firstName' => 'Васил',
            'lastName' => 'Божков',
        ],
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $a4eReports = [];
        $faker = Faker::create();
        $allClients = Client::all();

        foreach ($allClients as $client) {
            $start = $this->timer();
            $apiResponse = json_encode(self::FAKE_ARR, JSON_UNESCAPED_UNICODE);
            $end = $this->timer();
            $execTime = $this->calculateExecTime($start, $end, 10);
            $createdAt = now()->subDays($faker->numberBetween(1, 60));

            $latestLoan = $client->getLatestLoan();
            $report = [
                'client_id' => $client->getKey(),
                'loan_id' => !empty($latestLoan->loan_id) ? $latestLoan->getKey() : null,
                'request' => $apiResponse,
                'response' => $apiResponse,
                'score' => $faker->numberBetween(1, 100),
                'score_uncapped' => $faker->numberBetween(1, 100),
                'exec_time' => $execTime,
                'last' => 0,
                'active' => 1,
                'deleted' => 0,
                'created_at' => $createdAt,
                'created_by' => Administrator::DEFAULT_ADMINISTRATOR_ID,
            ];

            // Three reports for each client and only one is last
            $a4eReports[] = $report;
            $report['created_at'] = $createdAt->copy()->addDays(3);
            $a4eReports[] = $report;
            $report['created_at'] = $createdAt->copy()->addDays(6);
            $report['last'] = 1;
            $a4eReports[] = $report;
        }

        DB::table('a4e_report')->insert($a4eReports);
    }
}

