<?php

namespace Modules\ThirdParty\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

final class ReportStatsViewCommand extends Command
{
    /**
     * @var string
     */
    protected $signature = 'script:db-view:report-stats {action=up : Operation to be performed with view (up|down)}';

    /**
     * @var string
     */
    protected $description = 'Manage report_stats view';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        if ($this->argument('action') === 'up') {
            DB::statement(
<<<'SQL'
CREATE MATERIALIZED VIEW public.report_stats
TABLESPACE pg_default
AS SELECT combined_reports.pin,
    combined_reports.created_at,
    combined_reports.loan_id,
    o.office_id,
    o.name AS office_name,
    combined_reports.created_by AS created_by_id,
        CASE
            WHEN combined_reports.created_by = 1 THEN 'Автоматично'::text
            ELSE (a.first_name::text || ' '::text) || a.last_name::text
        END AS created_by_name,
        CASE
            WHEN combined_reports.name::text = ANY (ARRAY['noi2'::text, 'noi7'::text, 'noi51'::text]) THEN ('ной'::text || substr(combined_reports.name::text, 4))::character varying
            ELSE combined_reports.name
        END AS type
   FROM (
        SELECT
            mr.pin,
            mr.created_at,
            (
                SELECT min(mrp.loan_id) AS min
                FROM mvr_report_pivot mrp
                WHERE mrp.mvr_report_id = mr.mvr_report_id
            ) AS loan_id,
            mr.created_by,
            'мвр'::character varying AS name
           FROM mvr_report mr
        UNION ALL
        SELECT
            nr.pin,
            nr.created_at,
            (
                SELECT min(nrp.loan_id) AS min
                FROM noi_report_pivot nrp
                WHERE nrp.noi_report_id = nr.noi_report_id
            ) AS loan_id,
            nr.created_by,
            nr.name
        FROM noi_report nr
        UNION ALL
        SELECT
            cr.pin,
            cr.created_at,
            (
                SELECT min(crp.loan_id) AS min
                FROM ccr_report_pivot crp
                WHERE crp.ccr_report_id = cr.ccr_report_id
            ) AS loan_id,
            cr.created_by,
            'цкр'::character varying AS name
        FROM ccr_report cr
    ) combined_reports
    LEFT JOIN loan l ON combined_reports.loan_id = l.loan_id
    LEFT JOIN office o ON l.office_id = o.office_id
    LEFT JOIN administrator a ON combined_reports.created_by = a.administrator_id
  ORDER BY combined_reports.created_at DESC
WITH DATA;
SQL
            );

            DB::statement('CREATE INDEX idx_report_stats_created_at ON public.report_stats USING btree (created_at);');
            DB::statement('CREATE INDEX idx_report_stats_office_id ON public.report_stats USING btree (office_id);');
            DB::statement('CREATE INDEX idx_report_stats_pin ON public.report_stats USING btree (pin);');
        }

        if ($this->argument('action') === 'down') {
            DB::statement('DROP MATERIALIZED VIEW report_stats');
        }
    }
}
