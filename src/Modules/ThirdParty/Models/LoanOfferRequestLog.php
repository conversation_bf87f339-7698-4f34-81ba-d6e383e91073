<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\Loan;
use Modules\ThirdParty\Enums\LoanOfferCompanyEnum;

/**
 * @mixin IdeHelperLoanOfferRequestLog
 */
final class LoanOfferRequestLog extends Model
{
    public const UPDATED_AT = null;

    protected $table = 'loan_offers_request_log';

    protected $fillable = [
        'company',
        'loan_id',
        'url',
        'params',
        'response',
        'created_at',
        'created_by',
    ];

    protected $casts = [
        'params' => 'array',
        'company' => LoanOfferCompanyEnum::class,
    ];

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }
}
