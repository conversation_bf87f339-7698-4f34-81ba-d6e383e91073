<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\ThirdParty\Enums\LoanOfferCompanyEnum;

/**
 * @mixin IdeHelperLoanOffer
 */
final class LoanOffer extends Model
{
    public const UPDATED_AT = null;

    protected $fillable = [
        'loan_id',
        'loan_status_id',
        'company',
        'created_at',
        'created_by',
    ];

    protected $casts = [
        'company' => LoanOfferCompanyEnum::class,
    ];
}
