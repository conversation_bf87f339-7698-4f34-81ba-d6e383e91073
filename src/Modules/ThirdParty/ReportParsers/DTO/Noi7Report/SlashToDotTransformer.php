<?php

namespace Modules\ThirdParty\ReportParsers\DTO\Noi7Report;

use <PERSON><PERSON>\LaravelData\Support\DataProperty;
use <PERSON>tie\LaravelData\Transformers\Transformer;

/**
 * Example of SlashToDotTransformer from 'dasdas/asdasd/asdasd' to 'dasdas.asdasd.asdasd'
 */
class SlashToDotTransformer implements Transformer
{

    public function transform(DataProperty $property, mixed $value): mixed
    {
        return str_replace('/', '.', $value ?: '');
    }
}