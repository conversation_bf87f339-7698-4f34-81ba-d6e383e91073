<?php

namespace App\Modules\ThirdParty\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CpayMiddleware
{
    /**
     * Handle an incoming request and validate allowed IPs
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $allowedIps = config('thirdaprty.cpay.allowed_ips');
        if (empty($allowedIps)) {
            // TODO: throws, log, and exit;
        }

        if (!in_array($request->ip(), $allowedIps)) {
            // TODO: throws "CPay IP Restriction", log "CPay IP Restriction, IP: xxx", and exit;
            die('CPay IP Restriction! (Your IP: ' . $request->ip() . ')'); // TODO: remove this line
        }

        return $next($request);
    }
}
