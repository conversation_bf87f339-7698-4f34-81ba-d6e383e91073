<?php

// !!! CpayMiddleware - приемаме само определени IPта

// Cpay правят проверки за същвуващ клиент GET:/cpay/check_existing_customer/{ЕГН}

// Cpay изпращат кредити POST:/cpay/store
// Важно: те могат да изпращат и промени по кредите си
// - проверка на валидно ID на кредита, защото ползват свои IDта
// - проверка на хеш
// - запазване на кредита при нас
// - изпращане на емейл на админ за получен мейл от Cpay
// - събираме справки от ной/нси/ЦКР и правиме скоринг
// - при одобрение генерираме документи

// Cpay теглят от нас генерирани документи GET:cpay/documents/{document}

// Когато статуса на кредит се променя в наша система ние сме длъжни да им изпратим промени
// Пример от старата система:
if ($request->has('cpay_status') && !empty($request->cpay_status) && $credit->cpay_status != $request->cpay_status) {
    $cpay = new CPay();
    $cpay->changeStatus($request->cpay_status, $credit);
}
