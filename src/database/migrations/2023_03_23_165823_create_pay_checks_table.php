<?php

use App\Enums\PayStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('pay_checks', function (Blueprint $table) {
            $table->id();
            $table->string('idn');

            $table->json('input_data')->nullable();

            $table->enum('response_status', PayStatusEnum::values())->nullable()->index();
            $table->string('response_data')->nullable();

            $table->json('logs')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('pay_inits');
    }
};
