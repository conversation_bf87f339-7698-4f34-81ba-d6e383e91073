<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        // Failed to use Blueprint - Doctrine\DBAL\Exception: Unknown column type "jsonb" requested.
        DB::statement("
            ALTER TABLE client
            ALTER COLUMN verif_provider_data TYPE jsonb
            USING verif_provider_data::jsonb
        ");
    }

    public function down(): void
    {
        DB::statement("
            ALTER TABLE client
            ALTER COLUMN verif_provider_data TYPE json
            USING verif_provider_data::json
        ");
    }
};
