<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('veriff_webhook_log', function (Blueprint $table) {
            $table->string('veriff_status')->nullable()->index();
        });
    }

    public function down(): void
    {
        Schema::table('veriff_webhook_log', function (Blueprint $table) {
            $table->dropColumn('veriff_status');
        });
    }
};
