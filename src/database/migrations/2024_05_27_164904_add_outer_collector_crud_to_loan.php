<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dateTime('outer_collector_from_date')->nullable()->index();
            $table->integer('marked_as_outer_collector_by')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dropColumn(['outer_collector_from_date', 'marked_as_outer_collector_by']);
        });
    }
};
