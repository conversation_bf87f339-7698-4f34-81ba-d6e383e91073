<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('client_address', function (Blueprint $table) {
            $table->enum('address_is_match', ['yes', 'no'])->default('yes');
        });
    }

    public function down(): void
    {
        Schema::table('client_address', function (Blueprint $table) {
            //
        });
    }
};
