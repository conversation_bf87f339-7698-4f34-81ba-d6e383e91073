<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Communication\Application\Enums\CommunicationClassificationEnum;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('communication_pivots', function (Blueprint $table) {
            $table->enum('classification', CommunicationClassificationEnum::toArray())
                ->default(CommunicationClassificationEnum::COMMENT->value)
                ->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('communication_pivots', function (Blueprint $table) {
            $table->dropColumn('classification');
        });
    }
};
