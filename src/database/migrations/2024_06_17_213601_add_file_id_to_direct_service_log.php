<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('direct_service_log', function (Blueprint $table) {
            $table->foreignIdFor(\Modules\Common\Models\File::class, 'file_id')
                ->constrained('file', 'file_id');
        });
    }

    public function down(): void
    {
        Schema::table('direct_service_log', function (Blueprint $table) {
            $table->dropColumn('file_id');
        });
    }
};
