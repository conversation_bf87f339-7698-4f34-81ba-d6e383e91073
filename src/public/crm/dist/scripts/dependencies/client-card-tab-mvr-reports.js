"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var ClientCardTabMvrReports=function(){function e(){_classCallCheck(this,e),this.hash="mvr_reports",this.config={rest:["getMvrReportsRest","hasMvrReportsPermissionRest"],restCallbacks:{success:function(e){var t=e.endpoint,r=e.data;window.ClientCardTabMvrReports.restDataObject[t]=r,window.ClientCardTabMvrReports.restCheckResults()}},restData:{}},this.resetDefaults()}return _createClass(e,[{key:"init",value:function(){this.resetDefaults(),this.collectRestData()}},{key:"resetDefaults",value:function(){this.restDataObject={}}},{key:"collectRestData",value:function(){var e=window.ClientCardTabMvrReports.config.restCallbacks;this.config.rest.forEach((function(t,r){var a=window.ClientCard.config.rest[t];void 0!==e&&Object.keys(e).forEach((function(t,r){a[t]=e[t]}));var n=window.ClientCardTabMvrReports.config.restData[t];void 0===n&&(n={}),n.storeInSession=!0,n.endpoint=t,Object.keys(n).length>0&&Object.keys(n).forEach((function(e,t){if(void 0===a.data[e]||!a.data[e]){var r=n[e];a.data[e]=r}})),jQuery.ajax(a)}))}},{key:"restCheckResults",value:function(){if(Object.keys(window.ClientCardTabMvrReports.restDataObject).length<window.ClientCardTabMvrReports.config.rest.length)return!1;var e=window.ClientCard.config.rest.renderTab,t={tabId:window.ClientCardTabMvrReports.hash,clientId:window.ClientCardConfigObject.client.id,loanId:window.ClientCardConfigObject.loan.id};Object.keys(window.ClientCardTabMvrReports.restDataObject).forEach((function(e,r){var a=e.replace("get","").replace("Rest","");if(void 0===t[a]){var n=window.ClientCardTabMvrReports.restDataObject[e];t[a]=n}})),e.data=Object.assign(e.data,t),jQuery.ajax(e)}}]),e}();window.ClientCardTabMvrReports=new ClientCardTabMvrReports;
//# sourceMappingURL=client-card-tab-mvr-reports.js.map