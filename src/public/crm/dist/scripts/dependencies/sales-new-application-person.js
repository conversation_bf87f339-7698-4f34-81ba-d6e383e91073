"use strict";function _classCallCheck(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,n){for(var a=0;a<n.length;a++){var t=n[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function _createClass(e,n,a){return n&&_defineProperties(e.prototype,n),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}var SalesNewApplicationPerson=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"init",value:function(e){this.parent=e,this.selectorWorkable=this.parent.selectorWorkable,this.domElements=this.parent.domElements,this.rpa={pin:this.rpaPin,idCard:this.rpaIdCard,none:function(){}},this.rpaData={},this.initEventHandlers();var n=new URL(window.location.href).searchParams.get("rpa"),a=n||"none";this.initRPA(a)}},{key:"initRPA",value:function(e){var n=void 0!==this.rpa[e]?this.rpa[e]:null;n&&n()}},{key:"rpaPin",value:function(){window.SalesNewApplicationPerson.domElements.global.personCardInputPin.val("9112244723").trigger("change"),jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.searchByPin).trigger("click")}},{key:"rpaIdCard",value:function(){window.SalesNewApplicationPerson.domElements.global.personCardInputPin.val("9101159949").trigger("change"),window.SalesNewApplicationPerson.domElements.global.personCardInputIdCardNumber.val("6302902653").trigger("change"),jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.checkFromMvr).trigger("click")}},{key:"initEventHandlers",value:function(){jQuery(document).on("click",this.selectorWorkable.person.button,this.handleButtonClick),jQuery(document).on("change",this.selectorWorkable.person.select,this.handleSelectChange),jQuery(document).on("change",this.selectorWorkable.person.text,this.handleTextChange),jQuery(document).on("keyup",this.selectorWorkable.person.text,this.handleKeyup)}},{key:"handleButtonClick",value:function(e){var n=jQuery(e.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.button)[0].classList;if(0!==n.length){var a=window.SalesNewApplicationPerson.selectorWorkable.person.button.replace(".","");n.forEach((function(n,t){if(n.indexOf(a)>-1){var o=n.replace(a,"");if(o.length>0){var i=window.SalesNewApplication.prepareEventHandlerName(o);void 0!==window.SalesNewApplicationPerson[i]&&window.SalesNewApplicationPerson[i](e)}}}))}}},{key:"handleSelectChange",value:function(e){var n=jQuery(e.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.select)[0].classList;if(0!==n.length){var a=window.SalesNewApplicationPerson.selectorWorkable.person.select.replace(".","");n.forEach((function(n,t){if(n.indexOf(a)>-1){var o=n.replace(a,"");if(o.length>0){var i=window.SalesNewApplication.prepareEventHandlerName(o);void 0!==window.SalesNewApplicationPerson[i]&&window.SalesNewApplicationPerson[i](e)}}}))}}},{key:"handleTextChange",value:function(e){var n=jQuery(e.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.text)[0].classList;if(0===n.length)return e;var a=window.SalesNewApplicationPerson.selectorWorkable.person.text.replace(".","");n.forEach((function(n,t){if(n.indexOf(a)>-1){var o=n.replace(a,"");if(o.length>0){var i=window.SalesNewApplication.prepareEventHandlerName(o);void 0!==window.SalesNewApplicationPerson[i]&&window.SalesNewApplicationPerson[i](e)}}}))}},{key:"handleKeyup",value:function(e){var n=jQuery(e.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.text)[0].classList;if(0===n.length)return e;var a=window.SalesNewApplicationPerson.selectorWorkable.person.text.replace(".","");n.forEach((function(n,t){if(n.indexOf(a)>-1){var o=n.replace(a,"");if(o.length>0){var i=window.SalesNewApplication.prepareEventHandlerName(o);void 0!==window.SalesNewApplicationPerson[i]&&window.SalesNewApplicationPerson[i](e)}}}))}},{key:"searchByPin",value:function(e){e.preventDefault();var n=jQuery(e.target);if(window.SalesNewApplicationPerson.parent.isButtonLocked(n)||!window.SalesNewApplicationPerson.parent.helper.wasPinChanged())return!1;window.SalesNewApplicationPerson.parent.handleButtonState(n,!0);var a=window.SalesNewApplicationPerson.domElements.global.personCardInputPin.val().trim(),t=window.SalesNewApplicationPerson.domElements.global.pinErrorContainer,o=window.SalesNewApplicationPerson.parent.validator.validatePin(a);if(!o.status)return showErrorMessage(t,o.message,window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout),void window.SalesNewApplicationPerson.parent.handleButtonState(n,!1);var i=window.SalesNewApplicationPerson.domElements.global.loanProductId.val().trim(),r=window.SalesNewApplicationPerson.parent.validator.validateProductId(i);if(!r.status)return showErrorMessage(t,r.message,window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout),void window.SalesNewApplicationPerson.parent.handleButtonState(n,!1);var l={pin:a,selectedProductId:i,getAllContacts:!0};window.SalesNewApplicationPerson.parent.helper.getClientAndLoanData(l,{button:n})}},{key:"checkFromMvr",value:function(e){e.preventDefault();var n=jQuery(e.target);if(window.SalesNewApplicationPerson.parent.isButtonLocked(n))return!1;window.SalesNewApplicationPerson.parent.handleButtonState(n,!0);var a=window.SalesNewApplicationPerson.domElements.global.personCardInputPin.val().trim(),t=window.SalesNewApplicationPerson.parent.validator.validatePin(a),o=window.SalesNewApplicationPerson.domElements.global.pinErrorContainer;if(!t.status)return showErrorMessage(o,t.message,window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout),void window.SalesNewApplicationPerson.parent.handleButtonState(n,!1);var i=window.SalesNewApplicationPerson.domElements.global.personCardInputIdCardNumber.val().trim(),r=window.SalesNewApplicationPerson.parent.validator.validateIdCardNumber(i),l=window.SalesNewApplicationPerson.domElements.global.idCardErrorContainer;if(0===i.length||0===a.length)return showErrorMessage(l,window.SalesNewApplicationPerson.parent.config.message.mvr.errorSubmit,window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout),void window.SalesNewApplicationPerson.parent.handleButtonState(n,!1);if(!r.status)return showErrorMessage(l,r.message,window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout),void window.SalesNewApplicationPerson.parent.handleButtonState(n,!1);var s={"client_idcard[pin]":a,"client_idcard[idcard_number]":i};window.SalesNewApplicationPerson.parent.helper.getClientFromMvr(s,{button:n})}},{key:"paymentMethod",value:function(e){var n=jQuery(e.target),a=parseInt(n.val()),t=window.SalesNewApplicationPerson.domElements.global.iban;a!==window.SalesNewApplicationPerson.parent.config.paymentMethod.bank.payment_method_id?t.hide():t.show()}},{key:"pin",value:function(e){"change"===e.type&&(jQuery(e.target).val().trim()!==window.SalesNewApplicationPerson.parent.helper.metaDataObject.pin.previousData&&window.SalesNewApplicationPerson.domElements.global.clientMetaAction.val("").trigger("change"));if(13===e.keyCode&&jQuery(e.target).val().trim().length>0)return e.preventDefault(),void jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.searchByPin).trigger("click")}},{key:"addCurrentAddress",value:function(e){e.preventDefault();var n=jQuery(e.target);"true"!==n.attr("aria-expanded")?n.toggleClass("fa-minus fa-plus btn-circle-blue btn-primary btn-danger"):n.toggleClass("fa-plus fa-minus btn-circle-blue btn-primary btn-danger")}},{key:"addCurrentAddressGuarant",value:function(e){window.SalesNewApplicationPerson.addCurrentAddress(e)}},{key:"applyButton",value:function(e){e.preventDefault();var n=jQuery(e.target);if(!window.SalesNewApplicationPerson.parent.isButtonLocked(n)){if(window.SalesNewApplicationPerson.parent.handleButtonState(n,!0),!checkRefinanceSum())return window.SalesNewApplicationPerson.parent.handleButtonState(n,!1),void window.SalesNewApplicationPerson.domElements.global.modalRefinance.modal("show");if(window.SalesNewApplicationPerson.parent.validator.validateNewApplicationParams({loanSum:window.SalesNewApplicationPerson.domElements.global.loanSum.val().trim(),paymentMethodId:window.SalesNewApplicationPerson.domElements.global.paymentMethodSelect.val().trim()})){window.SalesNewApplicationPerson.parent.prepareFormDataByCardType();var a=window.SalesNewApplicationPerson.domElements.global.newAppApplyForm.serializeArray(),t=[],o={};a.forEach((function(e,n){void 0===o[e.name]&&t.push(e)}));var i=jQuery.map(t,(function(e,n){return"".concat(e.name,"=").concat(e.value)})).join("&");window.SalesNewApplicationPerson.parent.helper.createNewApplication(i,{button:n})}else window.SalesNewApplicationPerson.parent.handleButtonState(n,!1),window.SalesNewApplicationPerson.domElements.global.modalInflatedLoanAmount.modal("show")}}},{key:"officesSelect",value:function(e){var n=jQuery(e.target).val().trim();n.length>0&&window.SalesNewApplication.hidePaymentMethodOptions(n)}}]),e}();window.SalesNewApplicationPerson=new SalesNewApplicationPerson;
//# sourceMappingURL=sales-new-application-person.js.map