# Front End Resources Usage

The styles and scripts of the CreditHunter project are written in SCSS and ES6 then compiled to a minified resource files.

## Compiler
For the compiling process is used the (Prepros Compiler)[https://prepros.io/]

**License Key: 4PCUF-2DMJZ-RBTGC-C6NG7-N4YE3-3CWJS**

## Setup
1. Install the compiler
2. Drag and drop the **/crm/** directory in the compiler
3. Choose **core.scss** from the compiler and hit **Process file**
4. That's it! From now on, the watcher will detect each of your changes.

## SCSS File Structure
1. **Main File:** /styles/core.scss
2. **Components:** /styles/components/
3. **Helpers:** /styles/helpers/

## JS File Structure


### SRC - Raw Files
Path: **/crm/src/scripts/**

#### Core.js
Serves as autoloader of JS modules and their dependencies

Compiled to: **/crm/dist/scripts/core.js**

**Autoload Example Object:**
```js
{
    script: 'example-js-module-file-name',
    className: 'ExampleJSModuleClassName',
    onload: () => {

        // Called when the scrip has loaded (not needed for every module)
        window.ExampleJSModuleClassName.init();
    },
    loadWhen: () => {

        // Set of rules when to load the module
        return (
            typeof document.querySelector('.js-filters') !== 'undefined' &&
            document.querySelector('.js-filters') !== null
        );
    },
    worksWith: [ // Dependencies
        {
            script: 'client-card-tab-overview-grid',
            className: 'ClientCardTabOverviewGrid',
        },
    ]
},
```

#### Autoload
Autoloaded JS Modules must be placed within: **/crm/src/scripts/autoload/**

Compiled JS Modules go to: **/crm/dist/scripts/autoload/**

**Note:** File name should be the same in **SRC** and **DIST**

#### Dependencies
Dependecy JS Modules must be placed within: **/crm/src/scripts/dependencies/**

Compiled JS Modules go to: **/crm/dist/scripts/dependencies/**

**Note:** File name should be the same in **SRC** and **DIST**