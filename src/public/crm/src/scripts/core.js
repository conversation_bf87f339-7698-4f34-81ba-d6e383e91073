const versionController = new Date();
const versionAndromedaScripts = (
    window.location.href.indexOf('localhost') > -1 ?
    `${versionController.getHours()}${versionController.getMinutes()}${versionController.getSeconds()}` :
    '1.0.0'
);

window.andromedaStorePublic = {
    origin: window.location.origin,
    scripts: {
        version: `${versionAndromedaScripts}`,
        dir: `${window.location.origin}/crm/dist/scripts/autoload/`,
        dependenciesDir: `${window.location.origin}/crm/dist/scripts/dependencies/`,
    }
};

window.addEventListener('load', (event) => {
    window.andromedaAutoloadDependencies = [];

    window.breakpoints = {
        sm: 576,
        md: 768,
        lg: 992,
    };
    
    window.autoload = [
        {
            script: 'client-card',
            className: 'ClientCard',
            onload: (data = null) => {
                window.ClientCard.init(data);
            },
            loadWhen: () => {
                
                if (window.dbg) {
                    return false;
                }
                
                return (
                    typeof document.querySelector('[data-load-id="clientCard"]') !== 'undefined' &&
                    document.querySelector('[data-load-id="clientCard"]') !== null
                );
            },
            worksWith: [
                {
                    script: 'jquery-sortable',
                    className:'jquerySortable',
                },
                {
                    script: 'jquery-ui',
                    className:'jqueryUi',
                },
                {
                    script: 'client-card-tab-overview-grid',
                    className: 'ClientCardTabOverviewGrid',
                },
                {
                    script: 'client-card-tab-overview',
                    className: 'ClientCardTabOverview',
                },
                {
                    script: 'client-card-tab-ckr-reports',
                    className: 'ClientCardTabCkrReports',
                },
                {
                    script: 'client-card-tab-noi-reports',
                    className: 'ClientCardTabNoiReports',
                },
                {
                    script: 'client-card-tab-mvr-reports',
                    className: 'ClientCardTabMvrReports',
                },
                {
                    script: 'client-card-tab-a4e-reports',
                    className: 'ClientCardTabA4eReports',
                },
                {
                    script: 'client-card-tab-communication',
                    className: 'ClientCardTabCommunication',
                },
                {
                    script: 'client-card-tab-payment',
                    className: 'ClientCardTabPayment',
                },
                {
                    script: 'client-card-tab-payment-schedule',
                    className: 'ClientCardTabPaymentSchedule',
                },
                {
                    script: 'client-card-tab-system-log',
                    className: 'ClientCardTabSystemLog',
                },
                {
                    script: 'client-card-tab-previous-requests',
                    className: 'ClientCardTabPreviousRequests',
                },
            ],
        },
        {
            script: 'sales-new-application',
            className: 'SalesNewApplication',
            onload: (data = null) => {
                window.SalesNewApplication.init(data);
            },
            loadWhen: () => {

                return (
                    typeof document.querySelector('#newApplicationApplyForm') !== 'undefined' &&
                    document.querySelector('#newApplicationApplyForm')
                );
            },
            worksWith: [
                {
                    script: 'sales-new-application-helper',
                    className: 'SalesNewApplicationHelper',
                },
                {
                    script: 'sales-new-application-validator',
                    className: 'SalesNewApplicationValidator',
                },
                {
                    script: 'sales-new-application-person',
                    className: 'SalesNewApplicationPerson',
                },
                {
                    script: 'sales-new-application-company',
                    className: 'SalesNewApplicationCompany',
                },
            ],
        },
    ];

    window.scriptsDir = andromedaStorePublic.scripts.dir;

    autoload.forEach((element) => {
        
        if (
            typeof element.loadWhen === 'function' &&
            element.loadWhen() 
        ) { 
            if (
                typeof element.worksWith !== 'undefined' &&
                element.worksWith !== null &&
                element.worksWith.length > 0
            ) {

                window.andromedaAutoloadDependencies.push({
                    element: element,
                    loaded: 0,
                });

                element.worksWith.forEach((dependency) => {
                    window.loadDependencyScript(dependency.script, (window.andromedaAutoloadDependencies.length - 1));
                });
            } else { 
                window.loadScript(element);
            }
        }
    });
});

window.loadScript = (element) => {
    const script = document.createElement('script');
    script.src = `${andromedaStorePublic.scripts.dir}${element.script}.js?ver=${andromedaStorePublic.scripts.version}`;
    script.async = 'async';
    script.onload = () => {
        if (typeof window[element.className] === 'object') {
            if (typeof element.onload !== 'undefined') {
                element.onload(element);
            }
        }
    };

    // Append the Script
    document.body.appendChild(script);
}

window.loadDependencyScript = (dependency, id) => {
    const script = document.createElement('script');
    script.src = `${andromedaStorePublic.scripts.dependenciesDir}${dependency}.js?ver=${andromedaStorePublic.scripts.version}`;
    script.async = 'async';
    script.onload = () => {
        window.andromedaAutoloadDependencies[id].loaded += 1;
        if (window.andromedaAutoloadDependencies[id].loaded >= window.andromedaAutoloadDependencies[id].element.worksWith.length) {
            window.loadScript(window.andromedaAutoloadDependencies[id].element);
        }
    };

    // Append the Script
    document.body.appendChild(script);
}