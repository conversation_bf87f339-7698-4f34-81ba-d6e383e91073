{"version": 3, "sources": ["ui/default/skin.css"], "names": [], "mappings": ";;;;;;AAMA,KACE,WAAY,YACZ,MAAO,QACP,OAAQ,KACR,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,OACb,4BAA6B,YAC7B,gBAAiB,KACjB,YAAa,KACb,eAAgB,KAChB,eAAgB,QAChB,YAAa,OAEf,yBACE,WAAY,QACZ,MAAO,QACP,OAAQ,QACR,UAAW,QACX,YAAa,QACb,UAAW,QACX,WAAY,QACZ,YAAa,QACb,YAAa,QACb,4BAA6B,QAC7B,WAAY,QACZ,gBAAiB,QACjB,YAAa,QACb,eAAgB,QAChB,eAAgB,QAChB,YAAa,QAEf,yBAEE,WAAY,IACZ,OAAQ,EACR,MAAO,KACP,OAAQ,KACR,OAAQ,EACR,UAAW,KACX,QAAS,EACT,QAAS,EACT,SAAU,OACV,MAAO,KAET,oBACE,UAAW,IACX,WAAY,KAEd,cACE,UAAW,IACX,WAAY,MAEd,aACE,OAAQ,IAAI,MAAM,KAClB,cAAe,EACf,WAAY,KACZ,WAAY,WACZ,QAAS,KACT,eAAgB,OAChB,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,SAAU,OACV,SAAU,SACV,WAAY,kBAEd,oBACE,OAAQ,KACR,WAAY,KAEd,uCACE,OAAQ,IAAI,MAAM,KAClB,cAAe,EACf,WAAY,KAEd,iBACE,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,QAAS,KAEX,oBACA,wBACE,QAAS,EAEX,yBACE,OAAQ,EAEV,kCACE,YAAa,OACb,QAAS,KACT,cAAe,IAEjB,uCACE,YAAa,QACb,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,QAAS,KACT,gBAAiB,cAEnB,2CACE,eAAgB,IAElB,+CACE,YAAa,OACb,QAAS,KACT,cAAe,IAEjB,oEACE,aAAc,KACd,aAAc,MAEhB,kCACE,WAAY,KAEd,4FACE,iBAAkB,oBAClB,aAAc,oBACd,MAAO,QAET,wGACE,aAAc,oBAEhB,8EACE,MAAO,QAET,wEACE,KAAM,QAER,sEACE,MAAO,QAET,4FACE,iBAAkB,mBAClB,aAAc,mBACd,MAAO,QAET,wGACE,aAAc,mBAEhB,8EACE,MAAO,QAET,wEACE,KAAM,QAER,sEACE,MAAO,QAET,6FACE,iBAAkB,iBAClB,aAAc,iBACd,MAAO,QAET,yGACE,aAAc,iBAEhB,+EACE,MAAO,KAET,yEACE,KAAM,KAER,uEACE,MAAO,KAET,+FACE,iBAAkB,oBAClB,aAAc,oBACd,MAAO,QAET,2GACE,aAAc,oBAEhB,iFACE,MAAO,QAET,2EACE,KAAM,QAER,yEACE,MAAO,QAET,+DACA,qFACE,WAAY,EAEd,uFACE,YAAa,IAEf,8FACE,YAAa,KAEf,gFACE,QAAS,IAAI,IAAI,IAAI,IAEvB,4FACE,kBAAmB,IACnB,aAAc,IAEhB,iFACE,aAAc,IAEhB,wFACE,aAAc,KAEhB,0EACE,QAAS,IAAI,IAAI,IAAI,IAEvB,sFACE,mBAAoB,IACpB,cAAe,IAEjB,oBACE,QAAS,KACT,KAAM,EAAE,EAAE,KAEZ,cACE,QAAS,KACT,KAAM,EAAE,EAAE,KAEZ,iBACE,iBAAkB,QAClB,iBAAkB,KAClB,oBAAqB,EAAE,EACvB,kBAAmB,OACnB,aAAc,QACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,WAAY,WACZ,MAAO,KACP,OAAQ,QACR,QAAS,aACT,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,WAAY,OACZ,YAAa,IACb,eAAgB,OAChB,YAAa,KACb,OAAQ,EACR,QAAS,EACT,QAAS,IAAI,KACb,WAAY,OACZ,gBAAiB,KACjB,eAAgB,WAChB,YAAa,OAEf,2BACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,qBACP,OAAQ,YAEV,sCACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,KAET,sCACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,KAET,uCACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,KAET,4BACE,iBAAkB,QAClB,iBAAkB,KAClB,oBAAqB,EAAE,EACvB,kBAAmB,OACnB,aAAc,QACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,MAAO,QACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,eAAgB,OAChB,QAAS,EACT,QAAS,IAAI,KACb,gBAAiB,KACjB,eAAgB,WAElB,sCACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,kBAET,iDACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,iDACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,kDACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,uBACA,kCACA,wDACE,QAAS,IAEX,qCACA,gDACA,sEACE,QAAS,MACT,KAAM,aAER,sBACE,WAAY,EACZ,OAAQ,KACR,WAAY,WACZ,OAAQ,QACR,QAAS,aACT,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,YAAa,IACb,YAAa,IACb,OAAQ,EACR,QAAS,EACT,YAAa,OAEf,0BACE,UAAW,KAEb,wBACE,iBAAkB,YAClB,aAAc,YACd,WAAY,MACZ,MAAO,QAET,kCACE,iBAAkB,QAClB,aAAc,QACd,WAAY,KACZ,MAAO,kBAET,6CACE,iBAAkB,QAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,6CACE,iBAAkB,QAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,8CACE,iBAAkB,QAClB,aAAc,QACd,WAAY,KACZ,MAAO,QAET,sCACE,KAAM,aAER,8DACE,MAAO,QAET,mBACE,YAAa,OACb,cAAe,IACf,OAAQ,QACR,QAAS,KACT,OAAQ,KACR,UAAW,KAEb,0BAEE,OAAQ,IACR,SAAU,OACV,SAAU,SACV,IAAK,KACL,MAAO,IAET,0BACE,YAAa,OACb,cAAe,IACf,WAAY,EAAE,EAAE,EAAE,IAAI,YACtB,WAAY,YACZ,QAAS,KACT,OAAQ,KACR,gBAAiB,OACjB,QAAS,gBACT,MAAO,KAET,4DACE,QAAS,MACT,KAAM,kBAER,gEACE,QAAS,KACT,KAAM,QAER,0DACE,QAAS,KACT,KAAM,QAER,6BACE,MAAO,kBACP,OAAQ,YAEV,kFACE,KAAM,kBAER,oFACE,KAAM,kBAER,wFACE,KAAM,kBAER,8FACE,QAAS,KAEX,4FACE,QAAS,MAEX,oGACE,QAAS,KAEX,wGACE,QAAS,MAEX,0DACE,cAAe,IACf,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,QAAS,gBAEX,yCACE,YAAa,IAEf,yCACE,KAAM,SAER,2CACE,YAAa,IAEf,mCACE,aAAc,IAEhB,mCACE,MAAO,SAET,qCACE,aAAc,IAKhB,qDACE,QAAS,KACT,QAAS,EAEX,kDACE,QAAS,KACT,UAAW,KACX,WAAY,MACZ,WAAY,OACZ,WAAY,KACZ,QAAS,EAEX,kDACE,oBAAqB,EACrB,aAAc,KACd,kBAAmB,EACnB,mBAAoB,EACpB,aAAc,MACd,iBAAkB,IAClB,QAAS,IAAI,EAEf,8DACE,iBAAkB,EAEpB,oCACE,iBAAkB,QAClB,MAAO,kBACP,OAAQ,QACR,UAAW,KACX,WAAY,OACZ,YAAa,IACb,cAAe,IACf,WAAY,KACZ,QAAS,IAAI,IACb,eAAgB,KAChB,sBAAuB,KACvB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,2BACE,YAAa,OACb,MAAO,QACP,OAAQ,QACR,QAAS,KACT,sBAAuB,KACvB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,iDACE,QAAS,IAAI,IAEf,oDACE,cAAe,IACf,QAAS,IAEX,iDACE,cAAe,IACf,QAAS,IAEX,0DACE,iBAAkB,KAClB,MAAO,QAET,yDACE,iBAAkB,QAEpB,6DACE,iBAAkB,QAClB,MAAO,QAET,4DACE,iBAAkB,QAEpB,0DACE,iBAAkB,QAClB,MAAO,QAET,oGACE,iBAAkB,QAClB,MAAO,QAET,oGACE,MAAO,QAET,uGACE,MAAO,QAET,2CACE,iBAAkB,YAClB,MAAO,kBACP,OAAQ,YAGV,qCADA,gCAEE,YAAa,OACb,QAAS,KACT,OAAQ,KACR,gBAAiB,OACjB,MAAO,KAGT,yCADA,oCAEE,KAAM,aAER,4DACE,OAAQ,KACR,MAAO,KAET,iCACE,MAAO,aACP,QAAS,aACT,KAAM,EACN,wBAAyB,KACzB,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,KACb,eAAgB,KAChB,WAAY,UAEd,qCACE,MAAO,kBACP,QAAS,aACT,UAAW,KACX,OAAQ,KACR,YAAa,KACb,eAAgB,KAElB,iCACE,YAAa,OACb,QAAS,KACT,WAAY,KAEd,wCACE,QAAS,GACT,UAAW,EACX,WAAY,QAEd,qCACE,KAAM,QAER,yHACE,QAAS,KAEX,qJACE,QAAS,KAEX,iCACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,gBACtB,QAAS,KACT,KAAM,EAAE,EAAE,KACV,YAAa,EACb,UAAW,OACX,cAAe,EACf,WAAY,KACZ,QAAS,EAEX,wDACE,YAAa,OACb,QAAS,KACT,UAAW,OACX,OAAQ,EACR,QAAS,EAAE,IAEb,uDACE,OAAQ,KACR,OAAQ,IAAI,EAAE,IAAI,EAClB,QAAS,EAAE,IAEb,6DACE,YAAa,OAEf,6DACE,YAAa,IAEf,0FACE,aAAc,IAAI,MAAM,KAE1B,mFACE,YAAa,IAEf,kFACE,YAAa,IAEf,oDACE,YAAa,KACb,WAAY,MAEd,gEACE,YAAa,KAEf,oFACE,YAAa,IAAI,MAAM,KAEzB,6EACE,aAAc,IAEhB,4EACE,aAAc,IAKhB,4EACE,UAAW,gBAEb,8CACE,aAAc,KACd,WAAY,KAEd,0DACE,aAAc,KACd,UAAW,gBAEb,sEACE,aAAc,IAEhB,iCACE,QAAS,KACT,eAAgB,IAChB,OAAQ,MACR,OAAQ,EAEV,qBACE,WAAY,WACZ,QAAS,KACT,OAAQ,KAEV,8BACE,OAAQ,KAEV,qBACA,8BACE,MAAO,MAET,2BACE,WAAY,IACZ,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,YACZ,OAAQ,KACR,SAAU,SACV,MAAO,KAET,iCACE,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,OAAQ,KACR,SAAU,SACV,MAAO,KAET,qBACE,WAAY,WACZ,OAAQ,KACR,MAAO,KAET,8BACE,WAAY,4GACZ,OAAQ,KACR,MAAO,KAET,qBACA,8BACE,MAAO,KAET,2BACE,WAAY,KACZ,OAAQ,IAAI,MAAM,KAClB,WAAY,YACZ,OAAQ,IACR,MAAO,KAET,mBACE,QAAS,KACT,eAAgB,OAChB,gBAAiB,cAEnB,uBACE,YAAa,OACb,QAAS,KACT,gBAAiB,cACjB,cAAe,IACf,MAAO,QAET,yBACE,MAAO,IAET,qCAEE,OAAQ,IAAI,MAAM,cAEpB,qCACE,OAAQ,IAAI,MAAM,KAClB,UAAW,EACX,cAAe,EAEjB,oCACE,aAAc,KAEhB,oCACE,aAAc,KAEhB,0CACE,YAAa,KAEf,wCACE,aAAc,KAEhB,8BACE,YAAa,KAEf,8BACE,YAAa,KAEf,oCACE,aAAc,KAEhB,kCACE,YAAa,KAEf,gCAEA,0CADA,yCAEE,OAAQ,IAAI,EAAE,IAAI,IAEpB,qEACE,OAAQ,EACR,OAAQ,KAAK,EAEf,wBACE,QAAS,KAEX,iBACE,OAAQ,KACR,WAAY,UAAU,IAAK,CAAE,WAAW,KACxC,MAAO,KAGT,uBADA,uBAEE,WAAY,EAAE,EAAE,EAAE,IAAI,qBAAyB,MAC/C,UAAW,UAEb,yBACE,YAAa,OACb,QAAS,KACT,gBAAiB,OAEnB,kCACE,OAAQ,QAEV,+BACE,YAAa,OACb,iBAAkB,YAClB,OAAQ,EACR,OAAQ,QACR,QAAS,KACT,OAAQ,KACR,gBAAiB,OACjB,QAAS,EACT,QAAS,EACT,MAAO,KAET,mCACE,OAAQ,KACR,MAAO,KAET,qCACE,WAAY,QAEd,8CACE,YAAa,KAEf,wCACE,aAAc,KAEhB,yBACE,WAAY,KACZ,SAAU,SAEZ,4CACE,WAAY,IAEd,kBACE,WAAY,KACZ,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,EAAE,kBACxB,QAAS,IAAI,IAAI,KAAK,IACtB,SAAU,SAEZ,0BACE,YAAa,OACb,MAAO,QACP,QAAS,KACT,gBAAiB,cAEnB,wBACE,MAAO,kBACP,UAAW,KAEb,wBACE,MAAO,QACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,IACb,WAAY,IACZ,SAAU,SACV,eAAgB,QAElB,iCACE,OAAQ,KACR,YAAa,OACb,MAAO,KAET,4BACE,YAAa,IAEf,8BACE,MAAO,kBACP,UAAW,KACX,WAAY,OAEd,0BACE,OAAQ,EAEV,iCACE,YAAa,KACb,WAAY,OAEd,yCACE,WAAY,KACZ,OAAQ,EACR,QAAS,GACT,QAAS,KACT,KAAM,EACN,QAAS,GACT,SAAU,SACV,MAAO,EACP,IAAK,EACL,QAAS,EAEX,yBACE,QAAS,KACT,YAAa,EACb,UAAW,KACX,gBAAiB,SACjB,WAAY,IAEd,sCACE,cAAe,IACf,MAAO,KAET,wBACE,QAAS,KACT,UAAW,KACX,gBAAiB,SACjB,WAAY,KAEd,mCACE,WAAY,0CACZ,OAAQ,EACR,QAAS,GACT,QAAS,MACT,OAAQ,IACR,WAAY,MACZ,SAAU,SACV,MAAO,KAET,2BACE,WAAY,KACZ,OAAQ,EACR,QAAS,KACT,eAAgB,OAChB,UAAW,EACX,KAAM,EACN,QAAS,GACT,SAAU,SACV,MAAO,EACP,WAAY,OACZ,IAAK,EACL,QAAS,EAEX,gCACE,YAAa,OACb,MAAO,QACP,QAAS,KACT,eAAgB,OAChB,SAAU,SAEZ,oCACE,eAAgB,KAElB,+BACE,OAAQ,EACR,eAAgB,OAChB,UAAW,KACX,KAAM,EACN,QAAS,IACT,SAAU,SACV,MAAO,EACP,IAAK,EACL,QAAS,GAEX,iCACE,iBAAkB,KAClB,WAAY,EAAE,EAAE,IAAI,IAAI,KACxB,MAAO,QACP,WAAY,OAEd,kDACE,UAAW,KAEb,gCACE,YAAa,OACb,iBAAkB,KAClB,OAAQ,EACR,QAAS,KACT,gBAAiB,OACjB,KAAM,EACN,SAAU,SACV,MAAO,EACP,IAAK,EACL,QAAS,GAEX,0BACE,QAAS,KACT,eAAgB,OAChB,YAAa,EACb,SAAU,KAEZ,wBACE,OAAQ,IAEV,uCACE,YAAa,IAEf,4DACA,mDACA,oDACE,YAAa,IAEf,iCACE,aAAc,IAEhB,sDACA,6CACA,8CACE,aAAc,IAEhB,eACE,YAAa,OACb,QAAS,KAEX,2BACE,KAAM,kBAER,qBACE,MAAO,kBACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,eAAgB,UAElB,0CACE,aAAc,IAEhB,sDACE,YAAa,IAEf,oCACE,YAAa,IAEf,gDACE,aAAc,IAEhB,sBACE,YAAa,OACb,OAAQ,EACR,QAAS,KACT,gBAAiB,OACjB,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,QAAS,KAEX,gCACE,iBAAkB,sBAClB,OAAQ,EACR,KAAM,EACN,SAAU,SACV,MAAO,EACP,IAAK,EACL,QAAS,EAEX,wCACE,iBAAkB,KAEpB,iBACE,iBAAkB,KAClB,aAAc,KACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,EAAE,KAAK,KAAK,MAAM,kBAAsB,CAAE,EAAE,EAAE,KAAK,IAAI,mBACnE,QAAS,KACT,eAAgB,OAChB,WAAY,KACZ,UAAW,MACX,SAAU,OACV,SAAU,SACV,MAAO,KACP,QAAS,EAEX,yCACE,8CACE,WAAY,WACZ,OAAQ,IAAI,KACZ,MAAO,oBAGX,wBACE,QAAS,KAEX,yBACE,YAAa,OACb,iBAAkB,KAClB,cAAe,KACf,MAAO,QACP,QAAS,KACT,UAAW,KACX,gBAAiB,cACjB,QAAS,IAAI,KAAK,EAAE,KACpB,SAAU,SAEZ,qCACE,QAAS,EAEX,6BACE,OAAQ,KACR,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAET,oCACE,OAAQ,SAEV,0BACE,YAAa,KAEf,wBACE,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,IACb,OAAQ,EACR,eAAgB,KAElB,uBACE,MAAO,QACP,QAAS,KACT,KAAM,EACN,wBAAyB,KACzB,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,IACb,UAAW,EACX,WAAY,KACZ,eAAgB,KAElB,yCACE,oDACE,eAAgB,QAGpB,2BACE,YAAa,WACb,QAAS,KACT,eAAgB,OAChB,QAAS,KAAK,KAEhB,yCACE,wDACE,eAAgB,IAChB,2BAA4B,MAC5B,WAAY,KACZ,eAAgB,GAGpB,gCACE,cAAe,IAAI,MAAM,YACzB,MAAO,kBACP,QAAS,aACT,UAAW,KACX,YAAa,IACb,cAAe,IACf,gBAAiB,KACjB,YAAa,OAEf,sCACE,iBAAkB,oBAEpB,wCACE,cAAe,IAAI,MAAM,QACzB,MAAO,QAET,+BACE,WAAY,WACZ,QAAS,KACT,KAAM,EACN,eAAgB,OAChB,wBAAyB,KACzB,WAAY,MACZ,SAAU,KACV,2BAA4B,MAC5B,QAAS,KAAK,KAEhB,iCACE,cAAe,EACf,WAAY,KAEd,4CACE,WAAY,EAEd,2CACE,cAAe,EAEjB,2CACE,cAAe,EACf,WAAY,EAEd,iCACE,MAAO,QACP,OAAQ,QACR,gBAAiB,KAGnB,uCADA,uCAEE,MAAO,QACP,gBAAiB,KAEnB,wCACE,MAAO,QACP,gBAAiB,KAEnB,mCACE,KAAM,QAER,kCACE,QAAS,MACT,gBAAiB,KACjB,cAAe,KACf,mBAAoB,EACZ,kBAAmB,EAC3B,qBAAsB,EACd,oBAAqB,EAC7B,sBAAuB,OACf,qBAAsB,OAEhC,mDACE,MAAO,QACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,eAAgB,OAChB,cAAe,KACf,WAAY,KACZ,eAAgB,KAElB,mDACE,MAAO,QACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,eAAgB,OAChB,cAAe,KACf,WAAY,KACZ,eAAgB,KAElB,kDACE,cAAe,KAEjB,+DACA,+DACA,8DACE,WAAY,EAEd,8DACA,8DACA,6DACE,cAAe,EAEjB,8DACA,8DACA,6DACE,cAAe,EACf,WAAY,EAEd,2BACE,OAAQ,MACR,UAAW,OAEb,2BACE,UAAW,MAEb,qDACE,SAAU,KAEZ,yCACE,WAAY,OAEd,yBACE,YAAa,OACb,iBAAkB,KAClB,WAAY,IAAI,MAAM,KACtB,QAAS,KACT,gBAAiB,cACjB,QAAS,IAAI,KAGf,6BADA,+BAEE,QAAS,KAEX,+BACE,YAAa,OACb,iBAAkB,sBAClB,OAAQ,EACR,QAAS,KACT,gBAAiB,OACjB,KAAM,EACN,SAAU,SACV,MAAO,EACP,IAAK,EACL,QAAS,EAEX,wBACE,gBAAiB,SACjB,MAAO,KAET,iCACE,YAAa,IACb,eAAgB,IAElB,iCACE,cAAe,IAAI,MAAM,KAE3B,4CACE,cAAe,KAEjB,2BACE,eAAgB,IAChB,YAAa,IAEf,yBACE,SAAU,SACV,MAAO,KACP,QAAS,KAEX,8BACE,QAAS,KACT,KAAM,EACN,eAAgB,OAChB,wBAAyB,KAE3B,0CACE,QAAS,KACT,KAAM,EACN,wBAAyB,KAE3B,wDACE,KAAM,EACN,wBAAyB,KACzB,OAAQ,KAEV,8BACE,QAAS,EACT,WAAY,OAEd,6BACE,QAAS,EACT,WAAY,QAEd,iCACE,WAAY,WAAW,GAAG,OAAO,GAAI,CAAE,QAAQ,IAAK,KAEtD,wDACE,iBAAkB,GAEpB,gCACE,SAAU,OAKZ,sCACE,SAAU,iBAEZ,yCACE,uEACE,aAAc,GAGlB,yCACE,8FACE,YAAa,KAIjB,kEADA,oEAEE,YAAa,IAEf,gCACE,WAAY,MAEd,yCACE,iEACE,YAAa,GAGjB,yCACE,wFACE,aAAc,KAIlB,4DADA,8DAEE,aAAc,IAEhB,6BACE,QAAS,KACT,KAAM,EACN,wBAAyB,KAE3B,mBACE,YAAa,OACb,WAAY,KACZ,OAAQ,IAAI,OAAO,KACnB,WAAY,WACZ,QAAS,KACT,eAAgB,OAChB,UAAW,EACX,gBAAiB,OACjB,WAAY,MACZ,QAAS,KAEX,qBACE,MAAO,kBACP,OAAQ,EAAE,EAAE,KAAK,EAEnB,oBACE,QAAS,KACT,KAAM,EACN,wBAAyB,KACzB,SAAU,OACV,SAAU,SAEZ,4BACE,iBAAkB,KAClB,OAAQ,EACR,WAAY,WACZ,KAAM,EACN,wBAAyB,KACzB,OAAQ,KACR,SAAU,SACV,MAAO,KAET,0BACE,OAAQ,IAAI,OAAO,KAErB,2BACE,QAAS,KACT,KAAM,EAAE,EAAE,KACV,eAAgB,OAChB,SAAU,OAEZ,wBACE,QAAS,EAEX,iDACE,WAAY,KACZ,WAAY,WAAW,IAEzB,oDACA,2CACE,cAAe,KAEjB,uDACE,WAAY,EAAE,IAAI,IAAI,KAAK,gBAE7B,yBACE,QAAS,EACT,WAAY,OAEd,wBACE,QAAS,EACT,WAAY,QAEd,4BACE,WAAY,WAAW,GAAG,OAAO,IAAK,CAAE,QAAQ,KAAM,KAExD,mDACE,iBAAkB,GAEpB,uBACE,KAAM,EACN,SAAU,SAEZ,qGACA,qGACA,iGACE,QAAS,KAEX,2BACE,QAAS,MAEX,yCACE,SAAU,SACV,IAAK,IACL,UAAW,iBAEb,gDACE,KAAM,KAER,gDACE,KAAM,OAER,8CACE,KAAM,MAER,qEACA,qEACA,mEACE,cAAe,KAEjB,wDACE,MAAO,IAET,+DACA,+DACA,6DACE,aAAc,KAEhB,kDACE,KAAM,IAER,wBACE,UAAW,KAEb,kCACE,UAAW,KAEb,qDACE,YAAa,IAEf,sBACE,QAAS,KACT,SAAU,SACV,QAAS,EAEX,qCACE,QAAS,GAEX,2BACE,aAAc,kBACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,WAAY,WACZ,OAAQ,KACR,SAAU,SACV,IAAK,IACL,MAAO,KAGT,2DADA,2DAEE,aAAc,QACd,OAAQ,QAEV,mCACE,iBAAkB,0DAAgE,CAAE,2DAAiE,CAAE,0DAAgE,CAAE,4DACzN,oBAAqB,EAAE,CAAC,CAAE,EAAE,GAAG,CAAE,IAAI,IAAI,CAAE,KAAK,EAChD,gBAAiB,KAAK,KACtB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,WACZ,QAAS,GACT,OAAQ,KACR,KAAM,KACN,SAAU,SACV,IAAK,KACL,MAAO,KACP,QAAS,GAEX,+CACE,OAAQ,YAKV,oDACE,aAAc,KAEhB,0CACE,KAAM,IAKR,8CACE,cAAe,KAEjB,oCACE,MAAO,IAET,gBACA,wBACE,MAAO,kBACP,QAAS,MACT,UAAW,KACX,WAAY,OACZ,YAAa,IACb,YAAa,IACb,QAAS,EAAE,IAAI,EAAE,EACjB,eAAgB,KAChB,YAAa,OAEf,wBACE,QAAS,EAAE,IAEb,yBACE,QAAS,EAAE,EAAE,EAAE,IAEjB,eACE,QAAS,KACT,KAAM,EACN,eAAgB,OAChB,wBAAyB,KAE3B,sBACE,WAAY,WACZ,cAAe,IAEjB,+BACE,KAAM,EAER,6BACE,MAAO,KAET,kCACE,QAAS,KAEX,qBACE,QAAS,KACT,eAAgB,IAChB,UAAW,KACX,gBAAiB,cAEnB,4CACE,MAAO,sBAET,4CACE,MAAO,2BAET,4CACE,MAAO,sBAET,iCACE,YAAa,OACb,QAAS,KAEX,8BACE,YAAa,OACb,QAAS,KAEX,iCACE,QAAS,KACT,KAAM,EACN,eAAgB,OAChB,wBAAyB,KAE3B,+CACE,KAAM,EACN,wBAAyB,KAE3B,6CACE,QAAS,KACT,KAAM,EACN,wBAAyB,KAE3B,2DACE,KAAM,EACN,wBAAyB,KACzB,OAAQ,KAEV,mEACE,YAAa,IAEf,6DACE,aAAc,IAEhB,iDACA,qDACE,QAAS,KAIX,4CACA,mBAHA,oBACA,4BAGE,mBAAoB,KACjB,gBAAiB,KACZ,WAAY,KACpB,iBAAkB,KAClB,aAAc,KACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,WAAY,WACZ,MAAO,QACP,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,YAAa,KACb,OAAQ,EACR,WAAY,KACZ,QAAS,EACT,QAAS,IAAI,OACb,OAAQ,KACR,MAAO,KAGT,6BADA,8BAEE,iBAAkB,QAClB,MAAO,mBACP,OAAQ,YAGV,kDACA,yBAFA,0BAGE,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,QAAS,EAEX,4BACE,aAAc,EACd,cAAe,IACf,WAAY,IACZ,UAAW,MAEb,oBACE,iBAAkB,YAClB,OAAQ,EACR,aAAc,YACd,WAAY,MACZ,MAAO,QACP,OAAQ,QACR,QAAS,MACT,OAAQ,EACR,QAAS,EAEX,wBACE,QAAS,MACT,KAAM,QAER,6CACE,YAAa,IAEf,uCACE,aAAc,IAEhB,uBACE,OAAQ,QACR,SAAU,SAEZ,sDACE,iBAAkB,QAClB,MAAO,mBACP,OAAQ,YAEV,gCACE,OAAQ,QACR,KAAM,EACN,OAAQ,EAAE,IAEZ,kCACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,MAAO,KAET,sCACE,KAAM,QAER,4CACE,YAAa,OACb,QAAS,KAEX,0CACE,MAAO,IAET,oCACE,KAAM,IAER,sBACE,OAAQ,QACR,SAAU,SAEZ,6BACE,mBAAoB,KACjB,gBAAiB,KACZ,WAAY,KACpB,iBAAkB,KAClB,aAAc,KACd,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,WAAY,WACZ,MAAO,QACP,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,WAAW,CAAE,MAAM,CAAE,SAAS,CAAE,gBAAgB,CAAE,WACtH,UAAW,KACX,YAAa,KACb,OAAQ,EACR,WAAY,KACZ,QAAS,EACT,QAAS,IAAI,OACb,OAAQ,KACR,MAAO,KAET,uCACE,iBAAkB,QAClB,MAAO,mBACP,OAAQ,YAEV,yCACE,QAAS,KAEX,mCACE,iBAAkB,KAClB,aAAc,QACd,WAAY,KACZ,QAAS,EAEX,0BACE,eAAgB,KAChB,SAAU,SACV,IAAK,IACL,UAAW,iBAEb,sDACA,sDACE,cAAe,KAEjB,yCACE,MAAO,IAET,gDACA,gDACE,aAAc,KAEhB,mCACE,KAAM,IAER,mBACE,mBAAoB,SACjB,gBAAiB,SACZ,WAAY,SACpB,YAAa,SAEf,gBACE,OAAQ,EACR,OAAQ,KACR,KAAM,EACN,OAAQ,EACR,SAAU,OACV,oBAAqB,KACjB,oBAAqB,KACzB,QAAS,EACT,SAAU,MACV,IAAK,EACL,aAAc,WACd,MAAO,KAET,8EACE,QAAS,KAEX,gDACE,QAAS,KAEX,qCACE,QAAS,KAEX,0BACE,WAAY,KACZ,WAAY,IAEd,sBACE,MAAO,KAET,+BACE,YAAa,OACb,QAAS,KACT,gBAAiB,OAEnB,6BACE,iBAAkB,KAClB,OAAQ,MACR,SAAU,KACV,SAAU,SACV,MAAO,KAET,6BACA,uDACE,WAAY,IAEd,gCACE,WAAY,wGAEd,2CACE,KAAM,EACN,wBAAyB,KAE3B,yBACE,WAAY,KAEZ,QAAS,GACT,SAAU,SACV,KAAM,EAER,0BACE,OAAQ,IAAI,MAAM,KAClB,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAET,+BACE,OAAQ,EACR,OAAQ,KACR,SAAU,SAEZ,6BACE,aAAc,IAAI,EAAE,EAAE,IACtB,OAAQ,UACR,KAAM,MACN,OAAQ,KAAK,EAAE,EAAE,KACjB,IAAK,MAEP,6BACE,aAAc,IAAI,IAAI,EAAE,EACxB,OAAQ,UACR,KAAM,MACN,OAAQ,KAAK,EAAE,EAAE,MACjB,IAAK,MAEP,6BACE,aAAc,EAAE,EAAE,IAAI,IACtB,OAAQ,UACR,KAAM,MACN,OAAQ,MAAM,IAAI,EAAE,KACpB,IAAK,MAEP,6BACE,aAAc,EAAE,IAAI,IAAI,EACxB,OAAQ,UACR,KAAM,MACN,OAAQ,MAAM,EAAE,EAAE,MAClB,IAAK,MAEP,8EACE,YAAa,IAEf,sEACE,YAAa,KAEf,sEACE,YAAa,KAEf,wEACE,aAAc,IAEhB,gEACE,aAAc,KAEhB,gEACE,aAAc,KAEhB,8BACE,QAAS,KACT,UAAW,KACX,MAAO,MAET,kCACE,aAAc,KACd,aAAc,MACd,aAAc,EAAE,IAAI,IAAI,EACxB,WAAY,WACZ,OAAQ,KACR,MAAO,KAET,2EACE,OAAQ,KAAK,EAEf,iEACE,iBAAkB,oBAClB,aAAc,oBAEhB,qCACE,MAAO,kBACP,QAAS,MACT,UAAW,KACX,QAAS,IACT,WAAY,OACZ,MAAO,KAKT,gEACE,aAAc,EAKhB,4DACE,aAAc,EAMhB,eACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,EAAE,kBACxB,QAAS,aACT,SAAU,OACV,eAAgB,IAChB,QAAS,KAEX,mDACE,QAAS,EAEX,sDACE,QAAS,IAEX,mDACE,QAAS,IASX,iCACA,2BARA,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,wBAGE,OAAQ,EAEV,kBACE,WAAY,+MAA+M,KAAK,EAAE,IAAI,EAAE,KACxO,iBAAkB,KAClB,QAAS,KACT,KAAM,EAAE,EAAE,KACV,YAAa,EACb,UAAW,KACX,QAAS,EAAE,IAAI,EAAE,IAEnB,4FACE,WAAY,IAAI,MAAM,KAGxB,eACE,YAAa,OACb,WAAY,IACZ,OAAQ,EACR,cAAe,IACf,WAAY,KACZ,MAAO,QACP,QAAS,KACT,KAAM,EAAE,EAAE,KACV,UAAW,KACX,WAAY,OACZ,YAAa,IACb,OAAQ,KACR,gBAAiB,OACjB,OAAQ,IAAI,EAAE,IAAI,EAClB,QAAS,EACT,SAAU,OACV,QAAS,EAAE,IACX,eAAgB,KAChB,MAAO,KAET,yBACE,iBAAkB,YAClB,OAAQ,EACR,WAAY,KACZ,MAAO,kBACP,OAAQ,YAEV,oCACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAET,uBACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAET,2DACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAET,6BACE,OAAQ,QACR,YAAa,IACb,OAAQ,EAAE,IAEZ,iDACE,OAAQ,YAEV,+BACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,MAAO,KACP,QAAS,KAEX,uBACE,cAAe,IACf,aAAc,MACd,aAAc,IACd,WAAY,KACZ,WAAY,WACZ,QAAS,SACT,QAAS,KACT,UAAW,KACX,YAAa,IACb,iBAAkB,iBAAkB,KAAK,iBACrC,sBAAuB,iBAAkB,KAAK,iBAClD,WAAY,IACZ,QAAS,EACT,QAAS,IACT,WAAY,UAAU,IAAM,OAAO,CAAE,QAAQ,MAAM,QAErD,yBACE,UAAW,KACX,YAAa,IAEf,yBACE,gBAAiB,UAEnB,2BACE,QAAS,EAEX,gCACE,iBAAkB,QAClB,aAAc,QACd,MAAO,QAET,kCACE,MAAO,QAET,kCACE,MAAO,QAET,oCACE,KAAM,QAER,8BACE,iBAAkB,QAClB,aAAc,QACd,MAAO,QAET,gCACE,MAAO,QAET,gCACE,MAAO,KAET,kCACE,KAAM,QAER,6BACA,gCACE,iBAAkB,QAClB,aAAc,QACd,MAAO,QAET,+BACA,kCACE,MAAO,QAET,+BACA,kCACE,MAAO,QAET,iCACA,oCACE,KAAM,QAER,6BACE,iBAAkB,QAClB,aAAc,QACd,MAAO,QAET,+BACE,MAAO,QAET,+BACE,MAAO,QAET,iCACE,KAAM,QAER,6BACE,mBAAoB,OAChB,WAAY,OAChB,MAAO,QACP,UAAW,KACX,qBAAsB,EACtB,gBAAiB,EACjB,gBAAiB,EACb,kBAAmB,EACvB,kBAAmB,EACnB,aAAc,EACd,aAAc,EACV,eAAgB,EACpB,WAAY,OACZ,YAAa,OACb,WAAY,UACZ,WAAY,WAEd,+BACE,OAAQ,EAEV,iCACE,WAAY,KAEd,6BACE,mBAAoB,OAChB,WAAY,OAChB,qBAAsB,EACtB,gBAAiB,EACjB,gBAAiB,EACb,kBAAmB,EACvB,kBAAmB,EACnB,aAAc,EACd,aAAc,EACV,eAAgB,EACpB,sBAAuB,IACnB,aAAc,IAEpB,iCACE,QAAS,MAEX,gCACE,mBAAoB,MAChB,WAAY,MAChB,qBAAsB,EACtB,gBAAiB,EACjB,gBAAiB,EACb,kBAAmB,EACvB,kBAAmB,EACnB,aAAc,EACd,aAAc,EACV,eAAgB,EACpB,sBAAuB,IACnB,aAAc,IAEpB,yCACE,qBAAsB,EACtB,gBAAiB,EACjB,gBAAiB,EACb,kBAAmB,EACvB,kBAAmB,EACnB,aAAc,EACd,aAAc,EACV,eAAgB,EACpB,sBAAuB,OACnB,aAAc,OAEpB,cACE,QAAS,aACT,SAAU,SAEZ,wBACE,WAAY,MAAM,IAAK,KAEzB,qCACE,UAAW,OAEb,sBACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,gBACtB,UAAW,EACX,SAAU,OAEZ,yCACE,OAAQ,IAAI,IAAI,IAAI,IAEtB,mCACE,iBAAkB,YAClB,cAAe,KAGjB,qBADA,sBAEE,aAAc,MACd,QAAS,GACT,QAAS,MACT,OAAQ,EACR,SAAU,SACV,MAAO,EAGT,qCADA,sCAEE,KAAM,IACN,IAAK,KAEP,qCACE,aAAc,KAAK,YAAY,YAAY,YAC3C,aAAc,IACd,YAAa,KACb,WAAY,KAEd,sCACE,aAAc,KAAQ,YAAY,YAAY,YAC9C,aAAc,IACd,YAAa,KAGf,kCADA,mCAEE,KAAM,IACN,IAAK,EACL,UAAW,kBAEb,kCACE,aAAc,YAAY,YAAY,KAAK,YAC3C,aAAc,IACd,YAAa,KACb,WAAY,IAEd,mCACE,aAAc,YAAY,YAAY,KAAQ,YAC9C,aAAc,IACd,YAAa,KAGf,mCADA,oCAEE,KAAM,EACN,IAAK,gBACL,UAAW,iBAEb,mCACE,aAAc,YAAY,KAAK,YAAY,YAC3C,aAAc,IACd,YAAa,MAEf,oCACE,aAAc,YAAY,KAAQ,YAAY,YAC9C,aAAc,KACd,YAAa,MAGf,oCADA,qCAEE,KAAM,KACN,IAAK,gBACL,UAAW,iBAEb,oCACE,aAAc,YAAY,YAAY,YAAY,KAClD,aAAc,IACd,YAAa,KAEf,qCACE,aAAc,YAAY,YAAY,YAAY,KAClD,aAAc,KACd,YAAa,KAGf,yCADA,0CAEE,KAAM,KAGR,0CADA,2CAEE,KAAM,kBAER,uBACE,QAAS,KACT,eAAgB,IAChB,UAAW,EACX,wBAAyB,EACzB,WAAY,EAEd,kBACE,iBAAkB,KAClB,QAAS,KACT,eAAgB,IAChB,gBAAiB,SAEnB,0BACE,QAAS,KACT,SAAU,OAEZ,kCACE,QAAS,KAEX,wBACE,QAAS,KAEX,kCACE,QAAS,EAEX,gCACE,QAAS,EAEX,mCACA,qCACE,WAAY,MAAM,IAAK,IAAI,CAAE,QAAQ,IAAK,KAE5C,mBACE,iBAAkB,QAClB,aAAc,QACd,aAAc,MACd,aAAc,IACd,WAAY,WACZ,QAAS,aACT,OAAQ,KACR,SAAU,SACV,MAAO,KAET,sCACE,OAAQ,KACR,MAAO,KAET,iBACE,YAAa,OACb,QAAS,KACT,KAAM,EACN,wBAAyB,KACzB,OAAQ,KACR,gBAAiB,OACjB,SAAU,SAEZ,uBACE,iBAAkB,YAClB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,OAAQ,KACR,UAAW,MACX,MAAO,KAET,yBACE,iBAAkB,QAClB,OAAQ,IAAI,MAAM,QAClB,cAAe,IACf,WAAY,KACZ,OAAQ,KACR,KAAM,IACN,SAAU,SACV,IAAK,IACL,UAAW,iBAAiB,iBAC5B,MAAO,KAET,sBACE,SAAU,KAEZ,kBACE,QAAS,KAEX,sBACE,UAAW,kBAAkB,KAAK,YAAY,GAAG,SAAS,KAC1D,iBAAkB,kBAClB,cAAe,KACf,OAAQ,IACR,MAAO,IAET,mCACE,gBAAiB,MAEnB,mCACE,gBAAiB,MAEnB,6BACE,GAEA,KADA,IAEE,UAAW,SAEb,IACE,UAAW,UAGf,uDACE,YAAa,IAEf,iDACE,aAAc,IAEhB,oBACE,YAAa,OACb,iBAAkB,KAClB,WAAY,IAAI,MAAM,KACtB,MAAO,kBACP,QAAS,KACT,KAAM,EAAE,EAAE,KACV,UAAW,KACX,YAAa,IACb,OAAQ,KACR,SAAU,OACV,QAAS,EAAE,IACX,SAAU,SACV,eAAgB,UAElB,oCACE,QAAS,KACT,KAAM,EAAE,EAAE,KACV,gBAAiB,SACjB,SAAU,OAEZ,0BACE,QAAS,KACT,KAAM,EAAE,EAAE,KACV,aAAc,KACd,SAAU,OACV,cAAe,SACf,YAAa,OAEf,4BACE,QAAS,OACT,YAAa,OAEf,+BACE,KAAM,EAAE,EAAE,KACV,YAAa,IAEf,sBACA,+BACA,+BACE,MAAO,kBACP,gBAAiB,KAKnB,qEAHA,qEAIA,8EAHA,8EAIA,8EAHA,8EAIE,OAAQ,QACR,gBAAiB,UAEnB,mCACE,YAAa,SACb,WAAY,QACZ,OAAQ,YACR,QAAS,KACT,KAAM,EAAE,EAAE,KACV,gBAAiB,SACjB,YAAa,KACb,aAAc,KACd,aAAc,IAEhB,uCACE,QAAS,MACT,KAAM,kBAER,2CACE,aAAc,IAEhB,6CACE,YAAa,IAEf,6BACE,eAAgB,YAElB,qCACE,YAAa,IAEf,mBACE,QAAS,KAEX,iCACE,YAAa,OACb,iBAAkB,qBAClB,OAAQ,EACR,QAAS,KACT,gBAAiB,OACjB,KAAM,EACN,SAAU,SACV,MAAO,EACP,IAAK,EAEP,eACE,YAAa,OACb,WAAY,IACZ,OAAQ,EACR,cAAe,IACf,WAAY,KACZ,MAAO,QACP,QAAS,KACT,KAAM,EAAE,EAAE,KACV,UAAW,KACX,WAAY,OACZ,YAAa,IACb,OAAQ,KACR,gBAAiB,OACjB,OAAQ,IAAI,EAAE,IAAI,EAClB,QAAS,EACT,SAAU,OACV,QAAS,EACT,eAAgB,KAChB,MAAO,KAET,mBACE,QAAS,MACT,KAAM,QAER,6BACE,aAAc,IACd,cAAe,IACf,MAAO,QAET,qBACE,WAAY,QACZ,OAAQ,EACR,WAAY,KAEd,qBACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAET,yBACE,KAAM,QAER,sBACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAET,0BACE,KAAM,QAER,yBACA,+BACA,wBACA,8BACE,WAAY,IACZ,OAAQ,EACR,WAAY,KACZ,MAAO,kBACP,OAAQ,YAEV,6BACA,mCACA,4BACA,kCAEE,KAAM,kBAER,wBACA,8BACE,WAAY,QACZ,OAAQ,EACR,WAAY,KACZ,MAAO,QAGT,gCADA,0BAEE,UAAW,KAEb,4BACA,kCAEE,KAAM,QAER,8CACE,MAAO,QAET,kDACE,KAAM,QAER,wBACE,UAAW,KAEb,mBACE,OAAQ,KACR,MAAO,KAET,mBACE,eAAgB,OAChB,OAAQ,KACR,MAAO,KAET,uBACE,mBAAoB,QAChB,WAAY,QAChB,OAAQ,MACR,MAAO,KAET,wBACE,QAAS,EAAE,IACX,MAAO,MAET,uBACE,QAAS,MACT,UAAW,KACX,YAAa,IACb,eAAgB,QAChB,cAAe,IACf,YAAa,OAEf,uBACE,OAAQ,IAAI,EAAE,IAAI,EAClB,QAAS,EAAE,IACX,MAAO,KAET,6BACE,OAAQ,QACR,YAAa,IACb,OAAQ,EAAE,IAEZ,+BACE,YAAa,OACb,QAAS,KACT,gBAAiB,OACjB,MAAO,KAET,mCACE,KAAM,kBAER,gDACE,SAAU,OACV,cAAe,SACf,YAAa,OACb,MAAO,IAET,uBACE,OAAQ,EACR,cAAe,IACf,WAAY,WACZ,QAAS,KACT,OAAQ,IAAI,EAAE,IAAI,EAClB,SAAU,OAEZ,6BACE,WAAY,EAAE,EAAE,EAAE,IAAI,QAAQ,MAEhC,6BACE,WAAY,QACZ,WAAY,KACZ,MAAO,QAET,yBACE,cAAe,EAEjB,gCACE,MAAO,KAET,oCACE,KAAM,kBAER,iCACE,OAAQ,EAEV,gEACE,MAAO,KAET,mDACE,MAAO,KAKT,0DADA,0DADA,gDADA,gDAIE,WAAY,IACZ,WAAY,KACZ,MAAO,kBAET,2BACE,iBAAkB,KAEpB,kBAEA,4BADA,2BAEE,WAAY,+MAA+M,KAAK,EAAE,IAAI,EAAE,KACxO,iBAAkB,KAClB,QAAS,KACT,KAAM,EAAE,EAAE,KACV,YAAa,EACb,UAAW,KACX,QAAS,EAAE,EAEb,0DACE,OAAQ,EACR,QAAS,EACT,eAAgB,EAChB,YAAa,EACb,WAAY,OAEd,qCACE,WAAY,OAAO,IAAK,IAAI,CAAE,QAAQ,IAAK,OAAO,IAEpD,uCACE,WAAY,QAAQ,IAAK,IAAI,CAAE,OAAO,IAAK,OAAO,GAAI,CAAE,WAAW,GAAG,OAAO,IAE/E,+BACA,8DACE,WAAY,IAAI,MAAM,KACtB,WAAY,KAEd,6BACE,UAAW,OACX,WAAY,KAEd,2BACE,aAAc,EAEhB,8BACE,iBAAkB,KAGpB,mIADA,oGAEE,WAAY,IAAI,MAAM,KAExB,4CACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,gBAExB,sCACE,UAAW,gBAEb,yBACE,YAAa,OACb,QAAS,KACT,UAAW,KACX,OAAQ,EAAE,EACV,QAAS,EAAE,IAAI,EAAE,IAEnB,qCACE,YAAa,KAEf,iDACE,YAAa,EACb,UAAW,OAEb,2DACE,aAAc,IAAI,MAAM,KAE1B,qDACE,YAAa,IAAI,MAAM,KAEzB,kBACE,QAAS,aACT,QAAS,IACT,SAAU,SAEZ,wBACE,iBAAkB,QAClB,cAAe,IACf,WAAY,EAAE,IAAI,IAAI,kBACtB,MAAO,sBACP,UAAW,KACX,WAAY,OACZ,YAAa,IACb,QAAS,IAAI,IACb,eAAgB,KAElB,yBACE,SAAU,SAEZ,4CACE,YAAa,IAAI,MAAM,YACvB,aAAc,IAAI,MAAM,YACxB,WAAY,IAAI,MAAM,QACtB,OAAQ,EACR,KAAM,IACN,SAAU,SACV,UAAW,iBAEb,0CACE,cAAe,IAAI,MAAM,QACzB,YAAa,IAAI,MAAM,YACvB,aAAc,IAAI,MAAM,YACxB,KAAM,IACN,SAAU,SACV,IAAK,EACL,UAAW,iBAEb,6CACE,cAAe,IAAI,MAAM,YACzB,YAAa,IAAI,MAAM,QACvB,WAAY,IAAI,MAAM,YACtB,SAAU,SACV,MAAO,EACP,IAAK,IACL,UAAW,iBAEb,4CACE,cAAe,IAAI,MAAM,YACzB,aAAc,IAAI,MAAM,QACxB,WAAY,IAAI,MAAM,YACtB,KAAM,EACN,SAAU,SACV,IAAK,IACL,UAAW,iBAEb,eACE,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,QAAS,IACT,MAAO,KAET,4BACE,WAAY,EAEd,2BACE,cAAe,EAEjB,2BACE,OAAQ,EAEV,wBACE,OAAQ,IAAI,MAAM,KAClB,cAAe,IACf,QAAS,KACT,KAAM,EACN,SAAU,SAMZ,iCACE,iBAAkB,eAClB,QAAS,GACT,OAAQ,KACR,SAAU,SACV,MAAO,KACP,QAAS,KAEX,cACE,OAAQ,QAEV,6BACE,QAAS,KACT,KAAM,EACN,wBAAyB,KAE3B,+CACE,QAAS,KACT,KAAM,EACN,wBAAyB,KAE3B,iCACE,OAAQ,KAEV,+BACE,OAAQ", "file": "skin.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\n.tox {\n  box-sizing: content-box;\n  color: #222f3e;\n  cursor: auto;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 16px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: normal;\n  -webkit-tap-highlight-color: transparent;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  vertical-align: initial;\n  white-space: normal;\n}\n.tox *:not(svg):not(rect) {\n  box-sizing: inherit;\n  color: inherit;\n  cursor: inherit;\n  direction: inherit;\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  line-height: inherit;\n  -webkit-tap-highlight-color: inherit;\n  text-align: inherit;\n  text-decoration: inherit;\n  text-shadow: inherit;\n  text-transform: inherit;\n  vertical-align: inherit;\n  white-space: inherit;\n}\n.tox *:not(svg):not(rect) {\n  /* stylelint-disable-line no-duplicate-selectors */\n  background: transparent;\n  border: 0;\n  float: none;\n  height: auto;\n  margin: 0;\n  max-width: none;\n  outline: 0;\n  padding: 0;\n  position: static;\n  width: auto;\n}\n.tox:not([dir=rtl]) {\n  direction: ltr;\n  text-align: left;\n}\n.tox[dir=rtl] {\n  direction: rtl;\n  text-align: right;\n}\n.tox-tinymce {\n  border: 1px solid #cccccc;\n  border-radius: 0;\n  box-shadow: none;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  overflow: hidden;\n  position: relative;\n  visibility: inherit !important;\n}\n.tox-tinymce-inline {\n  border: none;\n  box-shadow: none;\n}\n.tox-tinymce-inline .tox-editor-header {\n  border: 1px solid #cccccc;\n  border-radius: 0;\n  box-shadow: none;\n}\n.tox-tinymce-aux {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  z-index: 1300;\n}\n.tox-tinymce *:focus,\n.tox-tinymce-aux *:focus {\n  outline: none;\n}\nbutton::-moz-focus-inner {\n  border: 0;\n}\n.tox .accessibility-issue__header {\n  align-items: center;\n  display: flex;\n  margin-bottom: 4px;\n}\n.tox .accessibility-issue__description {\n  align-items: stretch;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  display: flex;\n  justify-content: space-between;\n}\n.tox .accessibility-issue__description > div {\n  padding-bottom: 4px;\n}\n.tox .accessibility-issue__description > div > div {\n  align-items: center;\n  display: flex;\n  margin-bottom: 4px;\n}\n.tox .accessibility-issue__description > *:last-child:not(:only-child) {\n  border-color: #cccccc;\n  border-style: solid;\n}\n.tox .accessibility-issue__repair {\n  margin-top: 16px;\n}\n.tox .tox-dialog__body-content .accessibility-issue--info .accessibility-issue__description {\n  background-color: rgba(32, 122, 183, 0.1);\n  border-color: rgba(32, 122, 183, 0.4);\n  color: #222f3e;\n}\n.tox .tox-dialog__body-content .accessibility-issue--info .accessibility-issue__description > *:last-child {\n  border-color: rgba(32, 122, 183, 0.4);\n}\n.tox .tox-dialog__body-content .accessibility-issue--info .tox-form__group h2 {\n  color: #207ab7;\n}\n.tox .tox-dialog__body-content .accessibility-issue--info .tox-icon svg {\n  fill: #207ab7;\n}\n.tox .tox-dialog__body-content .accessibility-issue--info a .tox-icon {\n  color: #207ab7;\n}\n.tox .tox-dialog__body-content .accessibility-issue--warn .accessibility-issue__description {\n  background-color: rgba(255, 165, 0, 0.1);\n  border-color: rgba(255, 165, 0, 0.5);\n  color: #222f3e;\n}\n.tox .tox-dialog__body-content .accessibility-issue--warn .accessibility-issue__description > *:last-child {\n  border-color: rgba(255, 165, 0, 0.5);\n}\n.tox .tox-dialog__body-content .accessibility-issue--warn .tox-form__group h2 {\n  color: #cc8500;\n}\n.tox .tox-dialog__body-content .accessibility-issue--warn .tox-icon svg {\n  fill: #cc8500;\n}\n.tox .tox-dialog__body-content .accessibility-issue--warn a .tox-icon {\n  color: #cc8500;\n}\n.tox .tox-dialog__body-content .accessibility-issue--error .accessibility-issue__description {\n  background-color: rgba(204, 0, 0, 0.1);\n  border-color: rgba(204, 0, 0, 0.4);\n  color: #222f3e;\n}\n.tox .tox-dialog__body-content .accessibility-issue--error .accessibility-issue__description > *:last-child {\n  border-color: rgba(204, 0, 0, 0.4);\n}\n.tox .tox-dialog__body-content .accessibility-issue--error .tox-form__group h2 {\n  color: #c00;\n}\n.tox .tox-dialog__body-content .accessibility-issue--error .tox-icon svg {\n  fill: #c00;\n}\n.tox .tox-dialog__body-content .accessibility-issue--error a .tox-icon {\n  color: #c00;\n}\n.tox .tox-dialog__body-content .accessibility-issue--success .accessibility-issue__description {\n  background-color: rgba(120, 171, 70, 0.1);\n  border-color: rgba(120, 171, 70, 0.4);\n  color: #222f3e;\n}\n.tox .tox-dialog__body-content .accessibility-issue--success .accessibility-issue__description > *:last-child {\n  border-color: rgba(120, 171, 70, 0.4);\n}\n.tox .tox-dialog__body-content .accessibility-issue--success .tox-form__group h2 {\n  color: #78AB46;\n}\n.tox .tox-dialog__body-content .accessibility-issue--success .tox-icon svg {\n  fill: #78AB46;\n}\n.tox .tox-dialog__body-content .accessibility-issue--success a .tox-icon {\n  color: #78AB46;\n}\n.tox .tox-dialog__body-content .accessibility-issue__header h1,\n.tox .tox-dialog__body-content .tox-form__group .accessibility-issue__description h2 {\n  margin-top: 0;\n}\n.tox:not([dir=rtl]) .tox-dialog__body-content .accessibility-issue__header .tox-button {\n  margin-left: 4px;\n}\n.tox:not([dir=rtl]) .tox-dialog__body-content .accessibility-issue__header > *:nth-last-child(2) {\n  margin-left: auto;\n}\n.tox:not([dir=rtl]) .tox-dialog__body-content .accessibility-issue__description {\n  padding: 4px 4px 4px 8px;\n}\n.tox:not([dir=rtl]) .tox-dialog__body-content .accessibility-issue__description > *:last-child {\n  border-left-width: 1px;\n  padding-left: 4px;\n}\n.tox[dir=rtl] .tox-dialog__body-content .accessibility-issue__header .tox-button {\n  margin-right: 4px;\n}\n.tox[dir=rtl] .tox-dialog__body-content .accessibility-issue__header > *:nth-last-child(2) {\n  margin-right: auto;\n}\n.tox[dir=rtl] .tox-dialog__body-content .accessibility-issue__description {\n  padding: 4px 8px 4px 4px;\n}\n.tox[dir=rtl] .tox-dialog__body-content .accessibility-issue__description > *:last-child {\n  border-right-width: 1px;\n  padding-right: 4px;\n}\n.tox .tox-anchorbar {\n  display: flex;\n  flex: 0 0 auto;\n}\n.tox .tox-bar {\n  display: flex;\n  flex: 0 0 auto;\n}\n.tox .tox-button {\n  background-color: #207ab7;\n  background-image: none;\n  background-position: 0 0;\n  background-repeat: repeat;\n  border-color: #207ab7;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  box-sizing: border-box;\n  color: #fff;\n  cursor: pointer;\n  display: inline-block;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: bold;\n  letter-spacing: normal;\n  line-height: 24px;\n  margin: 0;\n  outline: none;\n  padding: 4px 16px;\n  text-align: center;\n  text-decoration: none;\n  text-transform: capitalize;\n  white-space: nowrap;\n}\n.tox .tox-button[disabled] {\n  background-color: #207ab7;\n  background-image: none;\n  border-color: #207ab7;\n  box-shadow: none;\n  color: rgba(255, 255, 255, 0.5);\n  cursor: not-allowed;\n}\n.tox .tox-button:focus:not(:disabled) {\n  background-color: #1c6ca1;\n  background-image: none;\n  border-color: #1c6ca1;\n  box-shadow: none;\n  color: #fff;\n}\n.tox .tox-button:hover:not(:disabled) {\n  background-color: #1c6ca1;\n  background-image: none;\n  border-color: #1c6ca1;\n  box-shadow: none;\n  color: #fff;\n}\n.tox .tox-button:active:not(:disabled) {\n  background-color: #185d8c;\n  background-image: none;\n  border-color: #185d8c;\n  box-shadow: none;\n  color: #fff;\n}\n.tox .tox-button--secondary {\n  background-color: #f0f0f0;\n  background-image: none;\n  background-position: 0 0;\n  background-repeat: repeat;\n  border-color: #f0f0f0;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  color: #222f3e;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: bold;\n  letter-spacing: normal;\n  outline: none;\n  padding: 4px 16px;\n  text-decoration: none;\n  text-transform: capitalize;\n}\n.tox .tox-button--secondary[disabled] {\n  background-color: #f0f0f0;\n  background-image: none;\n  border-color: #f0f0f0;\n  box-shadow: none;\n  color: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-button--secondary:focus:not(:disabled) {\n  background-color: #e3e3e3;\n  background-image: none;\n  border-color: #e3e3e3;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--secondary:hover:not(:disabled) {\n  background-color: #e3e3e3;\n  background-image: none;\n  border-color: #e3e3e3;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--secondary:active:not(:disabled) {\n  background-color: #d6d6d6;\n  background-image: none;\n  border-color: #d6d6d6;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--icon,\n.tox .tox-button.tox-button--icon,\n.tox .tox-button.tox-button--secondary.tox-button--icon {\n  padding: 4px;\n}\n.tox .tox-button--icon .tox-icon svg,\n.tox .tox-button.tox-button--icon .tox-icon svg,\n.tox .tox-button.tox-button--secondary.tox-button--icon .tox-icon svg {\n  display: block;\n  fill: currentColor;\n}\n.tox .tox-button-link {\n  background: 0;\n  border: none;\n  box-sizing: border-box;\n  cursor: pointer;\n  display: inline-block;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 16px;\n  font-weight: normal;\n  line-height: 1.3;\n  margin: 0;\n  padding: 0;\n  white-space: nowrap;\n}\n.tox .tox-button-link--sm {\n  font-size: 14px;\n}\n.tox .tox-button--naked {\n  background-color: transparent;\n  border-color: transparent;\n  box-shadow: unset;\n  color: #222f3e;\n}\n.tox .tox-button--naked[disabled] {\n  background-color: #f0f0f0;\n  border-color: #f0f0f0;\n  box-shadow: none;\n  color: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-button--naked:hover:not(:disabled) {\n  background-color: #e3e3e3;\n  border-color: #e3e3e3;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--naked:focus:not(:disabled) {\n  background-color: #e3e3e3;\n  border-color: #e3e3e3;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--naked:active:not(:disabled) {\n  background-color: #d6d6d6;\n  border-color: #d6d6d6;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-button--naked .tox-icon svg {\n  fill: currentColor;\n}\n.tox .tox-button--naked.tox-button--icon:hover:not(:disabled) {\n  color: #222f3e;\n}\n.tox .tox-checkbox {\n  align-items: center;\n  border-radius: 3px;\n  cursor: pointer;\n  display: flex;\n  height: 36px;\n  min-width: 36px;\n}\n.tox .tox-checkbox__input {\n  /* Hide from view but visible to screen readers */\n  height: 1px;\n  overflow: hidden;\n  position: absolute;\n  top: auto;\n  width: 1px;\n}\n.tox .tox-checkbox__icons {\n  align-items: center;\n  border-radius: 3px;\n  box-shadow: 0 0 0 2px transparent;\n  box-sizing: content-box;\n  display: flex;\n  height: 24px;\n  justify-content: center;\n  padding: calc(4px - 1px);\n  width: 24px;\n}\n.tox .tox-checkbox__icons .tox-checkbox-icon__unchecked svg {\n  display: block;\n  fill: rgba(34, 47, 62, 0.3);\n}\n.tox .tox-checkbox__icons .tox-checkbox-icon__indeterminate svg {\n  display: none;\n  fill: #207ab7;\n}\n.tox .tox-checkbox__icons .tox-checkbox-icon__checked svg {\n  display: none;\n  fill: #207ab7;\n}\n.tox .tox-checkbox--disabled {\n  color: rgba(34, 47, 62, 0.5);\n  cursor: not-allowed;\n}\n.tox .tox-checkbox--disabled .tox-checkbox__icons .tox-checkbox-icon__checked svg {\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-checkbox--disabled .tox-checkbox__icons .tox-checkbox-icon__unchecked svg {\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-checkbox--disabled .tox-checkbox__icons .tox-checkbox-icon__indeterminate svg {\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox input.tox-checkbox__input:checked + .tox-checkbox__icons .tox-checkbox-icon__unchecked svg {\n  display: none;\n}\n.tox input.tox-checkbox__input:checked + .tox-checkbox__icons .tox-checkbox-icon__checked svg {\n  display: block;\n}\n.tox input.tox-checkbox__input:indeterminate + .tox-checkbox__icons .tox-checkbox-icon__unchecked svg {\n  display: none;\n}\n.tox input.tox-checkbox__input:indeterminate + .tox-checkbox__icons .tox-checkbox-icon__indeterminate svg {\n  display: block;\n}\n.tox input.tox-checkbox__input:focus + .tox-checkbox__icons {\n  border-radius: 3px;\n  box-shadow: inset 0 0 0 1px #207ab7;\n  padding: calc(4px - 1px);\n}\n.tox:not([dir=rtl]) .tox-checkbox__label {\n  margin-left: 4px;\n}\n.tox:not([dir=rtl]) .tox-checkbox__input {\n  left: -10000px;\n}\n.tox:not([dir=rtl]) .tox-bar .tox-checkbox {\n  margin-left: 4px;\n}\n.tox[dir=rtl] .tox-checkbox__label {\n  margin-right: 4px;\n}\n.tox[dir=rtl] .tox-checkbox__input {\n  right: -10000px;\n}\n.tox[dir=rtl] .tox-bar .tox-checkbox {\n  margin-right: 4px;\n}\n.tox {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox .tox-collection--toolbar .tox-collection__group {\n  display: flex;\n  padding: 0;\n}\n.tox .tox-collection--grid .tox-collection__group {\n  display: flex;\n  flex-wrap: wrap;\n  max-height: 208px;\n  overflow-x: hidden;\n  overflow-y: auto;\n  padding: 0;\n}\n.tox .tox-collection--list .tox-collection__group {\n  border-bottom-width: 0;\n  border-color: #cccccc;\n  border-left-width: 0;\n  border-right-width: 0;\n  border-style: solid;\n  border-top-width: 1px;\n  padding: 4px 0;\n}\n.tox .tox-collection--list .tox-collection__group:first-child {\n  border-top-width: 0;\n}\n.tox .tox-collection__group-heading {\n  background-color: #e6e6e6;\n  color: rgba(34, 47, 62, 0.7);\n  cursor: default;\n  font-size: 12px;\n  font-style: normal;\n  font-weight: normal;\n  margin-bottom: 4px;\n  margin-top: -4px;\n  padding: 4px 8px;\n  text-transform: none;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n}\n.tox .tox-collection__item {\n  align-items: center;\n  color: #222f3e;\n  cursor: pointer;\n  display: flex;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n}\n.tox .tox-collection--list .tox-collection__item {\n  padding: 4px 8px;\n}\n.tox .tox-collection--toolbar .tox-collection__item {\n  border-radius: 3px;\n  padding: 4px;\n}\n.tox .tox-collection--grid .tox-collection__item {\n  border-radius: 3px;\n  padding: 4px;\n}\n.tox .tox-collection--list .tox-collection__item--enabled {\n  background-color: #fff;\n  color: #222f3e;\n}\n.tox .tox-collection--list .tox-collection__item--active {\n  background-color: #dee0e2;\n}\n.tox .tox-collection--toolbar .tox-collection__item--enabled {\n  background-color: #c8cbcf;\n  color: #222f3e;\n}\n.tox .tox-collection--toolbar .tox-collection__item--active {\n  background-color: #dee0e2;\n}\n.tox .tox-collection--grid .tox-collection__item--enabled {\n  background-color: #c8cbcf;\n  color: #222f3e;\n}\n.tox .tox-collection--grid .tox-collection__item--active:not(.tox-collection__item--state-disabled) {\n  background-color: #dee0e2;\n  color: #222f3e;\n}\n.tox .tox-collection--list .tox-collection__item--active:not(.tox-collection__item--state-disabled) {\n  color: #222f3e;\n}\n.tox .tox-collection--toolbar .tox-collection__item--active:not(.tox-collection__item--state-disabled) {\n  color: #222f3e;\n}\n.tox .tox-collection__item--state-disabled {\n  background-color: transparent;\n  color: rgba(34, 47, 62, 0.5);\n  cursor: not-allowed;\n}\n.tox .tox-collection__item-icon,\n.tox .tox-collection__item-checkmark {\n  align-items: center;\n  display: flex;\n  height: 24px;\n  justify-content: center;\n  width: 24px;\n}\n.tox .tox-collection__item-icon svg,\n.tox .tox-collection__item-checkmark svg {\n  fill: currentColor;\n}\n.tox .tox-collection--toolbar-lg .tox-collection__item-icon {\n  height: 48px;\n  width: 48px;\n}\n.tox .tox-collection__item-label {\n  color: currentColor;\n  display: inline-block;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 24px;\n  text-transform: none;\n  word-break: break-all;\n}\n.tox .tox-collection__item-accessory {\n  color: rgba(34, 47, 62, 0.7);\n  display: inline-block;\n  font-size: 14px;\n  height: 24px;\n  line-height: 24px;\n  text-transform: none;\n}\n.tox .tox-collection__item-caret {\n  align-items: center;\n  display: flex;\n  min-height: 24px;\n}\n.tox .tox-collection__item-caret::after {\n  content: '';\n  font-size: 0;\n  min-height: inherit;\n}\n.tox .tox-collection__item-caret svg {\n  fill: #222f3e;\n}\n.tox .tox-collection--list .tox-collection__item:not(.tox-collection__item--enabled) .tox-collection__item-checkmark svg {\n  display: none;\n}\n.tox .tox-collection--list .tox-collection__item:not(.tox-collection__item--enabled) .tox-collection__item-accessory + .tox-collection__item-checkmark {\n  display: none;\n}\n.tox .tox-collection--horizontal {\n  background-color: #fff;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);\n  display: flex;\n  flex: 0 0 auto;\n  flex-shrink: 0;\n  flex-wrap: nowrap;\n  margin-bottom: 0;\n  overflow-x: auto;\n  padding: 0;\n}\n.tox .tox-collection--horizontal .tox-collection__group {\n  align-items: center;\n  display: flex;\n  flex-wrap: nowrap;\n  margin: 0;\n  padding: 0 4px;\n}\n.tox .tox-collection--horizontal .tox-collection__item {\n  height: 34px;\n  margin: 2px 0 3px 0;\n  padding: 0 4px;\n}\n.tox .tox-collection--horizontal .tox-collection__item-label {\n  white-space: nowrap;\n}\n.tox .tox-collection--horizontal .tox-collection__item-caret {\n  margin-left: 4px;\n}\n.tox:not([dir=rtl]) .tox-collection--horizontal .tox-collection__group:not(:last-of-type) {\n  border-right: 1px solid #cccccc;\n}\n.tox:not([dir=rtl]) .tox-collection--list .tox-collection__item > *:not(:first-child) {\n  margin-left: 8px;\n}\n.tox:not([dir=rtl]) .tox-collection--list .tox-collection__item-label:first-child {\n  margin-left: 4px;\n}\n.tox:not([dir=rtl]) .tox-collection__item-accessory {\n  margin-left: 16px;\n  text-align: right;\n}\n.tox:not([dir=rtl]) .tox-collection .tox-collection__item-caret {\n  margin-left: 16px;\n}\n.tox[dir=rtl] .tox-collection--horizontal .tox-collection__group:not(:last-of-type) {\n  border-left: 1px solid #cccccc;\n}\n.tox[dir=rtl] .tox-collection--list .tox-collection__item > *:not(:first-child) {\n  margin-right: 8px;\n}\n.tox[dir=rtl] .tox-collection--list .tox-collection__item-label:first-child {\n  margin-right: 4px;\n}\n.tox[dir=rtl] .tox-collection__item-icon-rtl {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox[dir=rtl] .tox-collection__item-icon-rtl .tox-collection__item-icon svg {\n  transform: rotateY(180deg);\n}\n.tox[dir=rtl] .tox-collection__item-accessory {\n  margin-right: 16px;\n  text-align: left;\n}\n.tox[dir=rtl] .tox-collection .tox-collection__item-caret {\n  margin-right: 16px;\n  transform: rotateY(180deg);\n}\n.tox[dir=rtl] .tox-collection--horizontal .tox-collection__item-caret {\n  margin-right: 4px;\n}\n.tox .tox-color-picker-container {\n  display: flex;\n  flex-direction: row;\n  height: 225px;\n  margin: 0;\n}\n.tox .tox-sv-palette {\n  box-sizing: border-box;\n  display: flex;\n  height: 100%;\n}\n.tox .tox-sv-palette-spectrum {\n  height: 100%;\n}\n.tox .tox-sv-palette,\n.tox .tox-sv-palette-spectrum {\n  width: 225px;\n}\n.tox .tox-sv-palette-thumb {\n  background: none;\n  border: 1px solid black;\n  border-radius: 50%;\n  box-sizing: content-box;\n  height: 12px;\n  position: absolute;\n  width: 12px;\n}\n.tox .tox-sv-palette-inner-thumb {\n  border: 1px solid white;\n  border-radius: 50%;\n  height: 10px;\n  position: absolute;\n  width: 10px;\n}\n.tox .tox-hue-slider {\n  box-sizing: border-box;\n  height: 100%;\n  width: 25px;\n}\n.tox .tox-hue-slider-spectrum {\n  background: linear-gradient(to bottom, #f00, #ff0080, #f0f, #8000ff, #00f, #0080ff, #0ff, #00ff80, #0f0, #80ff00, #ff0, #ff8000, #f00);\n  height: 100%;\n  width: 100%;\n}\n.tox .tox-hue-slider,\n.tox .tox-hue-slider-spectrum {\n  width: 20px;\n}\n.tox .tox-hue-slider-thumb {\n  background: white;\n  border: 1px solid black;\n  box-sizing: content-box;\n  height: 4px;\n  width: 100%;\n}\n.tox .tox-rgb-form {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n.tox .tox-rgb-form div {\n  align-items: center;\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n  width: inherit;\n}\n.tox .tox-rgb-form input {\n  width: 6em;\n}\n.tox .tox-rgb-form input.tox-invalid {\n  /* Need !important to override Chrome's focus styling unfortunately */\n  border: 1px solid red !important;\n}\n.tox .tox-rgb-form .tox-rgba-preview {\n  border: 1px solid black;\n  flex-grow: 2;\n  margin-bottom: 0;\n}\n.tox:not([dir=rtl]) .tox-sv-palette {\n  margin-right: 15px;\n}\n.tox:not([dir=rtl]) .tox-hue-slider {\n  margin-right: 15px;\n}\n.tox:not([dir=rtl]) .tox-hue-slider-thumb {\n  margin-left: -1px;\n}\n.tox:not([dir=rtl]) .tox-rgb-form label {\n  margin-right: 0.5em;\n}\n.tox[dir=rtl] .tox-sv-palette {\n  margin-left: 15px;\n}\n.tox[dir=rtl] .tox-hue-slider {\n  margin-left: 15px;\n}\n.tox[dir=rtl] .tox-hue-slider-thumb {\n  margin-right: -1px;\n}\n.tox[dir=rtl] .tox-rgb-form label {\n  margin-left: 0.5em;\n}\n.tox .tox-toolbar .tox-swatches,\n.tox .tox-toolbar__primary .tox-swatches,\n.tox .tox-toolbar__overflow .tox-swatches {\n  margin: 2px 0 3px 4px;\n}\n.tox .tox-collection--list .tox-collection__group .tox-swatches-menu {\n  border: 0;\n  margin: -4px 0;\n}\n.tox .tox-swatches__row {\n  display: flex;\n}\n.tox .tox-swatch {\n  height: 30px;\n  transition: transform 0.15s, box-shadow 0.15s;\n  width: 30px;\n}\n.tox .tox-swatch:hover,\n.tox .tox-swatch:focus {\n  box-shadow: 0 0 0 1px rgba(127, 127, 127, 0.3) inset;\n  transform: scale(0.8);\n}\n.tox .tox-swatch--remove {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n.tox .tox-swatch--remove svg path {\n  stroke: #e74c3c;\n}\n.tox .tox-swatches__picker-btn {\n  align-items: center;\n  background-color: transparent;\n  border: 0;\n  cursor: pointer;\n  display: flex;\n  height: 30px;\n  justify-content: center;\n  outline: none;\n  padding: 0;\n  width: 30px;\n}\n.tox .tox-swatches__picker-btn svg {\n  height: 24px;\n  width: 24px;\n}\n.tox .tox-swatches__picker-btn:hover {\n  background: #dee0e2;\n}\n.tox:not([dir=rtl]) .tox-swatches__picker-btn {\n  margin-left: auto;\n}\n.tox[dir=rtl] .tox-swatches__picker-btn {\n  margin-right: auto;\n}\n.tox .tox-comment-thread {\n  background: #fff;\n  position: relative;\n}\n.tox .tox-comment-thread > *:not(:first-child) {\n  margin-top: 8px;\n}\n.tox .tox-comment {\n  background: #fff;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  box-shadow: 0 4px 8px 0 rgba(34, 47, 62, 0.1);\n  padding: 8px 8px 16px 8px;\n  position: relative;\n}\n.tox .tox-comment__header {\n  align-items: center;\n  color: #222f3e;\n  display: flex;\n  justify-content: space-between;\n}\n.tox .tox-comment__date {\n  color: rgba(34, 47, 62, 0.7);\n  font-size: 12px;\n}\n.tox .tox-comment__body {\n  color: #222f3e;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.3;\n  margin-top: 8px;\n  position: relative;\n  text-transform: initial;\n}\n.tox .tox-comment__body textarea {\n  resize: none;\n  white-space: normal;\n  width: 100%;\n}\n.tox .tox-comment__expander {\n  padding-top: 8px;\n}\n.tox .tox-comment__expander p {\n  color: rgba(34, 47, 62, 0.7);\n  font-size: 14px;\n  font-style: normal;\n}\n.tox .tox-comment__body p {\n  margin: 0;\n}\n.tox .tox-comment__buttonspacing {\n  padding-top: 16px;\n  text-align: center;\n}\n.tox .tox-comment-thread__overlay::after {\n  background: #fff;\n  bottom: 0;\n  content: \"\";\n  display: flex;\n  left: 0;\n  opacity: 0.9;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 5;\n}\n.tox .tox-comment__reply {\n  display: flex;\n  flex-shrink: 0;\n  flex-wrap: wrap;\n  justify-content: flex-end;\n  margin-top: 8px;\n}\n.tox .tox-comment__reply > *:first-child {\n  margin-bottom: 8px;\n  width: 100%;\n}\n.tox .tox-comment__edit {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-end;\n  margin-top: 16px;\n}\n.tox .tox-comment__gradient::after {\n  background: linear-gradient(rgba(255, 255, 255, 0), #fff);\n  bottom: 0;\n  content: \"\";\n  display: block;\n  height: 5em;\n  margin-top: -40px;\n  position: absolute;\n  width: 100%;\n}\n.tox .tox-comment__overlay {\n  background: #fff;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  flex-grow: 1;\n  left: 0;\n  opacity: 0.9;\n  position: absolute;\n  right: 0;\n  text-align: center;\n  top: 0;\n  z-index: 5;\n}\n.tox .tox-comment__loading-text {\n  align-items: center;\n  color: #222f3e;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n.tox .tox-comment__loading-text > div {\n  padding-bottom: 16px;\n}\n.tox .tox-comment__overlaytext {\n  bottom: 0;\n  flex-direction: column;\n  font-size: 14px;\n  left: 0;\n  padding: 1em;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 10;\n}\n.tox .tox-comment__overlaytext p {\n  background-color: #fff;\n  box-shadow: 0 0 8px 8px #fff;\n  color: #222f3e;\n  text-align: center;\n}\n.tox .tox-comment__overlaytext div:nth-of-type(2) {\n  font-size: 0.8em;\n}\n.tox .tox-comment__busy-spinner {\n  align-items: center;\n  background-color: #fff;\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 20;\n}\n.tox .tox-comment__scroll {\n  display: flex;\n  flex-direction: column;\n  flex-shrink: 1;\n  overflow: auto;\n}\n.tox .tox-conversations {\n  margin: 8px;\n}\n.tox:not([dir=rtl]) .tox-comment__edit {\n  margin-left: 8px;\n}\n.tox:not([dir=rtl]) .tox-comment__buttonspacing > *:last-child,\n.tox:not([dir=rtl]) .tox-comment__edit > *:last-child,\n.tox:not([dir=rtl]) .tox-comment__reply > *:last-child {\n  margin-left: 8px;\n}\n.tox[dir=rtl] .tox-comment__edit {\n  margin-right: 8px;\n}\n.tox[dir=rtl] .tox-comment__buttonspacing > *:last-child,\n.tox[dir=rtl] .tox-comment__edit > *:last-child,\n.tox[dir=rtl] .tox-comment__reply > *:last-child {\n  margin-right: 8px;\n}\n.tox .tox-user {\n  align-items: center;\n  display: flex;\n}\n.tox .tox-user__avatar svg {\n  fill: rgba(34, 47, 62, 0.7);\n}\n.tox .tox-user__name {\n  color: rgba(34, 47, 62, 0.7);\n  font-size: 12px;\n  font-style: normal;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n.tox:not([dir=rtl]) .tox-user__avatar svg {\n  margin-right: 8px;\n}\n.tox:not([dir=rtl]) .tox-user__avatar + .tox-user__name {\n  margin-left: 8px;\n}\n.tox[dir=rtl] .tox-user__avatar svg {\n  margin-left: 8px;\n}\n.tox[dir=rtl] .tox-user__avatar + .tox-user__name {\n  margin-right: 8px;\n}\n.tox .tox-dialog-wrap {\n  align-items: center;\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  left: 0;\n  position: fixed;\n  right: 0;\n  top: 0;\n  z-index: 1100;\n}\n.tox .tox-dialog-wrap__backdrop {\n  background-color: rgba(255, 255, 255, 0.75);\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 1;\n}\n.tox .tox-dialog-wrap__backdrop--opaque {\n  background-color: #fff;\n}\n.tox .tox-dialog {\n  background-color: #fff;\n  border-color: #cccccc;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: 0 16px 16px -10px rgba(34, 47, 62, 0.15), 0 0 40px 1px rgba(34, 47, 62, 0.15);\n  display: flex;\n  flex-direction: column;\n  max-height: 100%;\n  max-width: 480px;\n  overflow: hidden;\n  position: relative;\n  width: 95vw;\n  z-index: 2;\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox .tox-dialog {\n    align-self: flex-start;\n    margin: 8px auto;\n    width: calc(100vw - 16px);\n  }\n}\n.tox .tox-dialog-inline {\n  z-index: 1100;\n}\n.tox .tox-dialog__header {\n  align-items: center;\n  background-color: #fff;\n  border-bottom: none;\n  color: #222f3e;\n  display: flex;\n  font-size: 16px;\n  justify-content: space-between;\n  padding: 8px 16px 0 16px;\n  position: relative;\n}\n.tox .tox-dialog__header .tox-button {\n  z-index: 1;\n}\n.tox .tox-dialog__draghandle {\n  cursor: grab;\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n.tox .tox-dialog__draghandle:active {\n  cursor: grabbing;\n}\n.tox .tox-dialog__dismiss {\n  margin-left: auto;\n}\n.tox .tox-dialog__title {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 20px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.3;\n  margin: 0;\n  text-transform: none;\n}\n.tox .tox-dialog__body {\n  color: #222f3e;\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  font-size: 16px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.3;\n  min-width: 0;\n  text-align: left;\n  text-transform: none;\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox .tox-dialog__body {\n    flex-direction: column;\n  }\n}\n.tox .tox-dialog__body-nav {\n  align-items: flex-start;\n  display: flex;\n  flex-direction: column;\n  padding: 16px 16px;\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox .tox-dialog__body-nav {\n    flex-direction: row;\n    -webkit-overflow-scrolling: touch;\n    overflow-x: auto;\n    padding-bottom: 0;\n  }\n}\n.tox .tox-dialog__body-nav-item {\n  border-bottom: 2px solid transparent;\n  color: rgba(34, 47, 62, 0.7);\n  display: inline-block;\n  font-size: 14px;\n  line-height: 1.3;\n  margin-bottom: 8px;\n  text-decoration: none;\n  white-space: nowrap;\n}\n.tox .tox-dialog__body-nav-item:focus {\n  background-color: rgba(32, 122, 183, 0.1);\n}\n.tox .tox-dialog__body-nav-item--active {\n  border-bottom: 2px solid #207ab7;\n  color: #207ab7;\n}\n.tox .tox-dialog__body-content {\n  box-sizing: border-box;\n  display: flex;\n  flex: 1;\n  flex-direction: column;\n  -ms-flex-preferred-size: auto;\n  max-height: 650px;\n  overflow: auto;\n  -webkit-overflow-scrolling: touch;\n  padding: 16px 16px;\n}\n.tox .tox-dialog__body-content > * {\n  margin-bottom: 0;\n  margin-top: 16px;\n}\n.tox .tox-dialog__body-content > *:first-child {\n  margin-top: 0;\n}\n.tox .tox-dialog__body-content > *:last-child {\n  margin-bottom: 0;\n}\n.tox .tox-dialog__body-content > *:only-child {\n  margin-bottom: 0;\n  margin-top: 0;\n}\n.tox .tox-dialog__body-content a {\n  color: #207ab7;\n  cursor: pointer;\n  text-decoration: none;\n}\n.tox .tox-dialog__body-content a:hover,\n.tox .tox-dialog__body-content a:focus {\n  color: #185d8c;\n  text-decoration: none;\n}\n.tox .tox-dialog__body-content a:active {\n  color: #185d8c;\n  text-decoration: none;\n}\n.tox .tox-dialog__body-content svg {\n  fill: #222f3e;\n}\n.tox .tox-dialog__body-content ul {\n  display: block;\n  list-style-type: disc;\n  margin-bottom: 16px;\n  -webkit-margin-end: 0;\n          margin-inline-end: 0;\n  -webkit-margin-start: 0;\n          margin-inline-start: 0;\n  -webkit-padding-start: 2.5rem;\n          padding-inline-start: 2.5rem;\n}\n.tox .tox-dialog__body-content .tox-form__group h1 {\n  color: #222f3e;\n  font-size: 20px;\n  font-style: normal;\n  font-weight: bold;\n  letter-spacing: normal;\n  margin-bottom: 16px;\n  margin-top: 2rem;\n  text-transform: none;\n}\n.tox .tox-dialog__body-content .tox-form__group h2 {\n  color: #222f3e;\n  font-size: 16px;\n  font-style: normal;\n  font-weight: bold;\n  letter-spacing: normal;\n  margin-bottom: 16px;\n  margin-top: 2rem;\n  text-transform: none;\n}\n.tox .tox-dialog__body-content .tox-form__group p {\n  margin-bottom: 16px;\n}\n.tox .tox-dialog__body-content .tox-form__group h1:first-child,\n.tox .tox-dialog__body-content .tox-form__group h2:first-child,\n.tox .tox-dialog__body-content .tox-form__group p:first-child {\n  margin-top: 0;\n}\n.tox .tox-dialog__body-content .tox-form__group h1:last-child,\n.tox .tox-dialog__body-content .tox-form__group h2:last-child,\n.tox .tox-dialog__body-content .tox-form__group p:last-child {\n  margin-bottom: 0;\n}\n.tox .tox-dialog__body-content .tox-form__group h1:only-child,\n.tox .tox-dialog__body-content .tox-form__group h2:only-child,\n.tox .tox-dialog__body-content .tox-form__group p:only-child {\n  margin-bottom: 0;\n  margin-top: 0;\n}\n.tox .tox-dialog--width-lg {\n  height: 650px;\n  max-width: 1200px;\n}\n.tox .tox-dialog--width-md {\n  max-width: 800px;\n}\n.tox .tox-dialog--width-md .tox-dialog__body-content {\n  overflow: auto;\n}\n.tox .tox-dialog__body-content--centered {\n  text-align: center;\n}\n.tox .tox-dialog__footer {\n  align-items: center;\n  background-color: #fff;\n  border-top: 1px solid #cccccc;\n  display: flex;\n  justify-content: space-between;\n  padding: 8px 16px;\n}\n.tox .tox-dialog__footer-start,\n.tox .tox-dialog__footer-end {\n  display: flex;\n}\n.tox .tox-dialog__busy-spinner {\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.75);\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 3;\n}\n.tox .tox-dialog__table {\n  border-collapse: collapse;\n  width: 100%;\n}\n.tox .tox-dialog__table thead th {\n  font-weight: bold;\n  padding-bottom: 8px;\n}\n.tox .tox-dialog__table tbody tr {\n  border-bottom: 1px solid #cccccc;\n}\n.tox .tox-dialog__table tbody tr:last-child {\n  border-bottom: none;\n}\n.tox .tox-dialog__table td {\n  padding-bottom: 8px;\n  padding-top: 8px;\n}\n.tox .tox-dialog__popups {\n  position: absolute;\n  width: 100%;\n  z-index: 1100;\n}\n.tox .tox-dialog__body-iframe {\n  display: flex;\n  flex: 1;\n  flex-direction: column;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-dialog__body-iframe .tox-navobj {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-dialog__body-iframe .tox-navobj :nth-child(2) {\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  height: 100%;\n}\n.tox .tox-dialog-dock-fadeout {\n  opacity: 0;\n  visibility: hidden;\n}\n.tox .tox-dialog-dock-fadein {\n  opacity: 1;\n  visibility: visible;\n}\n.tox .tox-dialog-dock-transition {\n  transition: visibility 0s linear 0.3s, opacity 0.3s ease;\n}\n.tox .tox-dialog-dock-transition.tox-dialog-dock-fadein {\n  transition-delay: 0s;\n}\nbody.tox-dialog__disable-scroll {\n  overflow: hidden;\n}\n.tox.tox-platform-ie {\n  /* IE11 CSS styles go here */\n}\n.tox.tox-platform-ie .tox-dialog-wrap {\n  position: -ms-device-fixed;\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox:not([dir=rtl]) .tox-dialog__body-nav {\n    margin-right: 0;\n  }\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox:not([dir=rtl]) .tox-dialog__body-nav-item:not(:first-child) {\n    margin-left: 8px;\n  }\n}\n.tox:not([dir=rtl]) .tox-dialog__footer .tox-dialog__footer-start > *,\n.tox:not([dir=rtl]) .tox-dialog__footer .tox-dialog__footer-end > * {\n  margin-left: 8px;\n}\n.tox[dir=rtl] .tox-dialog__body {\n  text-align: right;\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox[dir=rtl] .tox-dialog__body-nav {\n    margin-left: 0;\n  }\n}\n@media only screen and (max-width:767px) {\n  body:not(.tox-force-desktop) .tox[dir=rtl] .tox-dialog__body-nav-item:not(:first-child) {\n    margin-right: 8px;\n  }\n}\n.tox[dir=rtl] .tox-dialog__footer .tox-dialog__footer-start > *,\n.tox[dir=rtl] .tox-dialog__footer .tox-dialog__footer-end > * {\n  margin-right: 8px;\n}\n.tox .tox-dropzone-container {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-dropzone {\n  align-items: center;\n  background: #fff;\n  border: 2px dashed #cccccc;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  flex-grow: 1;\n  justify-content: center;\n  min-height: 100px;\n  padding: 10px;\n}\n.tox .tox-dropzone p {\n  color: rgba(34, 47, 62, 0.7);\n  margin: 0 0 16px 0;\n}\n.tox .tox-edit-area {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  overflow: hidden;\n  position: relative;\n}\n.tox .tox-edit-area__iframe {\n  background-color: #fff;\n  border: 0;\n  box-sizing: border-box;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  height: 100%;\n  position: absolute;\n  width: 100%;\n}\n.tox.tox-inline-edit-area {\n  border: 1px dotted #cccccc;\n}\n.tox .tox-editor-container {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  overflow: hidden;\n}\n.tox .tox-editor-header {\n  z-index: 1;\n}\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  box-shadow: none;\n  transition: box-shadow 0.5s;\n}\n.tox.tox-tinymce--toolbar-bottom .tox-editor-header,\n.tox.tox-tinymce-inline .tox-editor-header {\n  margin-bottom: -1px;\n}\n.tox.tox-tinymce--toolbar-sticky-on .tox-editor-header {\n  box-shadow: 0 4px 4px -3px rgba(0, 0, 0, 0.25);\n}\n.tox-editor-dock-fadeout {\n  opacity: 0;\n  visibility: hidden;\n}\n.tox-editor-dock-fadein {\n  opacity: 1;\n  visibility: visible;\n}\n.tox-editor-dock-transition {\n  transition: visibility 0s linear 0.25s, opacity 0.25s ease;\n}\n.tox-editor-dock-transition.tox-editor-dock-fadein {\n  transition-delay: 0s;\n}\n.tox .tox-control-wrap {\n  flex: 1;\n  position: relative;\n}\n.tox .tox-control-wrap:not(.tox-control-wrap--status-invalid) .tox-control-wrap__status-icon-invalid,\n.tox .tox-control-wrap:not(.tox-control-wrap--status-unknown) .tox-control-wrap__status-icon-unknown,\n.tox .tox-control-wrap:not(.tox-control-wrap--status-valid) .tox-control-wrap__status-icon-valid {\n  display: none;\n}\n.tox .tox-control-wrap svg {\n  display: block;\n}\n.tox .tox-control-wrap__status-icon-wrap {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.tox .tox-control-wrap__status-icon-invalid svg {\n  fill: #c00;\n}\n.tox .tox-control-wrap__status-icon-unknown svg {\n  fill: orange;\n}\n.tox .tox-control-wrap__status-icon-valid svg {\n  fill: green;\n}\n.tox:not([dir=rtl]) .tox-control-wrap--status-invalid .tox-textfield,\n.tox:not([dir=rtl]) .tox-control-wrap--status-unknown .tox-textfield,\n.tox:not([dir=rtl]) .tox-control-wrap--status-valid .tox-textfield {\n  padding-right: 32px;\n}\n.tox:not([dir=rtl]) .tox-control-wrap__status-icon-wrap {\n  right: 4px;\n}\n.tox[dir=rtl] .tox-control-wrap--status-invalid .tox-textfield,\n.tox[dir=rtl] .tox-control-wrap--status-unknown .tox-textfield,\n.tox[dir=rtl] .tox-control-wrap--status-valid .tox-textfield {\n  padding-left: 32px;\n}\n.tox[dir=rtl] .tox-control-wrap__status-icon-wrap {\n  left: 4px;\n}\n.tox .tox-autocompleter {\n  max-width: 25em;\n}\n.tox .tox-autocompleter .tox-menu {\n  max-width: 25em;\n}\n.tox .tox-autocompleter .tox-autocompleter-highlight {\n  font-weight: bold;\n}\n.tox .tox-color-input {\n  display: flex;\n  position: relative;\n  z-index: 1;\n}\n.tox .tox-color-input .tox-textfield {\n  z-index: -1;\n}\n.tox .tox-color-input span {\n  border-color: rgba(34, 47, 62, 0.2);\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  box-sizing: border-box;\n  height: 24px;\n  position: absolute;\n  top: 6px;\n  width: 24px;\n}\n.tox .tox-color-input span:hover:not([aria-disabled=true]),\n.tox .tox-color-input span:focus:not([aria-disabled=true]) {\n  border-color: #207ab7;\n  cursor: pointer;\n}\n.tox .tox-color-input span::before {\n  background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.25) 25%, transparent 25%), linear-gradient(-45deg, rgba(0, 0, 0, 0.25) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, rgba(0, 0, 0, 0.25) 75%), linear-gradient(-45deg, transparent 75%, rgba(0, 0, 0, 0.25) 75%);\n  background-position: 0 0, 0 6px, 6px -6px, -6px 0;\n  background-size: 12px 12px;\n  border: 1px solid #fff;\n  border-radius: 3px;\n  box-sizing: border-box;\n  content: '';\n  height: 24px;\n  left: -1px;\n  position: absolute;\n  top: -1px;\n  width: 24px;\n  z-index: -1;\n}\n.tox .tox-color-input span[aria-disabled=true] {\n  cursor: not-allowed;\n}\n.tox:not([dir=rtl]) .tox-color-input {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox:not([dir=rtl]) .tox-color-input .tox-textfield {\n  padding-left: 36px;\n}\n.tox:not([dir=rtl]) .tox-color-input span {\n  left: 6px;\n}\n.tox[dir=\"rtl\"] .tox-color-input {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox[dir=\"rtl\"] .tox-color-input .tox-textfield {\n  padding-right: 36px;\n}\n.tox[dir=\"rtl\"] .tox-color-input span {\n  right: 6px;\n}\n.tox .tox-label,\n.tox .tox-toolbar-label {\n  color: rgba(34, 47, 62, 0.7);\n  display: block;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.3;\n  padding: 0 8px 0 0;\n  text-transform: none;\n  white-space: nowrap;\n}\n.tox .tox-toolbar-label {\n  padding: 0 8px;\n}\n.tox[dir=rtl] .tox-label {\n  padding: 0 0 0 8px;\n}\n.tox .tox-form {\n  display: flex;\n  flex: 1;\n  flex-direction: column;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-form__group {\n  box-sizing: border-box;\n  margin-bottom: 4px;\n}\n.tox .tox-form-group--maximize {\n  flex: 1;\n}\n.tox .tox-form__group--error {\n  color: #c00;\n}\n.tox .tox-form__group--collection {\n  display: flex;\n}\n.tox .tox-form__grid {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n.tox .tox-form__grid--2col > .tox-form__group {\n  width: calc(50% - (8px / 2));\n}\n.tox .tox-form__grid--3col > .tox-form__group {\n  width: calc(100% / 3 - (8px / 2));\n}\n.tox .tox-form__grid--4col > .tox-form__group {\n  width: calc(25% - (8px / 2));\n}\n.tox .tox-form__controls-h-stack {\n  align-items: center;\n  display: flex;\n}\n.tox .tox-form__group--inline {\n  align-items: center;\n  display: flex;\n}\n.tox .tox-form__group--stretched {\n  display: flex;\n  flex: 1;\n  flex-direction: column;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-form__group--stretched .tox-textarea {\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-form__group--stretched .tox-navobj {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-form__group--stretched .tox-navobj :nth-child(2) {\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  height: 100%;\n}\n.tox:not([dir=rtl]) .tox-form__controls-h-stack > *:not(:first-child) {\n  margin-left: 4px;\n}\n.tox[dir=rtl] .tox-form__controls-h-stack > *:not(:first-child) {\n  margin-right: 4px;\n}\n.tox .tox-lock.tox-locked .tox-lock-icon__unlock,\n.tox .tox-lock:not(.tox-locked) .tox-lock-icon__lock {\n  display: none;\n}\n.tox .tox-textfield,\n.tox .tox-toolbar-textfield,\n.tox .tox-listboxfield .tox-listbox--select,\n.tox .tox-textarea {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  background-color: #fff;\n  border-color: #cccccc;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  box-sizing: border-box;\n  color: #222f3e;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 16px;\n  line-height: 24px;\n  margin: 0;\n  min-height: 34px;\n  outline: none;\n  padding: 5px 4.75px;\n  resize: none;\n  width: 100%;\n}\n.tox .tox-textfield[disabled],\n.tox .tox-textarea[disabled] {\n  background-color: #f2f2f2;\n  color: rgba(34, 47, 62, 0.85);\n  cursor: not-allowed;\n}\n.tox .tox-textfield:focus,\n.tox .tox-listboxfield .tox-listbox--select:focus,\n.tox .tox-textarea:focus {\n  background-color: #fff;\n  border-color: #207ab7;\n  box-shadow: none;\n  outline: none;\n}\n.tox .tox-toolbar-textfield {\n  border-width: 0;\n  margin-bottom: 3px;\n  margin-top: 2px;\n  max-width: 250px;\n}\n.tox .tox-naked-btn {\n  background-color: transparent;\n  border: 0;\n  border-color: transparent;\n  box-shadow: unset;\n  color: #207ab7;\n  cursor: pointer;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n.tox .tox-naked-btn svg {\n  display: block;\n  fill: #222f3e;\n}\n.tox:not([dir=rtl]) .tox-toolbar-textfield + * {\n  margin-left: 4px;\n}\n.tox[dir=rtl] .tox-toolbar-textfield + * {\n  margin-right: 4px;\n}\n.tox .tox-listboxfield {\n  cursor: pointer;\n  position: relative;\n}\n.tox .tox-listboxfield .tox-listbox--select[disabled] {\n  background-color: #f2f2f2;\n  color: rgba(34, 47, 62, 0.85);\n  cursor: not-allowed;\n}\n.tox .tox-listbox__select-label {\n  cursor: default;\n  flex: 1;\n  margin: 0 4px;\n}\n.tox .tox-listbox__select-chevron {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  width: 16px;\n}\n.tox .tox-listbox__select-chevron svg {\n  fill: #222f3e;\n}\n.tox .tox-listboxfield .tox-listbox--select {\n  align-items: center;\n  display: flex;\n}\n.tox:not([dir=rtl]) .tox-listboxfield svg {\n  right: 8px;\n}\n.tox[dir=rtl] .tox-listboxfield svg {\n  left: 8px;\n}\n.tox .tox-selectfield {\n  cursor: pointer;\n  position: relative;\n}\n.tox .tox-selectfield select {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  background-color: #fff;\n  border-color: #cccccc;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  box-sizing: border-box;\n  color: #222f3e;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n  font-size: 16px;\n  line-height: 24px;\n  margin: 0;\n  min-height: 34px;\n  outline: none;\n  padding: 5px 4.75px;\n  resize: none;\n  width: 100%;\n}\n.tox .tox-selectfield select[disabled] {\n  background-color: #f2f2f2;\n  color: rgba(34, 47, 62, 0.85);\n  cursor: not-allowed;\n}\n.tox .tox-selectfield select::-ms-expand {\n  display: none;\n}\n.tox .tox-selectfield select:focus {\n  background-color: #fff;\n  border-color: #207ab7;\n  box-shadow: none;\n  outline: none;\n}\n.tox .tox-selectfield svg {\n  pointer-events: none;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.tox:not([dir=rtl]) .tox-selectfield select[size=\"0\"],\n.tox:not([dir=rtl]) .tox-selectfield select[size=\"1\"] {\n  padding-right: 24px;\n}\n.tox:not([dir=rtl]) .tox-selectfield svg {\n  right: 8px;\n}\n.tox[dir=rtl] .tox-selectfield select[size=\"0\"],\n.tox[dir=rtl] .tox-selectfield select[size=\"1\"] {\n  padding-left: 24px;\n}\n.tox[dir=rtl] .tox-selectfield svg {\n  left: 8px;\n}\n.tox .tox-textarea {\n  -webkit-appearance: textarea;\n     -moz-appearance: textarea;\n          appearance: textarea;\n  white-space: pre-wrap;\n}\n.tox-fullscreen {\n  border: 0;\n  height: 100%;\n  left: 0;\n  margin: 0;\n  overflow: hidden;\n  -ms-scroll-chaining: none;\n      overscroll-behavior: none;\n  padding: 0;\n  position: fixed;\n  top: 0;\n  touch-action: pinch-zoom;\n  width: 100%;\n}\n.tox-fullscreen .tox.tox-tinymce.tox-fullscreen .tox-statusbar__resize-handle {\n  display: none;\n}\n.tox-fullscreen .tox.tox-tinymce.tox-fullscreen {\n  z-index: 1200;\n}\n.tox-fullscreen .tox.tox-tinymce-aux {\n  z-index: 1201;\n}\n.tox .tox-help__more-link {\n  list-style: none;\n  margin-top: 1em;\n}\n.tox .tox-image-tools {\n  width: 100%;\n}\n.tox .tox-image-tools__toolbar {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n.tox .tox-image-tools__image {\n  background-color: #666;\n  height: 380px;\n  overflow: auto;\n  position: relative;\n  width: 100%;\n}\n.tox .tox-image-tools__image,\n.tox .tox-image-tools__image + .tox-image-tools__toolbar {\n  margin-top: 8px;\n}\n.tox .tox-image-tools__image-bg {\n  background: url(data:image/gif;base64,R0lGODdhDAAMAIABAMzMzP///ywAAAAADAAMAAACFoQfqYeabNyDMkBQb81Uat85nxguUAEAOw==);\n}\n.tox .tox-image-tools__toolbar > .tox-spacer {\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-croprect-block {\n  background: black;\n  filter: alpha(opacity=50);\n  opacity: 0.5;\n  position: absolute;\n  zoom: 1;\n}\n.tox .tox-croprect-handle {\n  border: 2px solid white;\n  height: 20px;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 20px;\n}\n.tox .tox-croprect-handle-move {\n  border: 0;\n  cursor: move;\n  position: absolute;\n}\n.tox .tox-croprect-handle-nw {\n  border-width: 2px 0 0 2px;\n  cursor: nw-resize;\n  left: 100px;\n  margin: -2px 0 0 -2px;\n  top: 100px;\n}\n.tox .tox-croprect-handle-ne {\n  border-width: 2px 2px 0 0;\n  cursor: ne-resize;\n  left: 200px;\n  margin: -2px 0 0 -20px;\n  top: 100px;\n}\n.tox .tox-croprect-handle-sw {\n  border-width: 0 0 2px 2px;\n  cursor: sw-resize;\n  left: 100px;\n  margin: -20px 2px 0 -2px;\n  top: 200px;\n}\n.tox .tox-croprect-handle-se {\n  border-width: 0 2px 2px 0;\n  cursor: se-resize;\n  left: 200px;\n  margin: -20px 0 0 -20px;\n  top: 200px;\n}\n.tox:not([dir=rtl]) .tox-image-tools__toolbar > .tox-slider:not(:first-of-type) {\n  margin-left: 8px;\n}\n.tox:not([dir=rtl]) .tox-image-tools__toolbar > .tox-button + .tox-slider {\n  margin-left: 32px;\n}\n.tox:not([dir=rtl]) .tox-image-tools__toolbar > .tox-slider + .tox-button {\n  margin-left: 32px;\n}\n.tox[dir=rtl] .tox-image-tools__toolbar > .tox-slider:not(:first-of-type) {\n  margin-right: 8px;\n}\n.tox[dir=rtl] .tox-image-tools__toolbar > .tox-button + .tox-slider {\n  margin-right: 32px;\n}\n.tox[dir=rtl] .tox-image-tools__toolbar > .tox-slider + .tox-button {\n  margin-right: 32px;\n}\n.tox .tox-insert-table-picker {\n  display: flex;\n  flex-wrap: wrap;\n  width: 170px;\n}\n.tox .tox-insert-table-picker > div {\n  border-color: #cccccc;\n  border-style: solid;\n  border-width: 0 1px 1px 0;\n  box-sizing: border-box;\n  height: 17px;\n  width: 17px;\n}\n.tox .tox-collection--list .tox-collection__group .tox-insert-table-picker {\n  margin: -4px 0;\n}\n.tox .tox-insert-table-picker .tox-insert-table-picker__selected {\n  background-color: rgba(32, 122, 183, 0.5);\n  border-color: rgba(32, 122, 183, 0.5);\n}\n.tox .tox-insert-table-picker__label {\n  color: rgba(34, 47, 62, 0.7);\n  display: block;\n  font-size: 14px;\n  padding: 4px;\n  text-align: center;\n  width: 100%;\n}\n.tox:not([dir=rtl]) {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox:not([dir=rtl]) .tox-insert-table-picker > div:nth-child(10n) {\n  border-right: 0;\n}\n.tox[dir=rtl] {\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.tox[dir=rtl] .tox-insert-table-picker > div:nth-child(10n+1) {\n  border-right: 0;\n}\n.tox {\n  /* stylelint-disable */\n  /* stylelint-enable */\n}\n.tox .tox-menu {\n  background-color: #fff;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  box-shadow: 0 4px 8px 0 rgba(34, 47, 62, 0.1);\n  display: inline-block;\n  overflow: hidden;\n  vertical-align: top;\n  z-index: 1150;\n}\n.tox .tox-menu.tox-collection.tox-collection--list {\n  padding: 0;\n}\n.tox .tox-menu.tox-collection.tox-collection--toolbar {\n  padding: 4px;\n}\n.tox .tox-menu.tox-collection.tox-collection--grid {\n  padding: 4px;\n}\n.tox .tox-menu__label h1,\n.tox .tox-menu__label h2,\n.tox .tox-menu__label h3,\n.tox .tox-menu__label h4,\n.tox .tox-menu__label h5,\n.tox .tox-menu__label h6,\n.tox .tox-menu__label p,\n.tox .tox-menu__label blockquote,\n.tox .tox-menu__label code {\n  margin: 0;\n}\n.tox .tox-menubar {\n  background: url(\"data:image/svg+xml;charset=utf8,%3Csvg height='39px' viewBox='0 0 40 39px' width='40' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0' y='38px' width='100' height='1' fill='%23cccccc'/%3E%3C/svg%3E\") left 0 top 0 #fff;\n  background-color: #fff;\n  display: flex;\n  flex: 0 0 auto;\n  flex-shrink: 0;\n  flex-wrap: wrap;\n  padding: 0 4px 0 4px;\n}\n.tox.tox-tinymce:not(.tox-tinymce-inline) .tox-editor-header:not(:first-child) .tox-menubar {\n  border-top: 1px solid #cccccc;\n}\n/* Deprecated. Remove in next major release */\n.tox .tox-mbtn {\n  align-items: center;\n  background: transparent;\n  border: 0;\n  border-radius: 3px;\n  box-shadow: none;\n  color: #222f3e;\n  display: flex;\n  flex: 0 0 auto;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  height: 34px;\n  justify-content: center;\n  margin: 2px 0 3px 0;\n  outline: none;\n  overflow: hidden;\n  padding: 0 4px;\n  text-transform: none;\n  width: auto;\n}\n.tox .tox-mbtn[disabled] {\n  background-color: transparent;\n  border: 0;\n  box-shadow: none;\n  color: rgba(34, 47, 62, 0.5);\n  cursor: not-allowed;\n}\n.tox .tox-mbtn:focus:not(:disabled) {\n  background: #dee0e2;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-mbtn--active {\n  background: #c8cbcf;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n  background: #dee0e2;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-mbtn__select-label {\n  cursor: default;\n  font-weight: normal;\n  margin: 0 4px;\n}\n.tox .tox-mbtn[disabled] .tox-mbtn__select-label {\n  cursor: not-allowed;\n}\n.tox .tox-mbtn__select-chevron {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  width: 16px;\n  display: none;\n}\n.tox .tox-notification {\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 1px;\n  box-shadow: none;\n  box-sizing: border-box;\n  display: -ms-grid;\n  display: grid;\n  font-size: 14px;\n  font-weight: normal;\n  -ms-grid-columns: minmax(40px, 1fr) auto minmax(40px, 1fr);\n      grid-template-columns: minmax(40px, 1fr) auto minmax(40px, 1fr);\n  margin-top: 4px;\n  opacity: 0;\n  padding: 4px;\n  transition: transform 100ms ease-in, opacity 150ms ease-in;\n}\n.tox .tox-notification p {\n  font-size: 14px;\n  font-weight: normal;\n}\n.tox .tox-notification a {\n  text-decoration: underline;\n}\n.tox .tox-notification--in {\n  opacity: 1;\n}\n.tox .tox-notification--success {\n  background-color: #e4eeda;\n  border-color: #d7e6c8;\n  color: #222f3e;\n}\n.tox .tox-notification--success p {\n  color: #222f3e;\n}\n.tox .tox-notification--success a {\n  color: #547831;\n}\n.tox .tox-notification--success svg {\n  fill: #222f3e;\n}\n.tox .tox-notification--error {\n  background-color: #f8dede;\n  border-color: #f2bfbf;\n  color: #222f3e;\n}\n.tox .tox-notification--error p {\n  color: #222f3e;\n}\n.tox .tox-notification--error a {\n  color: #c00;\n}\n.tox .tox-notification--error svg {\n  fill: #222f3e;\n}\n.tox .tox-notification--warn,\n.tox .tox-notification--warning {\n  background-color: #fffaea;\n  border-color: #ffe89d;\n  color: #222f3e;\n}\n.tox .tox-notification--warn p,\n.tox .tox-notification--warning p {\n  color: #222f3e;\n}\n.tox .tox-notification--warn a,\n.tox .tox-notification--warning a {\n  color: #222f3e;\n}\n.tox .tox-notification--warn svg,\n.tox .tox-notification--warning svg {\n  fill: #222f3e;\n}\n.tox .tox-notification--info {\n  background-color: #d9edf7;\n  border-color: #779ecb;\n  color: #222f3e;\n}\n.tox .tox-notification--info p {\n  color: #222f3e;\n}\n.tox .tox-notification--info a {\n  color: #222f3e;\n}\n.tox .tox-notification--info svg {\n  fill: #222f3e;\n}\n.tox .tox-notification__body {\n  -ms-grid-row-align: center;\n      align-self: center;\n  color: #222f3e;\n  font-size: 14px;\n  -ms-grid-column-span: 1;\n  grid-column-end: 3;\n  -ms-grid-column: 2;\n      grid-column-start: 2;\n  -ms-grid-row-span: 1;\n  grid-row-end: 2;\n  -ms-grid-row: 1;\n      grid-row-start: 1;\n  text-align: center;\n  white-space: normal;\n  word-break: break-all;\n  word-break: break-word;\n}\n.tox .tox-notification__body > * {\n  margin: 0;\n}\n.tox .tox-notification__body > * + * {\n  margin-top: 1rem;\n}\n.tox .tox-notification__icon {\n  -ms-grid-row-align: center;\n      align-self: center;\n  -ms-grid-column-span: 1;\n  grid-column-end: 2;\n  -ms-grid-column: 1;\n      grid-column-start: 1;\n  -ms-grid-row-span: 1;\n  grid-row-end: 2;\n  -ms-grid-row: 1;\n      grid-row-start: 1;\n  -ms-grid-column-align: end;\n      justify-self: end;\n}\n.tox .tox-notification__icon svg {\n  display: block;\n}\n.tox .tox-notification__dismiss {\n  -ms-grid-row-align: start;\n      align-self: start;\n  -ms-grid-column-span: 1;\n  grid-column-end: 4;\n  -ms-grid-column: 3;\n      grid-column-start: 3;\n  -ms-grid-row-span: 1;\n  grid-row-end: 2;\n  -ms-grid-row: 1;\n      grid-row-start: 1;\n  -ms-grid-column-align: end;\n      justify-self: end;\n}\n.tox .tox-notification .tox-progress-bar {\n  -ms-grid-column-span: 3;\n  grid-column-end: 4;\n  -ms-grid-column: 1;\n      grid-column-start: 1;\n  -ms-grid-row-span: 1;\n  grid-row-end: 3;\n  -ms-grid-row: 2;\n      grid-row-start: 2;\n  -ms-grid-column-align: center;\n      justify-self: center;\n}\n.tox .tox-pop {\n  display: inline-block;\n  position: relative;\n}\n.tox .tox-pop--resizing {\n  transition: width 0.1s ease;\n}\n.tox .tox-pop--resizing .tox-toolbar {\n  flex-wrap: nowrap;\n}\n.tox .tox-pop__dialog {\n  background-color: #fff;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);\n  min-width: 0;\n  overflow: hidden;\n}\n.tox .tox-pop__dialog > *:not(.tox-toolbar) {\n  margin: 4px 4px 4px 8px;\n}\n.tox .tox-pop__dialog .tox-toolbar {\n  background-color: transparent;\n  margin-bottom: -1px;\n}\n.tox .tox-pop::before,\n.tox .tox-pop::after {\n  border-style: solid;\n  content: '';\n  display: block;\n  height: 0;\n  position: absolute;\n  width: 0;\n}\n.tox .tox-pop.tox-pop--bottom::before,\n.tox .tox-pop.tox-pop--bottom::after {\n  left: 50%;\n  top: 100%;\n}\n.tox .tox-pop.tox-pop--bottom::after {\n  border-color: #fff transparent transparent transparent;\n  border-width: 8px;\n  margin-left: -8px;\n  margin-top: -1px;\n}\n.tox .tox-pop.tox-pop--bottom::before {\n  border-color: #cccccc transparent transparent transparent;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.tox .tox-pop.tox-pop--top::before,\n.tox .tox-pop.tox-pop--top::after {\n  left: 50%;\n  top: 0;\n  transform: translateY(-100%);\n}\n.tox .tox-pop.tox-pop--top::after {\n  border-color: transparent transparent #fff transparent;\n  border-width: 8px;\n  margin-left: -8px;\n  margin-top: 1px;\n}\n.tox .tox-pop.tox-pop--top::before {\n  border-color: transparent transparent #cccccc transparent;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.tox .tox-pop.tox-pop--left::before,\n.tox .tox-pop.tox-pop--left::after {\n  left: 0;\n  top: calc(50% - 1px);\n  transform: translateY(-50%);\n}\n.tox .tox-pop.tox-pop--left::after {\n  border-color: transparent #fff transparent transparent;\n  border-width: 8px;\n  margin-left: -15px;\n}\n.tox .tox-pop.tox-pop--left::before {\n  border-color: transparent #cccccc transparent transparent;\n  border-width: 10px;\n  margin-left: -19px;\n}\n.tox .tox-pop.tox-pop--right::before,\n.tox .tox-pop.tox-pop--right::after {\n  left: 100%;\n  top: calc(50% + 1px);\n  transform: translateY(-50%);\n}\n.tox .tox-pop.tox-pop--right::after {\n  border-color: transparent transparent transparent #fff;\n  border-width: 8px;\n  margin-left: -1px;\n}\n.tox .tox-pop.tox-pop--right::before {\n  border-color: transparent transparent transparent #cccccc;\n  border-width: 10px;\n  margin-left: -1px;\n}\n.tox .tox-pop.tox-pop--align-left::before,\n.tox .tox-pop.tox-pop--align-left::after {\n  left: 20px;\n}\n.tox .tox-pop.tox-pop--align-right::before,\n.tox .tox-pop.tox-pop--align-right::after {\n  left: calc(100% - 20px);\n}\n.tox .tox-sidebar-wrap {\n  display: flex;\n  flex-direction: row;\n  flex-grow: 1;\n  -ms-flex-preferred-size: 0;\n  min-height: 0;\n}\n.tox .tox-sidebar {\n  background-color: #fff;\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-end;\n}\n.tox .tox-sidebar__slider {\n  display: flex;\n  overflow: hidden;\n}\n.tox .tox-sidebar__pane-container {\n  display: flex;\n}\n.tox .tox-sidebar__pane {\n  display: flex;\n}\n.tox .tox-sidebar--sliding-closed {\n  opacity: 0;\n}\n.tox .tox-sidebar--sliding-open {\n  opacity: 1;\n}\n.tox .tox-sidebar--sliding-growing,\n.tox .tox-sidebar--sliding-shrinking {\n  transition: width 0.5s ease, opacity 0.5s ease;\n}\n.tox .tox-selector {\n  background-color: #4099ff;\n  border-color: #4099ff;\n  border-style: solid;\n  border-width: 1px;\n  box-sizing: border-box;\n  display: inline-block;\n  height: 10px;\n  position: absolute;\n  width: 10px;\n}\n.tox.tox-platform-touch .tox-selector {\n  height: 12px;\n  width: 12px;\n}\n.tox .tox-slider {\n  align-items: center;\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n  height: 24px;\n  justify-content: center;\n  position: relative;\n}\n.tox .tox-slider__rail {\n  background-color: transparent;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  height: 10px;\n  min-width: 120px;\n  width: 100%;\n}\n.tox .tox-slider__handle {\n  background-color: #207ab7;\n  border: 2px solid #185d8c;\n  border-radius: 3px;\n  box-shadow: none;\n  height: 24px;\n  left: 50%;\n  position: absolute;\n  top: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  width: 14px;\n}\n.tox .tox-source-code {\n  overflow: auto;\n}\n.tox .tox-spinner {\n  display: flex;\n}\n.tox .tox-spinner > div {\n  animation: tam-bouncing-dots 1.5s ease-in-out 0s infinite both;\n  background-color: rgba(34, 47, 62, 0.7);\n  border-radius: 100%;\n  height: 8px;\n  width: 8px;\n}\n.tox .tox-spinner > div:nth-child(1) {\n  animation-delay: -0.32s;\n}\n.tox .tox-spinner > div:nth-child(2) {\n  animation-delay: -0.16s;\n}\n@keyframes tam-bouncing-dots {\n  0%,\n  80%,\n  100% {\n    transform: scale(0);\n  }\n  40% {\n    transform: scale(1);\n  }\n}\n.tox:not([dir=rtl]) .tox-spinner > div:not(:first-child) {\n  margin-left: 4px;\n}\n.tox[dir=rtl] .tox-spinner > div:not(:first-child) {\n  margin-right: 4px;\n}\n.tox .tox-statusbar {\n  align-items: center;\n  background-color: #fff;\n  border-top: 1px solid #cccccc;\n  color: rgba(34, 47, 62, 0.7);\n  display: flex;\n  flex: 0 0 auto;\n  font-size: 12px;\n  font-weight: normal;\n  height: 18px;\n  overflow: hidden;\n  padding: 0 8px;\n  position: relative;\n  text-transform: uppercase;\n}\n.tox .tox-statusbar__text-container {\n  display: flex;\n  flex: 1 1 auto;\n  justify-content: flex-end;\n  overflow: hidden;\n}\n.tox .tox-statusbar__path {\n  display: flex;\n  flex: 1 1 auto;\n  margin-right: auto;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.tox .tox-statusbar__path > * {\n  display: inline;\n  white-space: nowrap;\n}\n.tox .tox-statusbar__wordcount {\n  flex: 0 0 auto;\n  margin-left: 1ch;\n}\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: rgba(34, 47, 62, 0.7);\n  text-decoration: none;\n}\n.tox .tox-statusbar a:hover:not(:disabled):not([aria-disabled=true]),\n.tox .tox-statusbar__path-item:hover:not(:disabled):not([aria-disabled=true]),\n.tox .tox-statusbar__wordcount:hover:not(:disabled):not([aria-disabled=true]),\n.tox .tox-statusbar a:focus:not(:disabled):not([aria-disabled=true]),\n.tox .tox-statusbar__path-item:focus:not(:disabled):not([aria-disabled=true]),\n.tox .tox-statusbar__wordcount:focus:not(:disabled):not([aria-disabled=true]) {\n  cursor: pointer;\n  text-decoration: underline;\n}\n.tox .tox-statusbar__resize-handle {\n  align-items: flex-end;\n  align-self: stretch;\n  cursor: nwse-resize;\n  display: flex;\n  flex: 0 0 auto;\n  justify-content: flex-end;\n  margin-left: auto;\n  margin-right: -8px;\n  padding-left: 1ch;\n}\n.tox .tox-statusbar__resize-handle svg {\n  display: block;\n  fill: rgba(34, 47, 62, 0.7);\n}\n.tox:not([dir=rtl]) .tox-statusbar__path > * {\n  margin-right: 4px;\n}\n.tox:not([dir=rtl]) .tox-statusbar__branding {\n  margin-left: 1ch;\n}\n.tox[dir=rtl] .tox-statusbar {\n  flex-direction: row-reverse;\n}\n.tox[dir=rtl] .tox-statusbar__path > * {\n  margin-left: 4px;\n}\n.tox .tox-throbber {\n  z-index: 1400;\n}\n.tox .tox-throbber__busy-spinner {\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.6);\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n.tox .tox-tbtn {\n  align-items: center;\n  background: transparent;\n  border: 0;\n  border-radius: 3px;\n  box-shadow: none;\n  color: #222f3e;\n  display: flex;\n  flex: 0 0 auto;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  height: 34px;\n  justify-content: center;\n  margin: 2px 0 3px 0;\n  outline: none;\n  overflow: hidden;\n  padding: 0;\n  text-transform: none;\n  width: 34px;\n}\n.tox .tox-tbtn svg {\n  display: block;\n  fill: #222f3e;\n}\n.tox .tox-tbtn.tox-tbtn-more {\n  padding-left: 5px;\n  padding-right: 5px;\n  width: inherit;\n}\n.tox .tox-tbtn:focus {\n  background: #dee0e2;\n  border: 0;\n  box-shadow: none;\n}\n.tox .tox-tbtn:hover {\n  background: #dee0e2;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-tbtn:hover svg {\n  fill: #222f3e;\n}\n.tox .tox-tbtn:active {\n  background: #c8cbcf;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-tbtn:active svg {\n  fill: #222f3e;\n}\n.tox .tox-tbtn--disabled,\n.tox .tox-tbtn--disabled:hover,\n.tox .tox-tbtn:disabled,\n.tox .tox-tbtn:disabled:hover {\n  background: transparent;\n  border: 0;\n  box-shadow: none;\n  color: rgba(34, 47, 62, 0.5);\n  cursor: not-allowed;\n}\n.tox .tox-tbtn--disabled svg,\n.tox .tox-tbtn--disabled:hover svg,\n.tox .tox-tbtn:disabled svg,\n.tox .tox-tbtn:disabled:hover svg {\n  /* stylelint-disable-line no-descending-specificity */\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-tbtn--enabled,\n.tox .tox-tbtn--enabled:hover {\n  background: #c8cbcf;\n  border: 0;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-tbtn--enabled > *,\n.tox .tox-tbtn--enabled:hover > * {\n  transform: none;\n}\n.tox .tox-tbtn--enabled svg,\n.tox .tox-tbtn--enabled:hover svg {\n  /* stylelint-disable-line no-descending-specificity */\n  fill: #222f3e;\n}\n.tox .tox-tbtn:focus:not(.tox-tbtn--disabled) {\n  color: #222f3e;\n}\n.tox .tox-tbtn:focus:not(.tox-tbtn--disabled) svg {\n  fill: #222f3e;\n}\n.tox .tox-tbtn:active > * {\n  transform: none;\n}\n.tox .tox-tbtn--md {\n  height: 51px;\n  width: 51px;\n}\n.tox .tox-tbtn--lg {\n  flex-direction: column;\n  height: 68px;\n  width: 68px;\n}\n.tox .tox-tbtn--return {\n  -ms-grid-row-align: stretch;\n      align-self: stretch;\n  height: unset;\n  width: 16px;\n}\n.tox .tox-tbtn--labeled {\n  padding: 0 4px;\n  width: unset;\n}\n.tox .tox-tbtn__vlabel {\n  display: block;\n  font-size: 10px;\n  font-weight: normal;\n  letter-spacing: -0.025em;\n  margin-bottom: 4px;\n  white-space: nowrap;\n}\n.tox .tox-tbtn--select {\n  margin: 2px 0 3px 0;\n  padding: 0 4px;\n  width: auto;\n}\n.tox .tox-tbtn__select-label {\n  cursor: default;\n  font-weight: normal;\n  margin: 0 4px;\n}\n.tox .tox-tbtn__select-chevron {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  width: 16px;\n}\n.tox .tox-tbtn__select-chevron svg {\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-tbtn--bespoke .tox-tbtn__select-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  width: 7em;\n}\n.tox .tox-split-button {\n  border: 0;\n  border-radius: 3px;\n  box-sizing: border-box;\n  display: flex;\n  margin: 2px 0 3px 0;\n  overflow: hidden;\n}\n.tox .tox-split-button:hover {\n  box-shadow: 0 0 0 1px #dee0e2 inset;\n}\n.tox .tox-split-button:focus {\n  background: #dee0e2;\n  box-shadow: none;\n  color: #222f3e;\n}\n.tox .tox-split-button > * {\n  border-radius: 0;\n}\n.tox .tox-split-button__chevron {\n  width: 16px;\n}\n.tox .tox-split-button__chevron svg {\n  fill: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-split-button .tox-tbtn {\n  margin: 0;\n}\n.tox.tox-platform-touch .tox-split-button .tox-tbtn:first-child {\n  width: 30px;\n}\n.tox.tox-platform-touch .tox-split-button__chevron {\n  width: 20px;\n}\n.tox .tox-split-button.tox-tbtn--disabled:hover,\n.tox .tox-split-button.tox-tbtn--disabled:focus,\n.tox .tox-split-button.tox-tbtn--disabled .tox-tbtn:hover,\n.tox .tox-split-button.tox-tbtn--disabled .tox-tbtn:focus {\n  background: transparent;\n  box-shadow: none;\n  color: rgba(34, 47, 62, 0.5);\n}\n.tox .tox-toolbar-overlord {\n  background-color: #fff;\n}\n.tox .tox-toolbar,\n.tox .tox-toolbar__primary,\n.tox .tox-toolbar__overflow {\n  background: url(\"data:image/svg+xml;charset=utf8,%3Csvg height='39px' viewBox='0 0 40 39px' width='40' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0' y='38px' width='100' height='1' fill='%23cccccc'/%3E%3C/svg%3E\") left 0 top 0 #fff;\n  background-color: #fff;\n  display: flex;\n  flex: 0 0 auto;\n  flex-shrink: 0;\n  flex-wrap: wrap;\n  padding: 0 0;\n}\n.tox .tox-toolbar__overflow.tox-toolbar__overflow--closed {\n  height: 0;\n  opacity: 0;\n  padding-bottom: 0;\n  padding-top: 0;\n  visibility: hidden;\n}\n.tox .tox-toolbar__overflow--growing {\n  transition: height 0.3s ease, opacity 0.2s linear 0.1s;\n}\n.tox .tox-toolbar__overflow--shrinking {\n  transition: opacity 0.3s ease, height 0.2s linear 0.1s, visibility 0s linear 0.3s;\n}\n.tox .tox-menubar + .tox-toolbar,\n.tox .tox-menubar + .tox-toolbar-overlord .tox-toolbar__primary {\n  border-top: 1px solid #cccccc;\n  margin-top: -1px;\n}\n.tox .tox-toolbar--scrolling {\n  flex-wrap: nowrap;\n  overflow-x: auto;\n}\n.tox .tox-pop .tox-toolbar {\n  border-width: 0;\n}\n.tox .tox-toolbar--no-divider {\n  background-image: none;\n}\n.tox-tinymce:not(.tox-tinymce-inline) .tox-editor-header:not(:first-child) .tox-toolbar:first-child,\n.tox-tinymce:not(.tox-tinymce-inline) .tox-editor-header:not(:first-child) .tox-toolbar-overlord:first-child .tox-toolbar__primary {\n  border-top: 1px solid #cccccc;\n}\n.tox.tox-tinymce-aux .tox-toolbar__overflow {\n  background-color: #fff;\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);\n}\n.tox[dir=rtl] .tox-tbtn__icon-rtl svg {\n  transform: rotateY(180deg);\n}\n.tox .tox-toolbar__group {\n  align-items: center;\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 0;\n  padding: 0 4px 0 4px;\n}\n.tox .tox-toolbar__group--pull-right {\n  margin-left: auto;\n}\n.tox .tox-toolbar--scrolling .tox-toolbar__group {\n  flex-shrink: 0;\n  flex-wrap: nowrap;\n}\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #cccccc;\n}\n.tox[dir=rtl] .tox-toolbar__group:not(:last-of-type) {\n  border-left: 1px solid #cccccc;\n}\n.tox .tox-tooltip {\n  display: inline-block;\n  padding: 8px;\n  position: relative;\n}\n.tox .tox-tooltip__body {\n  background-color: #222f3e;\n  border-radius: 3px;\n  box-shadow: 0 2px 4px rgba(34, 47, 62, 0.3);\n  color: rgba(255, 255, 255, 0.75);\n  font-size: 14px;\n  font-style: normal;\n  font-weight: normal;\n  padding: 4px 8px;\n  text-transform: none;\n}\n.tox .tox-tooltip__arrow {\n  position: absolute;\n}\n.tox .tox-tooltip--down .tox-tooltip__arrow {\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-top: 8px solid #222f3e;\n  bottom: 0;\n  left: 50%;\n  position: absolute;\n  transform: translateX(-50%);\n}\n.tox .tox-tooltip--up .tox-tooltip__arrow {\n  border-bottom: 8px solid #222f3e;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  left: 50%;\n  position: absolute;\n  top: 0;\n  transform: translateX(-50%);\n}\n.tox .tox-tooltip--right .tox-tooltip__arrow {\n  border-bottom: 8px solid transparent;\n  border-left: 8px solid #222f3e;\n  border-top: 8px solid transparent;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.tox .tox-tooltip--left .tox-tooltip__arrow {\n  border-bottom: 8px solid transparent;\n  border-right: 8px solid #222f3e;\n  border-top: 8px solid transparent;\n  left: 0;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.tox .tox-well {\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  padding: 8px;\n  width: 100%;\n}\n.tox .tox-well > *:first-child {\n  margin-top: 0;\n}\n.tox .tox-well > *:last-child {\n  margin-bottom: 0;\n}\n.tox .tox-well > *:only-child {\n  margin: 0;\n}\n.tox .tox-custom-editor {\n  border: 1px solid #cccccc;\n  border-radius: 3px;\n  display: flex;\n  flex: 1;\n  position: relative;\n}\n/* stylelint-disable */\n.tox {\n  /* stylelint-enable */\n}\n.tox .tox-dialog-loading::before {\n  background-color: rgba(0, 0, 0, 0.5);\n  content: \"\";\n  height: 100%;\n  position: absolute;\n  width: 100%;\n  z-index: 1000;\n}\n.tox .tox-tab {\n  cursor: pointer;\n}\n.tox .tox-dialog__content-js {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-dialog__body-content .tox-collection {\n  display: flex;\n  flex: 1;\n  -ms-flex-preferred-size: auto;\n}\n.tox .tox-image-tools-edit-panel {\n  height: 60px;\n}\n.tox .tox-image-tools__sidebar {\n  height: 60px;\n}\n"]}