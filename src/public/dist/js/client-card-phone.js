$('.clientPhoneForm').on('submit', function (event) {
    event.preventDefault();
    let url = updatePhoneUrl;
    let successMsg = successSubmitTranslateClientCard;
    let errorMsg = errorSubmitTranslateClientCard;

    $.ajax({
        url: url,
        data: $(this).serialize(),
        type: 'POST',
        success: function (data) {
            if (data.error) {
                errorContainer.show('slow');
                errorContainer.html(' <div id="cstm-danger1"' +
                    ' class="alert alert-warning bg-danger' +
                    ' text-white alert-dismissible fade show" ' +
                    'role="alert"><button type="button" class="close" ' +
                    'data-dismiss="alert" aria-label="Close"> ' +
                    ' <span aria-hidden="true">×</span>' +
                    ' </button>' + errorMsg + '</div>')
                return;
            }
            $('.phoneModal').modal('hide');
            $("#cstm-success-client-card").show('slow');
            $("#cstm-success-client-card").html('<div id="cstm-success1" ' +
                'class="alert alert-success alert-dismissible bg-success text-white border-0 fade show" ' +
                ' role="alert"> <button type="button" class="close" data-dismiss="alert" ' +
                'aria-label="Close"> <span aria-hidden="true">×</span>' +
                ' </button>' + successMsg + '</div>')
            setInterval(function () {
                location.reload();
            }, 1000);
        }
    });
});

$('.addClientPhoneForm').on('submit', function (event) {
    event.preventDefault();
    const url         = addPhoneUrl;
    const errorMsg    = errorSubmitTranslateClientCard;
    const successMsg  = successSubmitTranslateClientCard;

    $.ajax({
        url: url,
        data: $(this).serialize(),
        type: 'POST',
        success: function (data) {
            if (data.error) {
                errorContainer.show('slow');
                errorContainer.html(' <div id="cstm-danger1"' +
                    ' class="alert alert-warning bg-danger' +
                    ' text-white alert-dismissible fade show" ' +
                    'role="alert"><button type="button" class="close" ' +
                    'data-dismiss="alert" aria-label="Close"> ' +
                    ' <span aria-hidden="true">×</span>' +
                    ' </button>' + errorMsg + '</div>')
                return;
            }
            $('#addPhoneModal').modal('hide');
            $("#cstm-success-client-card").show('slow');
            $("#cstm-success-client-card").html('<div id="cstm-success1" ' +
                'class="alert alert-success alert-dismissible bg-success text-white border-0 fade show" ' +
                ' role="alert"> <button type="button" class="close" data-dismiss="alert" ' +
                'aria-label="Close"> <span aria-hidden="true">×</span>' +
                ' </button>' + successMsg + '</div>')
            setInterval(function () {
                location.reload();
            }, 1000);
        }
    });
});
